#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
联网搜索系统测试脚本
测试所有搜索功能模块
"""

import sys
import time
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from core.web_search_module import WebSearchModule
from core.smart_search_assistant import SmartSearchAssistant
from search_interface import SearchInterface


def test_web_search_module():
    """测试基础搜索模块"""
    print("🧪 测试基础搜索模块")
    print("-" * 50)
    
    try:
        web_search = WebSearchModule()
        
        # 测试API状态
        print("1️⃣ 测试API状态...")
        api_status = web_search.get_api_status()
        for engine, status in api_status.items():
            enabled = "✅" if status['enabled'] else "❌"
            print(f"   {engine}: {enabled} (优先级: {status['priority']})")
        
        # 测试基础搜索
        print("\n2️⃣ 测试基础搜索...")
        test_query = "人工智能"
        results = web_search.search(test_query, num_results=3)
        
        if results:
            print(f"   ✅ 搜索成功，返回 {len(results)} 个结果")
            for i, result in enumerate(results[:2], 1):
                title = result.get('title', '无标题')[:40]
                source = result.get('source', '未知')
                print(f"   {i}. {title} (来源: {source})")
        else:
            print("   ❌ 搜索失败或无结果")
        
        # 测试多引擎搜索
        print("\n3️⃣ 测试多引擎搜索...")
        multi_results = web_search.multi_search("机器学习", num_results_per_engine=2)
        
        if multi_results:
            print(f"   ✅ 多引擎搜索成功，合并后 {len(multi_results)} 个结果")
            sources = set(r.get('source', '') for r in multi_results)
            print(f"   📊 涉及搜索引擎: {', '.join(sources)}")
        else:
            print("   ❌ 多引擎搜索失败")
        
        return True
        
    except Exception as e:
        print(f"   ❌ 基础搜索模块测试失败: {str(e)}")
        return False


def test_smart_search_assistant():
    """测试智能搜索助手"""
    print("\n🧪 测试智能搜索助手")
    print("-" * 50)
    
    try:
        smart_assistant = SmartSearchAssistant()
        
        # 测试智能搜索
        print("1️⃣ 测试智能搜索...")
        test_query = "人工智能最新发展"
        result = smart_assistant.intelligent_search(test_query)
        
        if result and result.get('analysis'):
            analysis = result['analysis']
            print(f"   ✅ 智能搜索成功")
            print(f"   搜索类型: {result['search_type']}")
            print(f"   质量分数: {analysis['quality_score']:.1f}/100")
            print(f"   相关性分数: {analysis['relevance_score']:.1f}/100")
            print(f"   高质量结果: {len(analysis['filtered_results'])} 个")
        else:
            print("   ❌ 智能搜索失败")
        
        # 测试内容增强
        print("\n2️⃣ 测试内容增强...")
        test_content = "人工智能是一门新兴技术。"
        test_topic = "人工智能技术报告"
        
        enhancement = smart_assistant.enhance_content_with_search(test_content, test_topic)
        
        if enhancement['status'] == 'enhanced':
            gaps = enhancement['gaps']
            print(f"   ✅ 内容增强成功，发现 {len(gaps)} 个缺口")
            for gap in gaps[:2]:
                print(f"   缺口: {gap['type']} (优先级: {gap['priority']})")
        elif enhancement['status'] == 'no_gaps':
            print("   ✅ 内容已完整，无需增强")
        else:
            print("   ❌ 内容增强失败")
        
        # 测试便捷搜索
        print("\n3️⃣ 测试便捷搜索...")
        latest_result = smart_assistant.search_latest_info("深度学习")
        
        if latest_result and latest_result.get('analysis'):
            filtered_count = len(latest_result['analysis']['filtered_results'])
            print(f"   ✅ 最新信息搜索成功，{filtered_count} 个结果")
        else:
            print("   ❌ 最新信息搜索失败")
        
        return True
        
    except Exception as e:
        print(f"   ❌ 智能搜索助手测试失败: {str(e)}")
        return False


def test_search_interface():
    """测试搜索接口"""
    print("\n🧪 测试搜索接口")
    print("-" * 50)
    
    try:
        search_interface = SearchInterface()
        
        # 测试基础搜索
        print("1️⃣ 测试基础搜索接口...")
        results = search_interface.search("自然语言处理", num_results=3)
        
        if results:
            print(f"   ✅ 基础搜索成功，{len(results)} 个结果")
        else:
            print("   ❌ 基础搜索失败")
        
        # 测试智能搜索
        print("\n2️⃣ 测试智能搜索接口...")
        smart_result = search_interface.smart_search("计算机视觉技术发展")
        
        if smart_result and smart_result.get('analysis'):
            analysis = smart_result['analysis']
            print(f"   ✅ 智能搜索成功")
            print(f"   搜索类型: {smart_result['search_type']}")
            print(f"   高质量结果: {len(analysis['filtered_results'])} 个")
        else:
            print("   ❌ 智能搜索失败")
        
        # 测试便捷搜索方法
        print("\n3️⃣ 测试便捷搜索方法...")
        
        # 测试搜索最新信息
        latest = search_interface.search_latest("区块链")
        if latest and latest.get('analysis'):
            print(f"   ✅ 最新信息搜索: {len(latest['analysis']['filtered_results'])} 个结果")
        else:
            print("   ❌ 最新信息搜索失败")
        
        # 测试搜索市场信息
        market = search_interface.search_market("电动汽车")
        if market and market.get('analysis'):
            print(f"   ✅ 市场信息搜索: {len(market['analysis']['filtered_results'])} 个结果")
        else:
            print("   ❌ 市场信息搜索失败")
        
        return True
        
    except Exception as e:
        print(f"   ❌ 搜索接口测试失败: {str(e)}")
        return False


def test_api_connectivity():
    """测试API连通性"""
    print("\n🧪 测试API连通性")
    print("-" * 50)
    
    try:
        web_search = WebSearchModule()
        
        print("🔍 测试所有搜索API...")
        api_results = web_search.test_all_apis()
        
        working_count = 0
        for engine, status in api_results.items():
            status_icon = "✅" if status else "❌"
            status_text = "正常" if status else "异常"
            print(f"   {engine}: {status_icon} {status_text}")
            if status:
                working_count += 1
        
        print(f"\n📊 API连通性总结:")
        print(f"   可用API: {working_count}/{len(api_results)}")
        
        if working_count > 0:
            print(f"   ✅ 搜索系统可正常使用")
            return True
        else:
            print(f"   ❌ 所有API都不可用，请检查配置")
            return False
        
    except Exception as e:
        print(f"   ❌ API连通性测试失败: {str(e)}")
        return False


def test_search_performance():
    """测试搜索性能"""
    print("\n🧪 测试搜索性能")
    print("-" * 50)
    
    try:
        search_interface = SearchInterface()
        
        test_queries = ["人工智能", "机器学习", "深度学习"]
        
        print(f"🚀 性能测试：{len(test_queries)} 个查询")
        
        start_time = time.time()
        
        for i, query in enumerate(test_queries, 1):
            print(f"   🔍 查询 {i}: {query}")
            query_start = time.time()
            
            results = search_interface.search(query, num_results=5)
            
            query_duration = time.time() - query_start
            print(f"   ⏱️ 耗时: {query_duration:.2f}秒, 结果: {len(results)} 个")
            
            # 避免频率限制
            if i < len(test_queries):
                time.sleep(1)
        
        total_duration = time.time() - start_time
        avg_duration = total_duration / len(test_queries)
        
        print(f"\n📊 性能测试结果:")
        print(f"   总耗时: {total_duration:.2f}秒")
        print(f"   平均耗时: {avg_duration:.2f}秒/查询")
        
        if avg_duration < 10:
            print(f"   ✅ 性能良好")
            return True
        else:
            print(f"   ⚠️ 性能较慢，可能需要优化")
            return True
        
    except Exception as e:
        print(f"   ❌ 性能测试失败: {str(e)}")
        return False


def test_error_handling():
    """测试错误处理"""
    print("\n🧪 测试错误处理")
    print("-" * 50)
    
    try:
        search_interface = SearchInterface()
        
        # 测试空查询
        print("1️⃣ 测试空查询...")
        empty_results = search_interface.search("")
        if not empty_results:
            print("   ✅ 空查询处理正确")
        else:
            print("   ❌ 空查询处理异常")
        
        # 测试无效查询
        print("\n2️⃣ 测试特殊字符查询...")
        special_results = search_interface.search("@#$%^&*()")
        print(f"   ✅ 特殊字符查询处理完成，结果: {len(special_results)} 个")
        
        # 测试长查询
        print("\n3️⃣ 测试长查询...")
        long_query = "这是一个非常长的查询字符串" * 10
        long_results = search_interface.search(long_query[:100])  # 截断到合理长度
        print(f"   ✅ 长查询处理完成，结果: {len(long_results)} 个")
        
        return True
        
    except Exception as e:
        print(f"   ❌ 错误处理测试失败: {str(e)}")
        return False


def main():
    """主测试函数"""
    print("🔍 联网搜索系统全面测试")
    print("=" * 60)
    
    test_results = []
    
    # 测试API连通性（最重要）
    api_test = test_api_connectivity()
    test_results.append(("API连通性", api_test))
    
    if not api_test:
        print("\n❌ API连通性测试失败，跳过其他测试")
        print("💡 请检查以下配置:")
        print("   • METASO_API_KEY 环境变量")
        print("   • GOOGLE_SEARCH_API_KEY 和 GOOGLE_SEARCH_CX 环境变量")
        print("   • BING_SEARCH_API_KEY 环境变量")
        return
    
    # 测试基础模块
    web_test = test_web_search_module()
    test_results.append(("基础搜索模块", web_test))
    
    # 测试智能助手
    smart_test = test_smart_search_assistant()
    test_results.append(("智能搜索助手", smart_test))
    
    # 测试搜索接口
    interface_test = test_search_interface()
    test_results.append(("搜索接口", interface_test))
    
    # 测试性能
    performance_test = test_search_performance()
    test_results.append(("搜索性能", performance_test))
    
    # 测试错误处理
    error_test = test_error_handling()
    test_results.append(("错误处理", error_test))
    
    # 输出测试总结
    print("\n📊 测试总结")
    print("=" * 60)
    
    passed_tests = 0
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed_tests += 1
    
    print(f"\n📈 测试结果: {passed_tests}/{len(test_results)} 通过")
    
    if passed_tests == len(test_results):
        print("\n🎉 所有测试通过！联网搜索系统运行正常")
        print("\n💡 使用方法:")
        print("```python")
        print("from search_interface import search, smart_search")
        print("")
        print("# 基础搜索")
        print("results = search('人工智能')")
        print("")
        print("# 智能搜索")
        print("smart_results = smart_search('AI最新发展')")
        print("```")
        print("\n或者运行:")
        print("python search_interface.py")
    elif passed_tests >= len(test_results) * 0.7:
        print("\n⚠️ 大部分测试通过，系统基本可用")
        print("建议检查失败的测试项目")
    else:
        print("\n❌ 多个测试失败，建议检查配置和网络连接")


if __name__ == "__main__":
    main()
