#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
修复验证测试脚本
验证 asyncio 作用域问题是否已修复
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_generator_import():
    """测试生成器导入"""
    print("🧪 测试生成器导入...")
    try:
        from core.generator import CompleteReportGenerator
        print("✅ 生成器导入成功")
        return True
    except Exception as e:
        print(f"❌ 生成器导入失败: {str(e)}")
        return False

def test_generator_initialization():
    """测试生成器初始化"""
    print("🧪 测试生成器初始化...")
    try:
        from core.generator import CompleteReportGenerator
        generator = CompleteReportGenerator()
        print("✅ 生成器初始化成功")
        return True
    except Exception as e:
        print(f"❌ 生成器初始化失败: {str(e)}")
        return False

def test_workflow_coordinator():
    """测试工作流协调器"""
    print("🧪 测试工作流协调器...")
    try:
        from core.generator import CompleteReportGenerator
        generator = CompleteReportGenerator()
        
        # 检查工作流协调器是否正确初始化
        has_coordinator = hasattr(generator, 'workflow_coordinator')
        print(f"✅ 工作流协调器: {'已初始化' if has_coordinator else '未初始化'}")
        
        if has_coordinator:
            # 检查工作流方法
            has_execute = hasattr(generator.workflow_coordinator, 'execute_complete_workflow')
            print(f"✅ 执行方法: {'已定义' if has_execute else '未定义'}")
            return has_execute
        
        return False
    except Exception as e:
        print(f"❌ 工作流协调器测试失败: {str(e)}")
        return False

def test_asyncio_scope():
    """测试 asyncio 作用域问题"""
    print("🧪 测试 asyncio 作用域...")
    try:
        from core.generator import CompleteReportGenerator
        generator = CompleteReportGenerator()
        
        # 设置测试配置
        generator.report_config.update({
            "use_workflow_coordinator": True,
            "primary_sections": 2,
            "max_depth": 2
        })
        
        # 测试配置是否正确设置
        use_coordinator = generator.report_config.get("use_workflow_coordinator", False)
        print(f"✅ 工作流协调器配置: {'启用' if use_coordinator else '禁用'}")
        
        return True
    except Exception as e:
        print(f"❌ asyncio 作用域测试失败: {str(e)}")
        return False

def test_search_interface():
    """测试搜索接口"""
    print("🧪 测试搜索接口...")
    try:
        from search_interface import SearchInterface
        search_interface = SearchInterface()
        print("✅ 搜索接口初始化成功")
        return True
    except Exception as e:
        print(f"❌ 搜索接口测试失败: {str(e)}")
        return False

def main():
    """主测试函数"""
    print("🔧 修复验证测试")
    print("=" * 50)
    
    tests = [
        ("生成器导入", test_generator_import),
        ("生成器初始化", test_generator_initialization),
        ("工作流协调器", test_workflow_coordinator),
        ("asyncio作用域", test_asyncio_scope),
        ("搜索接口", test_search_interface)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n📋 {test_name}测试:")
        print("-" * 30)
        result = test_func()
        results.append((test_name, result))
    
    # 输出测试总结
    print("\n📊 测试总结:")
    print("=" * 50)
    
    passed_tests = 0
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
        if result:
            passed_tests += 1
    
    print(f"\n📈 测试结果: {passed_tests}/{len(results)} 通过")
    
    if passed_tests == len(results):
        print("\n🎉 所有测试通过！修复成功")
        print("💡 现在可以正常运行 main.py 了")
    elif passed_tests >= len(results) * 0.8:
        print("\n⚠️ 大部分测试通过，基本修复成功")
        print("建议检查失败的测试项目")
    else:
        print("\n❌ 多个测试失败，需要进一步修复")
    
    return passed_tests == len(results)

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
