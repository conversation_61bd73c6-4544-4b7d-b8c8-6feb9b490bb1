"""
完整的搜索增强功能演示
展示基于Gemini工具调用和Metaso搜索的完整流程
"""
import os
import sys
import time
from pathlib import Path
from complete_report_generator import CompleteReportGenerator

def create_demo_report():
    """创建演示报告"""
    print("📄 创建演示报告...")
    
    demo_dir = Path("demo_complete_search_data")
    if demo_dir.exists():
        import shutil
        shutil.rmtree(demo_dir)
    demo_dir.mkdir()
    
    # 创建一个明显需要最新信息的报告
    demo_content = """# 新能源汽车产业发展报告

## 1. 产业概述

新能源汽车是指采用非常规的车用燃料作为动力来源的汽车，包括纯电动汽车、混合动力汽车、燃料电池汽车等。

### 1.1 技术分类

- **纯电动汽车(BEV)**: 完全依靠电池驱动
- **插电式混合动力(PHEV)**: 结合电池和燃油发动机
- **燃料电池汽车(FCEV)**: 使用氢燃料电池

### 1.2 发展历程

新能源汽车的发展经历了起步、快速发展等阶段。

## 2. 技术发展

### 2.1 电池技术

锂离子电池是目前主流的动力电池技术。

### 2.2 充电技术

充电基础设施的建设是新能源汽车发展的关键。

## 3. 市场现状

### 3.1 全球市场

全球新能源汽车市场呈现快速增长态势。

### 3.2 中国市场

中国是全球最大的新能源汽车市场。

## 4. 发展前景

### 4.1 技术趋势

电池技术、自动驾驶等技术将继续发展。

### 4.2 市场预测

预计未来几年新能源汽车市场将持续增长。

---

*注：本报告基于公开资料整理，可能缺少2024年最新的销量数据、政策变化和技术突破信息。*
"""
    
    demo_report_path = demo_dir / "new_energy_vehicle_report.md"
    with open(demo_report_path, 'w', encoding='utf-8') as f:
        f.write(demo_content)
    
    print(f"✅ 演示报告已创建: {demo_report_path}")
    return demo_report_path

def demo_complete_search_enhancement():
    """演示完整的搜索增强功能"""
    print("🚀 完整搜索增强功能演示")
    print("=" * 60)
    
    # 设置API Key
    os.environ['METASO_API_KEY'] = 'mk-988A8E4DC50C53312E3D1A8729687F4C'
    
    # 创建演示报告
    demo_report_path = create_demo_report()
    
    # 初始化生成器
    generator = CompleteReportGenerator(use_async=False)
    
    print(f"\n📖 演示报告信息:")
    print(f"   主题: 新能源汽车产业发展")
    print(f"   文件: {demo_report_path}")
    print(f"   特点: 缺少2024年最新数据、政策和技术信息")
    
    print(f"\n🔍 搜索增强功能特点:")
    print(f"   • 🤖 Gemini智能分析报告内容")
    print(f"   • 🔧 自动决定搜索策略和查询内容")
    print(f"   • 🌐 调用Metaso API进行网页搜索")
    print(f"   • 📚 调用Metaso API进行学术论文搜索")
    print(f"   • 🧠 智能整合搜索结果到报告中")
    print(f"   • 👤 用户友好的确认界面")
    
    # 演示用户交互
    print(f"\n" + "="*60)
    print(f"🌐 系统检测到报告可以通过搜索获得增强")
    print(f"📋 Gemini分析发现以下信息缺口:")
    print(f"   1. 2024年最新销量数据和市场动态")
    print(f"   2. 最新政策法规和补贴政策")
    print(f"   3. 电池技术和充电技术的最新突破")
    print(f"   4. 主要厂商的竞争格局变化")
    print(f"   5. 前沿学术研究和技术发展")
    
    print(f"\n🔍 准备执行以下搜索任务:")
    print(f"   • 网页搜索: 2024年新能源汽车销量数据市场分析")
    print(f"   • 网页搜索: 新能源汽车政策补贴最新变化2024")
    print(f"   • 网页搜索: 电池技术充电技术突破2024")
    print(f"   • 学术搜索: electric vehicle battery technology 2024")
    
    print(f"\n🌐 是否进行联网搜索以补充这些信息？")
    print(f"   • 将调用Metaso搜索API获取最新信息")
    print(f"   • 搜索结果将经过质量验证")
    print(f"   • 补充内容将智能整合到报告中")
    
    # 获取用户输入
    while True:
        user_input = input(f"\n请选择 [y/n]: ").strip().lower()
        if user_input in ['y', 'yes', '是']:
            print(f"\n✅ 用户确认进行搜索增强")
            break
        elif user_input in ['n', 'no', '否']:
            print(f"\n❌ 用户取消搜索增强")
            cleanup_demo_data()
            return
        else:
            print("请输入 y 或 n")
    
    # 执行搜索增强
    try:
        print(f"\n🚀 开始执行搜索增强...")
        
        enhanced_path = generator.enhance_report_with_tool_calling(
            str(demo_report_path),
            "新能源汽车产业发展",
            user_confirm=False  # 已经确认过了
        )
        
        if enhanced_path != str(demo_report_path):
            print(f"\n✅ 搜索增强成功完成！")
            print(f"   原始报告: {demo_report_path}")
            print(f"   增强报告: {enhanced_path}")
            
            # 显示增强效果
            show_enhancement_results(demo_report_path, enhanced_path)
            
        else:
            print(f"\n⚠️ 搜索增强未执行")
            print(f"   可能原因: API配置问题或Gemini认为报告已足够完整")
    
    except Exception as e:
        print(f"\n❌ 搜索增强过程出错: {str(e)}")
        print(f"   这可能是由于网络连接或API配置问题")
    
    # 清理演示数据
    cleanup_demo_data()

def show_enhancement_results(original_path, enhanced_path):
    """显示增强效果"""
    print(f"\n📊 增强效果分析:")
    
    try:
        # 读取原始内容
        with open(original_path, 'r', encoding='utf-8') as f:
            original_content = f.read()
        
        # 读取增强内容
        with open(enhanced_path, 'r', encoding='utf-8') as f:
            enhanced_content = f.read()
        
        print(f"   📏 原始报告长度: {len(original_content)} 字符")
        print(f"   📏 增强报告长度: {len(enhanced_content)} 字符")
        print(f"   📈 增加内容: {len(enhanced_content) - len(original_content)} 字符")
        print(f"   📊 增长比例: {((len(enhanced_content) - len(original_content)) / len(original_content) * 100):.1f}%")
        
        # 检查补充信息
        if "最新信息补充" in enhanced_content:
            print(f"   ✅ 包含最新信息补充部分")
            
            # 统计补充部分
            supplement_start = enhanced_content.find("## 最新信息补充")
            if supplement_start != -1:
                supplement_content = enhanced_content[supplement_start:]
                supplement_sections = supplement_content.count("###")
                print(f"   📋 补充信息包含 {supplement_sections} 个部分")
        
        # 显示增强内容预览
        print(f"\n📝 增强内容预览 (最后200字符):")
        preview = enhanced_content[-200:].strip()
        print(f"   ...{preview}")
    
    except Exception as e:
        print(f"   ⚠️ 无法分析增强效果: {str(e)}")

def cleanup_demo_data():
    """清理演示数据"""
    try:
        demo_dir = Path("demo_complete_search_data")
        if demo_dir.exists():
            import shutil
            shutil.rmtree(demo_dir)
            print(f"\n🧹 演示数据已清理")
    except Exception as e:
        print(f"\n⚠️ 清理演示数据失败: {str(e)}")

def show_feature_summary():
    """显示功能总结"""
    print(f"\n🎯 搜索增强功能总结")
    print("=" * 60)
    
    print(f"✨ 核心特性:")
    print(f"   1. 🤖 AI智能分析 - Gemini自动识别信息缺口")
    print(f"   2. 🔧 动态工具调用 - 根据需要选择搜索策略")
    print(f"   3. 🌐 高质量搜索 - Metaso提供中文优化搜索")
    print(f"   4. 📚 学术搜索 - 获取前沿研究和技术发展")
    print(f"   5. 🧠 智能整合 - AI自动整合搜索结果")
    print(f"   6. 👤 用户控制 - 友好的确认和选择机制")
    
    print(f"\n🔍 搜索能力:")
    print(f"   • 最新数据和发展动态")
    print(f"   • 市场分析和竞争格局")
    print(f"   • 技术发展和创新突破")
    print(f"   • 政策法规和行业标准")
    print(f"   • 实际案例和应用实践")
    print(f"   • 前沿学术研究成果")
    
    print(f"\n⚙️ 技术架构:")
    print(f"   • Gemini 2.5 Pro/Flash - AI分析和整合")
    print(f"   • Metaso Search API - 网页和学术搜索")
    print(f"   • 工具调用框架 - 动态搜索决策")
    print(f"   • 质量控制系统 - 结果验证和筛选")
    
    print(f"\n🚀 使用方法:")
    print(f"   1. 生成基础报告")
    print(f"   2. 系统自动分析内容缺口")
    print(f"   3. 用户确认是否进行搜索 (y/n)")
    print(f"   4. AI执行智能搜索和整合")
    print(f"   5. 获得增强版报告")

def main():
    """主演示函数"""
    print("🎯 AI报告生成器 - 完整搜索增强功能演示")
    print("=" * 60)
    
    print("本演示将展示基于Gemini工具调用的智能搜索增强功能:")
    print("   • AI自动分析报告内容")
    print("   • 智能识别信息缺口")
    print("   • 动态调用搜索工具")
    print("   • 用户友好的确认界面 (y/n选择)")
    print("   • 智能整合搜索结果")
    
    try:
        # 显示功能总结
        show_feature_summary()
        
        # 执行完整演示
        demo_complete_search_enhancement()
        
        print(f"\n🎉 演示完成!")
        print(f"\n💡 要在实际项目中使用此功能:")
        print(f"   1. 确保配置了Metaso API Key")
        print(f"   2. 在报告生成时启用搜索增强")
        print(f"   3. 根据提示选择是否进行搜索 (y/n)")
        print(f"   4. 等待AI完成智能搜索和整合")
        
    except KeyboardInterrupt:
        print(f"\n\n❌ 用户中断演示")
        cleanup_demo_data()
    except Exception as e:
        print(f"\n❌ 演示过程出错: {str(e)}")
        cleanup_demo_data()

if __name__ == "__main__":
    main()
