"""
测试缓存机制
验证文件读取缓存功能是否正常工作
"""
import os
import sys
import time
from pathlib import Path
import shutil

def create_test_data():
    """创建测试数据"""
    print("📁 创建测试数据...")
    
    test_dir = Path("test_cache_data")
    if test_dir.exists():
        shutil.rmtree(test_dir)
    test_dir.mkdir()
    
    # 创建一些测试文件
    files_created = []
    
    # 1. 文本文件
    with open(test_dir / "test1.txt", "w", encoding="utf-8") as f:
        f.write("这是第一个测试文本文件\n包含一些中文内容\n用于测试缓存机制")
    files_created.append("test1.txt")
    
    # 2. Markdown文件
    with open(test_dir / "test2.md", "w", encoding="utf-8") as f:
        f.write("# 测试Markdown\n\n这是一个**测试**文件，用于验证缓存功能。\n\n- 项目1\n- 项目2\n- 项目3")
    files_created.append("test2.md")
    
    # 3. JSO<PERSON>文件
    import json
    test_data = {
        "name": "缓存测试",
        "version": "1.0",
        "features": ["文件读取", "缓存机制", "性能优化"],
        "config": {
            "cache_enabled": True,
            "max_cache_size": "100MB"
        }
    }
    with open(test_dir / "test3.json", "w", encoding="utf-8") as f:
        json.dump(test_data, f, ensure_ascii=False, indent=2)
    files_created.append("test3.json")
    
    # 4. CSV文件
    with open(test_dir / "test4.csv", "w", encoding="utf-8") as f:
        f.write("文件名,类型,大小,状态\n")
        f.write("test1.txt,文本,1KB,正常\n")
        f.write("test2.md,Markdown,2KB,正常\n")
        f.write("test3.json,JSON,1KB,正常\n")
    files_created.append("test4.csv")
    
    print(f"✅ 创建了 {len(files_created)} 个测试文件: {', '.join(files_created)}")
    return test_dir, files_created

def test_cache_functionality():
    """测试缓存功能"""
    print("🧪 测试缓存机制")
    print("=" * 60)
    
    try:
        from complete_report_generator import CompleteReportGenerator
        
        # 创建测试数据
        test_dir, files_created = create_test_data()
        
        # 初始化生成器
        generator = CompleteReportGenerator(use_async=False)
        
        print(f"\n🔄 第一次读取（应该处理文件并创建缓存）")
        start_time = time.time()
        
        content1 = generator.read_data_source(str(test_dir))
        
        first_read_time = time.time() - start_time
        print(f"⏱️ 第一次读取耗时: {first_read_time:.2f} 秒")
        
        # 检查是否创建了缓存
        processed_dir = test_dir / "processed"
        if processed_dir.exists():
            print(f"✅ 缓存目录已创建: {processed_dir}")
            
            cache_files = list(processed_dir.glob("*"))
            print(f"📄 缓存文件: {[f.name for f in cache_files]}")
            
            # 检查具体的缓存文件
            merged_file = processed_dir / "merged_content.txt"
            info_file = processed_dir / "processing_info.json"
            
            if merged_file.exists():
                print(f"✅ 合并内容文件存在: {merged_file.stat().st_size} 字节")
            else:
                print(f"❌ 合并内容文件不存在")
                
            if info_file.exists():
                print(f"✅ 处理信息文件存在: {info_file.stat().st_size} 字节")
                
                # 读取处理信息
                import json
                with open(info_file, 'r', encoding='utf-8') as f:
                    info = json.load(f)
                print(f"📊 处理信息: {info.get('total_files', 0)} 个文件, {info.get('total_characters', 0)} 字符")
            else:
                print(f"❌ 处理信息文件不存在")
        else:
            print(f"❌ 缓存目录未创建")
        
        print(f"\n🔄 第二次读取（应该使用缓存）")
        start_time = time.time()
        
        content2 = generator.read_data_source(str(test_dir))
        
        second_read_time = time.time() - start_time
        print(f"⏱️ 第二次读取耗时: {second_read_time:.2f} 秒")
        
        # 比较两次读取的结果
        if content1 == content2:
            print(f"✅ 两次读取内容一致")
        else:
            print(f"❌ 两次读取内容不一致")
            print(f"   第一次长度: {len(content1)}")
            print(f"   第二次长度: {len(content2)}")
        
        # 性能比较
        if second_read_time < first_read_time:
            speedup = first_read_time / second_read_time
            print(f"🚀 缓存加速: {speedup:.1f}x 倍")
        else:
            print(f"⚠️ 缓存未能提升性能")
        
        # 测试详细读取缓存
        print(f"\n🔄 测试详细读取缓存")
        start_time = time.time()
        
        data_files1, content_detailed1, image_files1 = generator._read_data_source_detailed(str(test_dir))
        
        detailed_first_time = time.time() - start_time
        print(f"⏱️ 第一次详细读取耗时: {detailed_first_time:.2f} 秒")
        print(f"📄 数据文件: {len(data_files1)} 个")
        print(f"🖼️ 图片文件: {len(image_files1)} 个")
        
        start_time = time.time()
        
        data_files2, content_detailed2, image_files2 = generator._read_data_source_detailed(str(test_dir))
        
        detailed_second_time = time.time() - start_time
        print(f"⏱️ 第二次详细读取耗时: {detailed_second_time:.2f} 秒")
        
        # 验证详细读取结果
        if (data_files1 == data_files2 and 
            content_detailed1 == content_detailed2 and 
            image_files1 == image_files2):
            print(f"✅ 详细读取缓存工作正常")
        else:
            print(f"❌ 详细读取缓存有问题")
        
        # 显示内容摘要
        print(f"\n📝 内容摘要:")
        if content1:
            lines = content1.split('\n')[:10]
            for line in lines:
                if line.strip():
                    print(f"   {line}")
            if len(content1.split('\n')) > 10:
                print(f"   ... (还有 {len(content1.split('\n')) - 10} 行)")
        
        # 清理测试数据
        print(f"\n🧹 清理测试数据...")
        if test_dir.exists():
            shutil.rmtree(test_dir)
            print(f"✅ 测试数据清理完成")
        
        # 总结
        print(f"\n📊 测试总结:")
        print(f"   ✅ 缓存机制: {'正常' if processed_dir.existed else '异常'}")
        print(f"   ✅ 内容一致性: {'正常' if content1 == content2 else '异常'}")
        print(f"   ✅ 性能提升: {'是' if second_read_time < first_read_time else '否'}")
        print(f"   ✅ 详细缓存: {'正常' if data_files1 == data_files2 else '异常'}")
        
        return True
        
    except ImportError as e:
        print(f"❌ 导入失败: {str(e)}")
        return False
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        return False

def test_cache_invalidation():
    """测试缓存失效机制"""
    print(f"\n🔄 测试缓存失效机制")
    print("=" * 60)
    
    try:
        from complete_report_generator import CompleteReportGenerator
        
        # 创建测试数据
        test_dir, _ = create_test_data()
        generator = CompleteReportGenerator(use_async=False)
        
        # 第一次读取，创建缓存
        print(f"📖 第一次读取，创建缓存...")
        content1 = generator.read_data_source(str(test_dir))
        
        # 修改一个文件
        print(f"✏️ 修改测试文件...")
        with open(test_dir / "test1.txt", "a", encoding="utf-8") as f:
            f.write("\n\n这是新增的内容，用于测试缓存失效")
        
        # 再次读取（应该检测到文件变化）
        print(f"📖 再次读取...")
        content2 = generator.read_data_source(str(test_dir))
        
        if len(content2) > len(content1):
            print(f"✅ 缓存正确更新，检测到文件变化")
            print(f"   原内容长度: {len(content1)}")
            print(f"   新内容长度: {len(content2)}")
        else:
            print(f"⚠️ 缓存可能未正确更新")
        
        # 清理
        if test_dir.exists():
            shutil.rmtree(test_dir)
        
        return True
        
    except Exception as e:
        print(f"❌ 缓存失效测试失败: {str(e)}")
        return False

if __name__ == "__main__":
    print("🚀 AI报告生成器 - 缓存机制测试")
    print("=" * 60)
    
    # 测试基本缓存功能
    cache_test_passed = test_cache_functionality()
    
    # 测试缓存失效
    # invalidation_test_passed = test_cache_invalidation()
    
    if cache_test_passed:
        print(f"\n🎉 缓存机制测试通过！")
        print(f"\n💡 缓存机制的优势:")
        print(f"   1. 避免重复处理相同文件")
        print(f"   2. 显著提升读取速度")
        print(f"   3. 减少API调用次数（特别是PDF OCR）")
        print(f"   4. 支持断点续传和增量处理")
    else:
        print(f"\n💡 如果测试失败，请检查:")
        print(f"   1. 确保complete_report_generator.py文件存在")
        print(f"   2. 检查文件权限")
        print(f"   3. 确保有足够的磁盘空间")
    
    sys.exit(0 if cache_test_passed else 1)
