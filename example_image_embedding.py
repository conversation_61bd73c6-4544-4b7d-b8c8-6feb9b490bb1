"""
图片嵌入功能使用示例
演示如何使用基于Gemini智能匹配的图片嵌入功能
"""
from complete_report_generator import CompleteReportGenerator
from pathlib import Path

def example_basic_usage():
    """基础使用示例"""
    print("📖 基础使用示例")
    print("=" * 50)
    
    # 初始化生成器
    generator = CompleteReportGenerator(use_async=False)
    
    # 生成报告（自动包含图片嵌入）
    output_path = generator.generate_report_sync(
        topic="人工智能技术发展报告",
        data_sources=["data/ai_research", "data/market_analysis"],
        framework_file_path="frameworks/ai_report_framework.md"
    )
    
    print(f"✅ 报告生成完成（包含图片）: {output_path}")

def example_manual_image_embedding():
    """手动图片嵌入示例"""
    print("\n📖 手动图片嵌入示例")
    print("=" * 50)
    
    # 初始化生成器
    generator = CompleteReportGenerator(use_async=False)
    
    # 先生成基础报告（禁用自动图片嵌入）
    generator.report_config["enable_image_embedding"] = False
    
    basic_output = generator.generate_report_sync(
        topic="区块链技术分析",
        data_sources=["data/blockchain_docs"],
        framework_file_path="frameworks/blockchain_framework.md"
    )
    
    print(f"📄 基础报告生成完成: {basic_output}")
    
    # 手动执行图片嵌入
    enhanced_output = generator.embed_images_in_report(
        basic_output,
        ["data/blockchain_docs"],
        "区块链技术分析",
        auto_confirm=False  # 需要用户确认
    )
    
    print(f"🖼️ 增强报告生成完成: {enhanced_output}")

def example_auto_confirm_embedding():
    """自动确认图片嵌入示例"""
    print("\n📖 自动确认图片嵌入示例")
    print("=" * 50)
    
    generator = CompleteReportGenerator(use_async=False)
    
    # 生成基础报告
    generator.report_config["enable_image_embedding"] = False
    
    basic_output = generator.generate_report_sync(
        topic="新能源汽车市场分析",
        data_sources=["data/ev_market"],
        framework_file_path="frameworks/market_analysis.md"
    )
    
    # 自动确认高分匹配的图片
    enhanced_output = generator.embed_images_in_report(
        basic_output,
        ["data/ev_market"],
        "新能源汽车市场分析",
        auto_confirm=True  # 自动确认得分>0.7的匹配
    )
    
    print(f"🤖 自动图片嵌入完成: {enhanced_output}")

def example_preview_only():
    """仅预览图片匹配示例"""
    print("\n📖 仅预览图片匹配示例")
    print("=" * 50)
    
    generator = CompleteReportGenerator(use_async=False)
    
    # 假设已有报告文件
    existing_report = "reports/existing_report.md"
    
    if Path(existing_report).exists():
        # 收集图片数据
        all_image_data = generator.collect_all_image_data(["data/source1", "data/source2"])
        
        if all_image_data:
            # 读取报告内容
            report_content = generator.read_generated_report(existing_report)
            
            # 准备图片信息
            processor = generator.ImageInfoProcessor(generator)
            image_descriptions = processor.prepare_image_info_for_gemini(all_image_data)
            
            # 进行匹配分析
            matcher = generator.GeminiImageMatcher(generator)
            matches = matcher.analyze_image_matches(
                report_content, 
                image_descriptions,
                {'topic': '技术分析报告'}
            )
            
            # 生成预览报告
            preview_manager = generator.ImageMatchPreview(generator)
            preview_report = preview_manager.generate_preview_report(matches)
            
            # 保存预览
            preview_path = existing_report.replace('.md', '_image_preview.md')
            with open(preview_path, 'w', encoding='utf-8') as f:
                f.write(preview_report)
            
            print(f"📋 图片匹配预览已保存: {preview_path}")
        else:
            print("📷 未找到图片数据")
    else:
        print(f"❌ 报告文件不存在: {existing_report}")

def example_custom_configuration():
    """自定义配置示例"""
    print("\n📖 自定义配置示例")
    print("=" * 50)
    
    generator = CompleteReportGenerator(use_async=False)
    
    # 自定义报告配置
    generator.report_config.update({
        "enable_image_embedding": True,
        "image_embedding_auto_confirm": True,
        "image_embedding_score_threshold": 0.8,  # 只嵌入高分匹配
        "max_images_per_report": 5,  # 限制图片数量
        "image_caption_prefix": "图表",  # 自定义标题前缀
    })
    
    output_path = generator.generate_report_sync(
        topic="云计算技术趋势分析",
        data_sources=["data/cloud_computing"],
        framework_file_path="frameworks/tech_trend.md"
    )
    
    print(f"⚙️ 自定义配置报告生成完成: {output_path}")

def example_batch_processing():
    """批量处理示例"""
    print("\n📖 批量处理示例")
    print("=" * 50)
    
    generator = CompleteReportGenerator(use_async=False)
    
    # 批量处理多个报告
    reports_to_enhance = [
        ("reports/ai_report.md", ["data/ai_research"], "人工智能"),
        ("reports/blockchain_report.md", ["data/blockchain"], "区块链"),
        ("reports/iot_report.md", ["data/iot_data"], "物联网")
    ]
    
    enhanced_reports = []
    
    for report_path, data_sources, topic in reports_to_enhance:
        if Path(report_path).exists():
            print(f"🔄 处理报告: {report_path}")
            
            try:
                enhanced_path = generator.embed_images_in_report(
                    report_path,
                    data_sources,
                    topic,
                    auto_confirm=True
                )
                enhanced_reports.append(enhanced_path)
                print(f"   ✅ 完成: {enhanced_path}")
                
            except Exception as e:
                print(f"   ❌ 失败: {str(e)}")
                enhanced_reports.append(report_path)  # 保留原始报告
        else:
            print(f"   ⚠️ 文件不存在: {report_path}")
    
    print(f"\n📊 批量处理完成: {len(enhanced_reports)} 个报告")

def example_error_handling():
    """错误处理示例"""
    print("\n📖 错误处理示例")
    print("=" * 50)
    
    generator = CompleteReportGenerator(use_async=False)
    
    try:
        # 尝试处理不存在的报告
        result = generator.embed_images_in_report(
            "nonexistent_report.md",
            ["data/some_data"],
            "测试主题",
            auto_confirm=True
        )
        print(f"结果: {result}")
        
    except Exception as e:
        print(f"❌ 预期的错误: {str(e)}")
    
    try:
        # 尝试处理没有图片的数据源
        result = generator.embed_images_in_report(
            "reports/text_only_report.md",
            ["data/text_only"],
            "纯文本报告",
            auto_confirm=True
        )
        print(f"结果: {result}")
        
    except Exception as e:
        print(f"❌ 处理错误: {str(e)}")

def main():
    """主函数 - 运行所有示例"""
    print("🚀 图片嵌入功能使用示例")
    print("=" * 60)
    
    examples = [
        ("基础使用", example_basic_usage),
        ("手动图片嵌入", example_manual_image_embedding),
        ("自动确认嵌入", example_auto_confirm_embedding),
        ("仅预览匹配", example_preview_only),
        ("自定义配置", example_custom_configuration),
        ("批量处理", example_batch_processing),
        ("错误处理", example_error_handling)
    ]
    
    print("📋 可用示例:")
    for i, (name, _) in enumerate(examples, 1):
        print(f"   {i}. {name}")
    
    print("\n💡 使用说明:")
    print("   • 确保数据源目录包含图片文件")
    print("   • 图片会自动分析并建立索引")
    print("   • Gemini会智能匹配图片与文本内容")
    print("   • 支持用户确认或自动确认模式")
    print("   • 生成详细的匹配预览报告")
    
    print("\n🛠️ 配置选项:")
    print("   • enable_image_embedding: 启用/禁用图片嵌入")
    print("   • image_embedding_auto_confirm: 自动确认模式")
    print("   • image_embedding_score_threshold: 得分阈值")
    print("   • max_images_per_report: 最大图片数量")
    
    print("\n📁 文件结构:")
    print("   • 原始报告: report.md")
    print("   • 增强报告: report_with_images.md")
    print("   • 匹配预览: report_image_preview.md")
    
    # 运行一个简单的示例
    try:
        print("\n🎯 运行错误处理示例...")
        example_error_handling()
    except Exception as e:
        print(f"示例运行失败: {str(e)}")

if __name__ == "__main__":
    main()
