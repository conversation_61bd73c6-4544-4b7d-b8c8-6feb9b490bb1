"""
搜索增强功能使用示例
演示如何使用搜索API集成功能增强报告内容
"""
from complete_report_generator import CompleteReportGenerator
from pathlib import Path

def example_basic_search_enhancement():
    """基础搜索增强示例"""
    print("📖 基础搜索增强示例")
    print("=" * 50)
    
    # 初始化生成器
    generator = CompleteReportGenerator(use_async=False)
    
    # 生成报告（自动包含搜索增强）
    output_path = generator.generate_report_sync(
        topic="区块链技术发展趋势",
        data_sources=["data/blockchain_research"],
        framework_file_path="frameworks/tech_analysis.md"
    )
    
    print(f"✅ 报告生成完成（包含搜索增强）: {output_path}")

def example_manual_search_enhancement():
    """手动搜索增强示例"""
    print("\n📖 手动搜索增强示例")
    print("=" * 50)
    
    # 初始化生成器
    generator = CompleteReportGenerator(use_async=False)
    
    # 先生成基础报告（禁用自动搜索增强）
    generator.report_config["enable_search_enhancement"] = False
    
    basic_output = generator.generate_report_sync(
        topic="5G技术应用分析",
        data_sources=["data/5g_docs"],
        framework_file_path="frameworks/tech_application.md"
    )
    
    print(f"📄 基础报告生成完成: {basic_output}")
    
    # 手动执行搜索增强
    enhanced_output = generator.enhance_report_with_search(
        basic_output,
        "5G技术应用分析",
        user_confirm=True  # 需要用户确认
    )
    
    print(f"🔍 增强报告生成完成: {enhanced_output}")

def example_auto_search_enhancement():
    """自动搜索增强示例"""
    print("\n📖 自动搜索增强示例")
    print("=" * 50)
    
    generator = CompleteReportGenerator(use_async=False)
    
    # 生成基础报告
    generator.report_config["enable_search_enhancement"] = False
    
    basic_output = generator.generate_report_sync(
        topic="新能源汽车市场分析",
        data_sources=["data/ev_market"],
        framework_file_path="frameworks/market_analysis.md"
    )
    
    # 自动搜索增强（不需要用户确认）
    enhanced_output = generator.enhance_report_with_search(
        basic_output,
        "新能源汽车市场分析",
        user_confirm=False  # 自动执行
    )
    
    print(f"🤖 自动搜索增强完成: {enhanced_output}")

def example_custom_search_configuration():
    """自定义搜索配置示例"""
    print("\n📖 自定义搜索配置示例")
    print("=" * 50)
    
    generator = CompleteReportGenerator(use_async=False)
    
    # 自定义搜索配置
    generator.report_config.update({
        "enable_search_enhancement": True,
        "search_auto_confirm": True,
        "max_search_results": 3,  # 限制搜索结果数量
        "search_priority_threshold": "medium",  # 只处理中等以上优先级的缺口
    })
    
    output_path = generator.generate_report_sync(
        topic="人工智能伦理研究",
        data_sources=["data/ai_ethics"],
        framework_file_path="frameworks/ethics_analysis.md"
    )
    
    print(f"⚙️ 自定义配置搜索增强完成: {output_path}")

def example_search_gap_analysis():
    """搜索缺口分析示例"""
    print("\n📖 搜索缺口分析示例")
    print("=" * 50)
    
    generator = CompleteReportGenerator(use_async=False)
    
    # 假设已有报告文件
    existing_report = "reports/existing_report.md"
    
    if Path(existing_report).exists():
        # 读取报告内容
        report_content = generator.read_generated_report(existing_report)
        
        # 分析内容缺口
        search_trigger = generator.SearchTrigger(generator)
        content_gaps = search_trigger.analyze_content_gaps(
            report_content, 
            "技术发展报告"
        )
        
        print(f"📊 发现 {len(content_gaps)} 个内容缺口:")
        for i, gap in enumerate(content_gaps, 1):
            print(f"   {i}. 类型: {gap['type']}")
            print(f"      查询: {gap['query']}")
            print(f"      优先级: {gap['priority']}")
            print(f"      原因: {gap['reason']}")
            print()
        
        # 可以选择性地处理特定类型的缺口
        high_priority_gaps = [gap for gap in content_gaps if gap['priority'] == 'high']
        
        if high_priority_gaps:
            print(f"🔍 处理 {len(high_priority_gaps)} 个高优先级缺口...")
            # 这里可以实现选择性增强
    else:
        print(f"❌ 报告文件不存在: {existing_report}")

def example_batch_search_enhancement():
    """批量搜索增强示例"""
    print("\n📖 批量搜索增强示例")
    print("=" * 50)
    
    generator = CompleteReportGenerator(use_async=False)
    
    # 批量处理多个报告
    reports_to_enhance = [
        ("reports/ai_report.md", "人工智能技术"),
        ("reports/blockchain_report.md", "区块链应用"),
        ("reports/iot_report.md", "物联网发展")
    ]
    
    enhanced_reports = []
    
    for report_path, topic in reports_to_enhance:
        if Path(report_path).exists():
            print(f"🔄 处理报告: {report_path}")
            
            try:
                enhanced_path = generator.enhance_report_with_search(
                    report_path,
                    topic,
                    user_confirm=False  # 批量处理时自动确认
                )
                enhanced_reports.append(enhanced_path)
                print(f"   ✅ 完成: {enhanced_path}")
                
            except Exception as e:
                print(f"   ❌ 失败: {str(e)}")
                enhanced_reports.append(report_path)  # 保留原始报告
        else:
            print(f"   ⚠️ 文件不存在: {report_path}")
    
    print(f"\n📊 批量处理完成: {len(enhanced_reports)} 个报告")

def example_search_api_testing():
    """搜索API测试示例"""
    print("\n📖 搜索API测试示例")
    print("=" * 50)
    
    generator = CompleteReportGenerator(use_async=False)
    
    # 初始化搜索管理器
    search_manager = generator.SearchManager(generator)
    
    print("📊 搜索API配置状态:")
    for api_name, config in search_manager.search_apis.items():
        status = "✅ 已配置" if config.get('enabled', False) else "❌ 未配置"
        print(f"   {api_name}: {status}")
    
    # 测试搜索功能
    if search_manager.search_apis:
        test_queries = [
            "人工智能 2024 最新发展",
            "区块链技术 应用案例",
            "5G网络 市场分析"
        ]
        
        for query in test_queries:
            print(f"\n🔍 测试查询: {query}")
            
            try:
                # 选择可用的搜索源
                available_sources = [name for name, config in search_manager.search_apis.items() 
                                   if config.get('enabled', False)]
                
                if available_sources:
                    results = search_manager.multi_source_search(
                        query, available_sources[:1], num_results=2
                    )
                    
                    print(f"   结果数量: {len(results)}")
                    for i, result in enumerate(results, 1):
                        print(f"   {i}. {result.get('title', '')[:60]}...")
                        print(f"      来源: {result.get('source', '')}")
                else:
                    print("   ⚠️ 没有可用的搜索API")
                    
            except Exception as e:
                print(f"   ❌ 搜索失败: {str(e)}")
    else:
        print("❌ 未配置任何搜索API")

def example_content_integration():
    """内容整合示例"""
    print("\n📖 内容整合示例")
    print("=" * 50)
    
    generator = CompleteReportGenerator(use_async=False)
    
    # 模拟搜索结果
    mock_search_results = [
        {
            'title': '2024年AI技术突破报告',
            'url': 'https://example.com/ai-2024',
            'snippet': '2024年人工智能在自然语言处理和计算机视觉领域取得重大突破，GPT-4的性能提升了40%。',
            'source': 'google',
            'gap_type': 'latest_data'
        },
        {
            'title': 'AI市场规模预测',
            'url': 'https://research.example.com/ai-market',
            'snippet': '预计2024年全球AI市场规模将达到5000亿美元，年增长率超过25%。',
            'source': 'bing',
            'gap_type': 'market_data'
        }
    ]
    
    # 模拟内容缺口
    mock_content_gaps = [
        {
            'type': 'latest_data',
            'query': 'AI 2024 最新数据',
            'priority': 'high',
            'reason': '缺少最新的技术发展数据'
        },
        {
            'type': 'market_data',
            'query': 'AI 市场规模 预测',
            'priority': 'medium',
            'reason': '缺少市场分析数据'
        }
    ]
    
    # 原始报告内容
    original_content = """# AI技术发展报告

## 1. 技术概述
人工智能技术正在快速发展...

## 2. 应用领域
AI在多个领域都有应用...
"""
    
    # 执行内容整合
    content_integrator = generator.ContentIntegrator(generator)
    
    try:
        enhanced_content = content_integrator.integrate_search_results(
            original_content,
            mock_search_results,
            "AI技术发展",
            mock_content_gaps
        )
        
        print("✅ 内容整合完成")
        print(f"原始长度: {len(original_content)} 字符")
        print(f"增强长度: {len(enhanced_content)} 字符")
        print(f"增加内容: {len(enhanced_content) - len(original_content)} 字符")
        
        # 显示增强内容的预览
        if "最新信息补充" in enhanced_content:
            print("✅ 成功添加补充信息部分")
        
    except Exception as e:
        print(f"❌ 内容整合失败: {str(e)}")

def main():
    """主函数 - 运行所有示例"""
    print("🚀 搜索增强功能使用示例")
    print("=" * 60)
    
    examples = [
        ("基础搜索增强", example_basic_search_enhancement),
        ("手动搜索增强", example_manual_search_enhancement),
        ("自动搜索增强", example_auto_search_enhancement),
        ("自定义搜索配置", example_custom_search_configuration),
        ("搜索缺口分析", example_search_gap_analysis),
        ("批量搜索增强", example_batch_search_enhancement),
        ("搜索API测试", example_search_api_testing),
        ("内容整合演示", example_content_integration)
    ]
    
    print("📋 可用示例:")
    for i, (name, _) in enumerate(examples, 1):
        print(f"   {i}. {name}")
    
    print("\n💡 使用说明:")
    print("   • 需要配置搜索API才能使用完整功能")
    print("   • 运行 python search_api_config.py 进行配置")
    print("   • 支持Google Search和Bing Search API")
    print("   • 搜索结果会经过质量验证和智能整合")
    
    print("\n🛠️ 配置选项:")
    print("   • enable_search_enhancement: 启用/禁用搜索增强")
    print("   • search_auto_confirm: 自动确认搜索")
    print("   • max_search_results: 最大搜索结果数")
    print("   • search_priority_threshold: 搜索优先级阈值")
    
    print("\n📁 输出文件:")
    print("   • 原始报告: report.md")
    print("   • 增强报告: report_enhanced.md")
    print("   • 包含最新信息补充部分")
    
    # 运行一个简单的示例
    try:
        print("\n🎯 运行内容整合演示...")
        example_content_integration()
    except Exception as e:
        print(f"示例运行失败: {str(e)}")

if __name__ == "__main__":
    main()
