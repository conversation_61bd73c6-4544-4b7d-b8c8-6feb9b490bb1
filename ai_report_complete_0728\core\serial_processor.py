#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
串行处理模块
负责必须按顺序执行的工作流步骤
"""

import time
import asyncio
from typing import List, Dict, Any, Optional
from pathlib import Path


class SerialProcessor:
    """
    串行处理器
    处理必须按顺序执行的工作流步骤
    """

    def __init__(self, generator):
        """
        初始化串行处理器
        
        Args:
            generator: 主生成器实例
        """
        self.generator = generator
        self.api_manager = generator.api_manager
        self.report_config = generator.report_config

    # ==================== 第一阶段：框架生成（串行） ====================

    async def generate_framework_stage(self, topic: str, framework_file_path: Optional[str] = None) -> Dict[str, Any]:
        """
        第一阶段：统筹模型串行完成所有框架工作
        
        这个阶段必须串行执行，因为：
        1. 框架生成需要整体规划
        2. 子结构依赖于主框架
        3. 任务指导需要完整的结构信息
        """
        print("\n" + "="*80)
        print("🎯 第一阶段：统筹模型(gemini-2.5-pro)串行完成所有框架工作")
        print("="*80)

        # 步骤1：读取框架文件（串行）
        print("📖 步骤1：读取指定路径下的报告框架")
        framework_content = ""
        if framework_file_path:
            framework_content = await self._read_framework_file_async(framework_file_path)
            print(f"✅ 成功读取框架文件，内容长度: {len(framework_content)} 字符")
        else:
            print("⚠️ 未指定框架文件，将使用默认框架")

        # 步骤2：生成完整的标题结构（串行）
        max_depth = self.report_config.get("max_depth", 6)
        print(f"\n📝 步骤2：生成完整的1-{max_depth}级标题结构")
        framework = await self._generate_complete_framework_serial(topic, framework_content)

        if not framework or "sections" not in framework:
            raise ValueError("框架生成失败")

        sections = framework["sections"]
        total_nodes = self._count_all_nodes(sections)
        print(f"✅ 框架生成完成:")
        print(f"   一级章节: {len(sections)} 个")
        print(f"   总节点数: {total_nodes} 个")

        return framework

    async def generate_task_instructions_stage(self, sections: List[Dict[str, Any]], data_sources: List[str]):
        """
        为每个节点制定任务指导（串行）
        
        这个步骤必须串行，因为需要统筹模型统一规划所有任务
        """
        print("\n📋 步骤3：为每个节点制定任务指导（串行）")
        
        # 收集所有节点信息
        all_nodes = []
        def collect_all_nodes(nodes, section_idx=0):
            for node in nodes:
                node_info = {
                    'title': node.get('title', ''),
                    'level': node.get('level', 1),
                    'section_index': section_idx,
                    'node_ref': node
                }
                all_nodes.append(node_info)
                
                if 'children' in node and node['children']:
                    collect_all_nodes(node['children'], section_idx)

        for i, section in enumerate(sections):
            collect_all_nodes([section], i)

        print(f"   📊 总共需要制定 {len(all_nodes)} 个节点的任务指导")

        # 统筹模型串行制定所有任务指导
        for i, node_info in enumerate(all_nodes, 1):
            print(f"   📋 制定任务指导 {i}/{len(all_nodes)}: {node_info['title']}")
            
            instruction = await self._generate_single_task_instruction(
                node_info, data_sources, len(all_nodes)
            )
            
            # 将指导存储到节点中
            node_info['node_ref']['task_instruction'] = instruction

        print("✅ 任务指导制定完成")

    # ==================== 第三阶段：整体审核（串行） ====================

    async def overall_document_audit_stage(self, sections: List[Dict[str, Any]], topic: str, iteration: int) -> Dict[str, Any]:
        """
        整体文档审核和优化（必须串行）
        
        这个步骤必须串行执行，因为：
        1. 需要统筹模型从整体角度审核
        2. 整体优化需要考虑所有章节的协调性
        3. 避免局部优化影响整体结构
        """
        print(f"📄 第{iteration}轮：整体文档审核和优化（串行）")

        # 统筹模型审核整体文档
        print("   🔍 统筹模型审核整体文档结构...")
        overall_audit = await self._audit_overall_document_serial(sections, topic, iteration)

        # 统筹模型优化整体文档
        if overall_audit.get("needs_optimization", False):
            print("   🔧 统筹模型执行整体文档优化...")
            optimized_sections = await self._optimize_overall_document_serial(
                sections, topic, overall_audit, iteration
            )
            
            # 更新所有章节
            for i, optimized_section in enumerate(optimized_sections):
                if i < len(sections):
                    sections[i].update(optimized_section)
            
            print("   ✅ 完成整体文档优化")
        else:
            print("   ✅ 整体文档无需优化")

        return overall_audit

    # ==================== 第四阶段：最终文档生成（串行） ====================

    async def final_document_generation_stage(self, topic: str, framework: Dict[str, Any]) -> str:
        """
        最终文档生成（串行）
        
        这个步骤必须串行执行，因为：
        1. 文档生成需要统一的格式和风格
        2. 需要整体的字数控制和平衡
        3. 最终清理和格式化必须统一处理
        """
        print("\n" + "="*80)
        print("📄 第四阶段：最终文档生成（串行）")
        print("="*80)

        # 步骤1：字数控制优化
        print("📊 步骤1：最终字数控制")
        target_words = self.report_config.get("target_words", 50000)
        await self._word_count_optimization_serial(framework, target_words)

        # 步骤2：生成最终文档
        print("📝 步骤2：生成最终文档")
        output_path = await self._generate_final_document_serial(topic, framework)

        print(f"✅ 最终文档生成完成: {output_path}")
        return output_path

    # ==================== 私有方法 ====================

    async def _read_framework_file_async(self, framework_path: str) -> str:
        """异步读取框架文件"""
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(None, self.generator.read_framework_file, framework_path)

    async def _generate_complete_framework_serial(self, topic: str, framework_content: str) -> Dict[str, Any]:
        """串行生成完整框架"""
        # 使用统筹模型生成框架
        prompt = f"""
作为报告统筹模型，请为主题"{topic}"设计一份详细的研究报告框架。

{f"请参考以下现有框架:\\n{framework_content}\\n" if framework_content else ""}

要求：
1. 必须包含恰好{self.report_config.get("primary_sections", 8)}个一级标题
2. 每个一级标题下必须完整扩展到{self.report_config.get("max_depth", 6)}级子标题
3. 标题层级必须连贯，不能跳级
4. 每个标题都应该有明确的title字段和level字段

请以JSON格式返回完整结构。
"""

        response = await self.generator.call_orchestrator_model_async(prompt)
        
        try:
            import json
            import re
            
            # 尝试解析JSON
            framework = None
            if "```json" in response:
                start = response.find("```json") + 7
                end = response.find("```", start)
                if end != -1:
                    json_str = response[start:end].strip()
                    framework = json.loads(json_str)
            elif response.strip().startswith("{"):
                framework = json.loads(response.strip())

            if framework:
                validated_framework = self.generator._validate_and_fix_framework(framework)
                if validated_framework:
                    return validated_framework

            print("⚠️ 框架解析失败，使用默认框架")
            return self.generator._get_default_framework()

        except Exception as e:
            print(f"框架解析失败: {str(e)}")
            return self.generator._get_default_framework()

    async def _generate_single_task_instruction(self, node_info: Dict[str, Any], data_sources: List[str], total_nodes: int) -> str:
        """为单个节点生成任务指导"""
        title = node_info['title']
        level = node_info['level']
        section_idx = node_info['section_index']
        
        # 获取相关数据源
        data_source = ""
        if section_idx < len(data_sources):
            data_source = data_sources[section_idx]

        prompt = f"""
作为报告统筹模型，请为节点"{title}"（第{level}级标题）制定详细的任务指导。

节点信息：
- 标题：{title}
- 层级：第{level}级
- 相关数据源：{data_source}
- 总节点数：{total_nodes}

请制定包含以下内容的任务指导：
1. 内容要求和重点
2. 字数控制建议
3. 写作风格要求
4. 数据引用要求

请直接返回任务指导内容：
"""

        instruction = await self.generator.call_orchestrator_model_async(prompt)
        return self.generator._clean_model_response(instruction)

    def _count_all_nodes(self, sections: List[Dict[str, Any]]) -> int:
        """计算所有节点数量"""
        count = 0
        for section in sections:
            count += 1
            if "children" in section and section["children"]:
                count += self._count_all_nodes(section["children"])
        return count

    async def _audit_overall_document_serial(self, sections: List[Dict[str, Any]], topic: str, iteration: int) -> Dict[str, Any]:
        """串行审核整体文档"""
        # 生成整体文档概要
        document_summary = self._generate_document_summary(sections, topic)

        prompt = f"""
作为专业的报告统筹模型，请对整体文档进行综合审核：

报告主题：{topic}
文档概要：
{document_summary}

请从以下维度进行整体审核：
1. 结构完整性：章节安排是否合理、逻辑是否清晰
2. 内容连贯性：各章节之间是否衔接自然
3. 深度一致性：各章节分析深度是否均衡
4. 重复检查：是否存在内容重复
5. 专业标准：整体是否达到专业报告标准

这是第{iteration}轮整体审核。

请以JSON格式返回审核结果：
{{
    "overall_score": 8.5,
    "needs_optimization": true,
    "structural_issues": ["章节逻辑需要调整"],
    "content_issues": ["某些章节深度不够"],
    "optimization_requirements": [
        "调整章节顺序",
        "增强章节间的逻辑连接",
        "平衡各章节的分析深度"
    ]
}}
"""

        response = await self.generator.call_orchestrator_model_async(prompt)

        try:
            import json
            if "```json" in response:
                start = response.find("```json") + 7
                end = response.find("```", start)
                if end != -1:
                    json_str = response[start:end].strip()
                    return json.loads(json_str)

            if response.strip().startswith("{"):
                return json.loads(response.strip())

            return {"overall_score": 8.0, "needs_optimization": False, "optimization_requirements": []}

        except Exception as e:
            print(f"解析整体审核结果失败: {str(e)}")
            return {"overall_score": 8.0, "needs_optimization": False, "optimization_requirements": []}

    async def _optimize_overall_document_serial(self, sections: List[Dict[str, Any]], topic: str, audit_result: Dict[str, Any], iteration: int) -> List[Dict[str, Any]]:
        """串行优化整体文档"""
        optimization_requirements = audit_result.get("optimization_requirements", [])
        
        # 生成当前文档的完整内容
        full_document = self._generate_full_document_text(sections, topic)

        prompt = f"""
作为专业的报告统筹模型，请根据审核结果优化整体文档：

报告主题：{topic}
当前完整文档：
{full_document[:5000]}  # 限制长度避免超出token限制

优化要求：
{chr(10).join(f"- {req}" for req in optimization_requirements)}

请按照以下要求进行整体优化：
1. 调整章节结构和逻辑顺序
2. 增强章节间的连贯性
3. 平衡各章节的分析深度
4. 消除内容重复
5. 提升整体专业水准

这是第{iteration}轮整体优化。

请返回优化后的完整文档结构（保持JSON格式）：
"""

        response = await self.generator.call_orchestrator_model_async(prompt)
        
        try:
            # 尝试解析优化后的结构
            import json
            if "```json" in response:
                start = response.find("```json") + 7
                end = response.find("```", start)
                if end != -1:
                    json_str = response[start:end].strip()
                    optimized_data = json.loads(json_str)
                    if "sections" in optimized_data:
                        return optimized_data["sections"]

            # 如果解析失败，返回原始sections
            return sections

        except Exception as e:
            print(f"解析整体优化结果失败: {str(e)}")
            return sections

    def _generate_document_summary(self, sections: List[Dict[str, Any]], topic: str) -> str:
        """生成文档概要"""
        summary_parts = [f"报告主题：{topic}\n"]
        
        for i, section in enumerate(sections, 1):
            title = section.get("title", f"章节{i}")
            content = section.get("content", "")
            content_preview = content[:200] + "..." if len(content) > 200 else content
            summary_parts.append(f"{i}. {title}\n   内容概要：{content_preview}\n")
        
        return "\n".join(summary_parts)

    def _generate_full_document_text(self, sections: List[Dict[str, Any]], topic: str) -> str:
        """生成完整文档文本"""
        document_parts = [f"# {topic}\n\n"]
        
        for i, section in enumerate(sections, 1):
            title = section.get("title", f"章节{i}")
            content = section.get("content", "")
            document_parts.append(f"## {i}. {title}\n\n{content}\n\n")
        
        return "".join(document_parts)

    async def _word_count_optimization_serial(self, framework: Dict[str, Any], target_words: int):
        """串行执行字数控制优化"""
        print(f"   🎯 目标字数: {target_words} 字")
        
        # 统计当前字数
        current_words = self._count_total_words(framework)
        print(f"   📊 当前字数: {current_words} 字")
        
        if abs(current_words - target_words) > target_words * 0.1:  # 超过10%差异
            print(f"   🔧 需要调整字数，目标: {target_words} 字")
            await self._adjust_word_count_serial(framework, target_words, current_words)
        else:
            print(f"   ✅ 字数符合要求")

    def _count_total_words(self, framework: Dict[str, Any]) -> int:
        """统计总字数"""
        total_words = 0
        
        def count_section_words(section):
            nonlocal total_words
            content = section.get("content", "")
            total_words += len(content)
            
            if "children" in section:
                for child in section["children"]:
                    count_section_words(child)
        
        sections = framework.get("sections", [])
        for section in sections:
            count_section_words(section)
        
        return total_words

    async def _adjust_word_count_serial(self, framework: Dict[str, Any], target_words: int, current_words: int):
        """串行调整字数"""
        # 这里可以实现具体的字数调整逻辑
        print(f"   📝 执行字数调整: {current_words} -> {target_words}")

    async def _generate_final_document_serial(self, topic: str, framework: Dict[str, Any]) -> str:
        """串行生成最终文档"""
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(None, self.generator._generate_word_document, topic, framework)
