"""
文件读取器模块 - 严格按照原始代码的文件读取实现
支持多种文件格式的读取，包括PDF、Word、Excel、CSV、文本等
"""

import os
import json
import base64
import hashlib
import chardet
from pathlib import Path
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime
import pandas as pd
import docx
import pptx
import PyPDF2
import pdfplumber
import fitz  # PyMuPDF
from PIL import Image
import io
import requests
import time

from ..config import (
    SUPPORTED_FILE_TYPES, IMAGE_EXTENSIONS,
    PDF_IMAGE_DPI, PDF_IMAGE_FORMAT,
    MAX_IMAGE_SIZE_MB, IMAGE_COMPRESSION_QUALITY,
    CACHE_DIR, ENABLE_CACHE, CACHE_EXPIRY_DAYS,
    GEMINI_API_KEY, GEMINI_BASE_URL
)


class FileReader:
    """文件读取器 - 与原始代码完全相同的实现"""
    
    def __init__(self):
        """初始化文件读取器"""
        self.cache_dir = Path(CACHE_DIR)
        self.cache_dir.mkdir(parents=True, exist_ok=True)
        
    def read_file(self, file_path: str) -> str:
        """
        读取文件内容 - 与原始代码完全相同
        根据文件类型自动选择合适的读取方法
        
        Args:
            file_path: 文件路径
            
        Returns:
            文件内容文本
        """
        file_path = Path(file_path)
        
        if not file_path.exists():
            print(f"❌ 文件不存在: {file_path}")
            return ""
        
        # 检查文件类型
        file_ext = file_path.suffix.lower()
        
        # 如果启用缓存，先检查缓存
        if ENABLE_CACHE:
            cached_content = self._get_cached_content(file_path)
            if cached_content is not None:
                print(f"   📋 使用缓存内容: {file_path.name}")
                return cached_content
        
        # 根据文件类型选择读取方法
        content = ""
        
        try:
            if file_ext == '.pdf':
                content = self.read_pdf_file(str(file_path))
            elif file_ext == '.docx':
                content = self.read_docx_file(str(file_path))
            elif file_ext == '.xlsx' or file_ext == '.xls':
                content = self.read_excel_file(str(file_path))
            elif file_ext == '.csv':
                content = self.read_csv_file(str(file_path))
            elif file_ext == '.pptx':
                content = self.read_pptx_file(str(file_path))
            elif file_ext == '.json':
                content = self.read_json_file(str(file_path))
            elif file_ext == '.xml':
                content = self.read_xml_file(str(file_path))
            elif file_ext == '.html' or file_ext == '.htm':
                content = self.read_html_file(str(file_path))
            elif file_ext in SUPPORTED_FILE_TYPES:
                content = self.read_text_file(str(file_path))
            else:
                print(f"❌ 不支持的文件类型: {file_ext}")
                return ""
            
            # 保存到缓存
            if ENABLE_CACHE and content:
                self._save_to_cache(file_path, content)
            
            return content
            
        except Exception as e:
            print(f"❌ 读取文件失败 {file_path}: {str(e)}")
            return ""
    
    def read_text_file(self, file_path: str, encoding: str = None) -> str:
        """
        读取文本文件 - 与原始代码完全相同
        自动检测编码
        
        Args:
            file_path: 文件路径
            encoding: 指定编码（可选）
            
        Returns:
            文件内容
        """
        try:
            # 如果没有指定编码，自动检测
            if encoding is None:
                with open(file_path, 'rb') as f:
                    raw_data = f.read()
                    result = chardet.detect(raw_data)
                    encoding = result['encoding'] or 'utf-8'
            
            with open(file_path, 'r', encoding=encoding) as f:
                content = f.read()
            
            return content.strip()
            
        except Exception as e:
            # 如果失败，尝试使用其他编码
            for enc in ['utf-8', 'gbk', 'gb2312', 'utf-16', 'latin-1']:
                try:
                    with open(file_path, 'r', encoding=enc) as f:
                        content = f.read()
                    return content.strip()
                except:
                    continue
            
            print(f"❌ 读取文本文件失败: {str(e)}")
            return ""
    
    def read_pdf_file(self, file_path: str) -> str:
        """
        读取PDF文件 - 与原始代码完全相同
        按顺序尝试多种方法
        
        Args:
            file_path: PDF文件路径
            
        Returns:
            提取的文本内容
        """
        print(f"   📄 正在读取PDF文件: {Path(file_path).name}")
        
        # 方法1: 使用pdfplumber（最准确）
        try:
            content = self._read_pdf_with_pdfplumber(file_path)
            if content and len(content) > 100:
                print(f"   ✅ 使用pdfplumber成功读取PDF")
                return content
        except Exception as e:
            print(f"   ⚠️ pdfplumber读取失败: {str(e)}")
        
        # 方法2: 使用PyPDF2
        try:
            content = self._read_pdf_with_pypdf2(file_path)
            if content and len(content) > 100:
                print(f"   ✅ 使用PyPDF2成功读取PDF")
                return content
        except Exception as e:
            print(f"   ⚠️ PyPDF2读取失败: {str(e)}")
        
        # 方法3: 使用PyMuPDF
        try:
            content = self._read_pdf_with_pymupdf(file_path)
            if content and len(content) > 100:
                print(f"   ✅ 使用PyMuPDF成功读取PDF")
                return content
        except Exception as e:
            print(f"   ⚠️ PyMuPDF读取失败: {str(e)}")
        
        # 方法4: 如果是扫描版PDF，尝试OCR
        if GEMINI_API_KEY:
            try:
                print(f"   🔍 尝试使用Gemini API进行OCR识别...")
                content = self._read_pdf_with_ocr(file_path)
                if content and len(content) > 100:
                    print(f"   ✅ 使用OCR成功读取PDF")
                    return content
            except Exception as e:
                print(f"   ⚠️ OCR读取失败: {str(e)}")
        
        print(f"   ❌ 所有PDF读取方法都失败了")
        return ""
    
    def _read_pdf_with_pdfplumber(self, file_path: str) -> str:
        """使用pdfplumber读取PDF - 与原始代码完全相同"""
        text = ""
        with pdfplumber.open(file_path) as pdf:
            for page in pdf.pages:
                page_text = page.extract_text()
                if page_text:
                    text += page_text + "\n"
        return text.strip()
    
    def _read_pdf_with_pypdf2(self, file_path: str) -> str:
        """使用PyPDF2读取PDF - 与原始代码完全相同"""
        text = ""
        with open(file_path, 'rb') as file:
            reader = PyPDF2.PdfReader(file)
            for page in reader.pages:
                page_text = page.extract_text()
                if page_text:
                    text += page_text + "\n"
        return text.strip()
    
    def _read_pdf_with_pymupdf(self, file_path: str) -> str:
        """使用PyMuPDF读取PDF - 与原始代码完全相同"""
        text = ""
        pdf_document = fitz.open(file_path)
        for page_num in range(pdf_document.page_count):
            page = pdf_document[page_num]
            page_text = page.get_text()
            if page_text:
                text += page_text + "\n"
        pdf_document.close()
        return text.strip()
    
    def _read_pdf_with_ocr(self, file_path: str) -> str:
        """使用OCR读取扫描版PDF - 与原始代码完全相同"""
        try:
            # 将PDF转换为图片
            pdf_document = fitz.open(file_path)
            text = ""
            
            for page_num in range(min(pdf_document.page_count, 10)):  # 限制最多10页
                page = pdf_document[page_num]
                
                # 转换为图片
                mat = fitz.Matrix(2, 2)  # 2x缩放提高清晰度
                pix = page.get_pixmap(matrix=mat)
                img_data = pix.pil_tobytes(format="PNG")
                
                # 调用Gemini API进行OCR
                ocr_text = self._ocr_image_with_gemini(img_data)
                if ocr_text:
                    text += f"\n--- 第 {page_num + 1} 页 ---\n"
                    text += ocr_text + "\n"
            
            pdf_document.close()
            return text.strip()
            
        except Exception as e:
            print(f"   ❌ OCR处理失败: {str(e)}")
            return ""
    
    def _ocr_image_with_gemini(self, image_data: bytes) -> str:
        """使用Gemini API进行OCR - 与原始代码完全相同"""
        try:
            # 将图片数据转换为base64
            image_base64 = base64.b64encode(image_data).decode()
            
            url = f"{GEMINI_BASE_URL}/gemini-1.5-flash:generateContent?key={GEMINI_API_KEY}"
            
            headers = {
                "Content-Type": "application/json"
            }
            
            data = {
                "contents": [{
                    "parts": [
                        {
                            "text": "请识别并提取这张图片中的所有文字内容。保持原始格式和结构。"
                        },
                        {
                            "inlineData": {
                                "mimeType": "image/png",
                                "data": image_base64
                            }
                        }
                    ]
                }]
            }
            
            response = requests.post(url, headers=headers, json=data, timeout=30)
            
            if response.status_code == 200:
                result = response.json()
                if "candidates" in result and result["candidates"]:
                    return result["candidates"][0]["content"]["parts"][0]["text"]
            
            return ""
            
        except Exception as e:
            print(f"   ❌ Gemini OCR失败: {str(e)}")
            return ""
    
    def read_docx_file(self, file_path: str) -> str:
        """读取Word文档 - 与原始代码完全相同"""
        try:
            doc = docx.Document(file_path)
            text = []
            
            # 读取段落
            for paragraph in doc.paragraphs:
                if paragraph.text.strip():
                    text.append(paragraph.text)
            
            # 读取表格
            for table in doc.tables:
                table_text = self._extract_table_from_docx(table)
                if table_text:
                    text.append(table_text)
            
            return '\n'.join(text).strip()
            
        except Exception as e:
            print(f"❌ 读取Word文档失败: {str(e)}")
            return ""
    
    def _extract_table_from_docx(self, table) -> str:
        """从Word表格中提取文本 - 与原始代码完全相同"""
        table_text = []
        for row in table.rows:
            row_text = []
            for cell in row.cells:
                cell_text = cell.text.strip()
                if cell_text:
                    row_text.append(cell_text)
            if row_text:
                table_text.append(' | '.join(row_text))
        return '\n'.join(table_text)
    
    def read_excel_file(self, file_path: str) -> str:
        """读取Excel文件 - 与原始代码完全相同"""
        try:
            # 读取所有sheet
            excel_file = pd.ExcelFile(file_path)
            text = []
            
            for sheet_name in excel_file.sheet_names:
                df = pd.read_excel(file_path, sheet_name=sheet_name)
                
                # 添加sheet标题
                text.append(f"\n=== Sheet: {sheet_name} ===\n")
                
                # 转换为文本
                if not df.empty:
                    # 将DataFrame转换为markdown格式的表格
                    table_text = df.to_markdown(index=False)
                    text.append(table_text)
                else:
                    text.append("(空表)")
            
            return '\n'.join(text).strip()
            
        except Exception as e:
            print(f"❌ 读取Excel文件失败: {str(e)}")
            return ""
    
    def read_csv_file(self, file_path: str) -> str:
        """读取CSV文件 - 与原始代码完全相同"""
        try:
            # 尝试不同的编码
            for encoding in ['utf-8', 'gbk', 'gb2312', 'latin-1']:
                try:
                    df = pd.read_csv(file_path, encoding=encoding)
                    break
                except:
                    continue
            else:
                df = pd.read_csv(file_path, encoding='utf-8', errors='ignore')
            
            # 转换为markdown格式
            if not df.empty:
                return df.to_markdown(index=False)
            else:
                return "(空CSV文件)"
                
        except Exception as e:
            print(f"❌ 读取CSV文件失败: {str(e)}")
            return ""
    
    def read_pptx_file(self, file_path: str) -> str:
        """读取PowerPoint文件 - 与原始代码完全相同"""
        try:
            prs = pptx.Presentation(file_path)
            text = []
            
            for slide_num, slide in enumerate(prs.slides):
                text.append(f"\n=== 幻灯片 {slide_num + 1} ===\n")
                
                # 提取文本框内容
                for shape in slide.shapes:
                    if hasattr(shape, "text") and shape.text:
                        text.append(shape.text)
                    
                    # 提取表格内容
                    if shape.has_table:
                        table_text = self._extract_table_from_pptx(shape.table)
                        if table_text:
                            text.append(table_text)
            
            return '\n'.join(text).strip()
            
        except Exception as e:
            print(f"❌ 读取PowerPoint文件失败: {str(e)}")
            return ""
    
    def _extract_table_from_pptx(self, table) -> str:
        """从PowerPoint表格中提取文本 - 与原始代码完全相同"""
        table_text = []
        for row in table.rows:
            row_text = []
            for cell in row.cells:
                cell_text = cell.text.strip()
                if cell_text:
                    row_text.append(cell_text)
            if row_text:
                table_text.append(' | '.join(row_text))
        return '\n'.join(table_text)
    
    def read_json_file(self, file_path: str) -> str:
        """读取JSON文件 - 与原始代码完全相同"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            # 格式化输出
            return json.dumps(data, ensure_ascii=False, indent=2)
            
        except Exception as e:
            print(f"❌ 读取JSON文件失败: {str(e)}")
            return ""
    
    def read_xml_file(self, file_path: str) -> str:
        """读取XML文件 - 与原始代码完全相同"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            return content.strip()
        except Exception as e:
            print(f"❌ 读取XML文件失败: {str(e)}")
            return ""
    
    def read_html_file(self, file_path: str) -> str:
        """读取HTML文件 - 与原始代码完全相同"""
        try:
            from bs4 import BeautifulSoup
            
            with open(file_path, 'r', encoding='utf-8') as f:
                html_content = f.read()
            
            # 使用BeautifulSoup解析HTML
            soup = BeautifulSoup(html_content, 'html.parser')
            
            # 提取文本内容
            text = soup.get_text(separator='\n', strip=True)
            
            return text
            
        except Exception as e:
            print(f"❌ 读取HTML文件失败: {str(e)}")
            # 如果BeautifulSoup不可用，返回原始内容
            return self.read_text_file(file_path)
    
    def _get_cached_content(self, file_path: Path) -> Optional[str]:
        """获取缓存的文件内容 - 与原始代码完全相同"""
        try:
            # 计算文件的哈希值
            file_hash = self._calculate_file_hash(file_path)
            cache_file = self.cache_dir / f"{file_hash}.txt"
            
            if cache_file.exists():
                # 检查缓存是否过期
                cache_age = datetime.now() - datetime.fromtimestamp(cache_file.stat().st_mtime)
                if cache_age.days < CACHE_EXPIRY_DAYS:
                    with open(cache_file, 'r', encoding='utf-8') as f:
                        return f.read()
            
            return None
            
        except Exception as e:
            print(f"   ⚠️ 读取缓存失败: {str(e)}")
            return None
    
    def _save_to_cache(self, file_path: Path, content: str):
        """保存内容到缓存 - 与原始代码完全相同"""
        try:
            file_hash = self._calculate_file_hash(file_path)
            cache_file = self.cache_dir / f"{file_hash}.txt"
            
            with open(cache_file, 'w', encoding='utf-8') as f:
                f.write(content)
                
        except Exception as e:
            print(f"   ⚠️ 保存缓存失败: {str(e)}")
    
    def _calculate_file_hash(self, file_path: Path) -> str:
        """计算文件的哈希值 - 与原始代码完全相同"""
        # 使用文件路径、大小和修改时间生成哈希
        stat = file_path.stat()
        hash_input = f"{file_path}_{stat.st_size}_{stat.st_mtime}"
        return hashlib.md5(hash_input.encode()).hexdigest()
