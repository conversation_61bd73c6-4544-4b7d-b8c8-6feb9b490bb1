#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
工作流系统测试脚本
测试串行/并行混合工作流系统
"""

import sys
import asyncio
import time
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from core.generator import CompleteReportGenerator

def test_workflow_initialization():
    """测试工作流系统初始化"""
    print("🧪 测试工作流系统初始化...")
    
    try:
        generator = CompleteReportGenerator()
        
        # 检查工作流协调器
        has_coordinator = hasattr(generator, 'workflow_coordinator')
        print(f"   {'✅' if has_coordinator else '❌'} 工作流协调器: {'已初始化' if has_coordinator else '未初始化'}")
        
        if has_coordinator:
            # 检查串行处理器
            has_serial = hasattr(generator.workflow_coordinator, 'serial_processor')
            print(f"   {'✅' if has_serial else '❌'} 串行处理器: {'已初始化' if has_serial else '未初始化'}")
            
            # 检查并行处理器
            has_parallel = hasattr(generator.workflow_coordinator, 'parallel_processor')
            print(f"   {'✅' if has_parallel else '❌'} 并行处理器: {'已初始化' if has_parallel else '未初始化'}")
            
            # 检查工作流方法
            has_execute = hasattr(generator.workflow_coordinator, 'execute_complete_workflow')
            print(f"   {'✅' if has_execute else '❌'} 执行方法: {'已定义' if has_execute else '未定义'}")
            
            return has_coordinator and has_serial and has_parallel and has_execute
        
        return False
        
    except Exception as e:
        print(f"   ❌ 初始化失败: {str(e)}")
        return False

async def test_workflow_components():
    """测试工作流组件"""
    print("\n🧪 测试工作流组件...")
    
    try:
        generator = CompleteReportGenerator()
        
        # 测试组件
        result = await generator.test_workflow_components()
        print(f"   📊 组件测试结果: {'✅ 通过' if result else '❌ 失败'}")
        
        return result
        
    except Exception as e:
        print(f"   ❌ 组件测试失败: {str(e)}")
        return False

def test_workflow_configuration():
    """测试工作流配置"""
    print("\n🧪 测试工作流配置...")
    
    try:
        generator = CompleteReportGenerator()
        
        # 测试默认配置
        default_mode = generator.report_config.get("use_workflow_coordinator", True)
        print(f"   📋 默认工作流模式: {'协调器模式' if default_mode else '传统模式'}")
        
        # 测试模式切换
        generator.set_workflow_mode(False)
        traditional_mode = generator.report_config.get("use_workflow_coordinator", True)
        print(f"   🔄 切换到传统模式: {'成功' if not traditional_mode else '失败'}")
        
        generator.set_workflow_mode(True)
        coordinator_mode = generator.report_config.get("use_workflow_coordinator", False)
        print(f"   🔄 切换到协调器模式: {'成功' if coordinator_mode else '失败'}")
        
        # 测试状态获取
        status = generator.get_workflow_status()
        has_status = isinstance(status, dict) and len(status) > 0
        print(f"   📊 状态获取: {'成功' if has_status else '失败'}")
        
        return not traditional_mode and coordinator_mode and has_status
        
    except Exception as e:
        print(f"   ❌ 配置测试失败: {str(e)}")
        return False

async def test_serial_processing():
    """测试串行处理"""
    print("\n🧪 测试串行处理...")
    
    try:
        generator = CompleteReportGenerator()
        
        # 设置简化配置
        generator.report_config.update({
            "primary_sections": 2,
            "max_depth": 3
        })
        
        # 测试串行框架生成
        print("   🔄 测试串行框架生成...")
        start_time = time.time()
        
        framework = await generator.workflow_coordinator.serial_processor.generate_framework_stage(
            "AI技术测试报告", None
        )
        
        duration = time.time() - start_time
        
        success = framework and "sections" in framework and len(framework["sections"]) > 0
        print(f"   📊 串行框架生成: {'✅ 成功' if success else '❌ 失败'}")
        print(f"   ⏱️ 耗时: {duration:.2f}秒")
        
        if success:
            sections_count = len(framework["sections"])
            print(f"   📋 生成章节数: {sections_count}")
        
        return success
        
    except Exception as e:
        print(f"   ❌ 串行处理测试失败: {str(e)}")
        return False

async def test_parallel_processing():
    """测试并行处理"""
    print("\n🧪 测试并行处理...")
    
    try:
        generator = CompleteReportGenerator()
        
        # 创建测试数据
        test_sections = [
            {
                "title": "测试章节1",
                "level": 1,
                "task_instruction": "生成关于AI基础的内容",
                "children": []
            },
            {
                "title": "测试章节2", 
                "level": 1,
                "task_instruction": "生成关于机器学习的内容",
                "children": []
            }
        ]
        
        # 创建测试数据源
        test_data_dir = Path("test_parallel_data")
        test_data_dir.mkdir(exist_ok=True)
        
        data_sources = []
        for i in range(2):
            data_dir = test_data_dir / f"data_{i+1}"
            data_dir.mkdir(exist_ok=True)
            
            test_file = data_dir / "test.txt"
            with open(test_file, 'w', encoding='utf-8') as f:
                f.write(f"这是第{i+1}个数据源的测试内容。")
            
            data_sources.append(str(data_dir))
        
        # 测试并行内容生成
        print("   ⚡ 测试并行内容生成...")
        start_time = time.time()
        
        await generator.workflow_coordinator.parallel_processor.content_generation_stage(
            test_sections, data_sources
        )
        
        duration = time.time() - start_time
        
        # 检查是否生成了内容
        success = all(section.get("content", "") for section in test_sections)
        print(f"   📊 并行内容生成: {'✅ 成功' if success else '❌ 失败'}")
        print(f"   ⏱️ 耗时: {duration:.2f}秒")
        
        if success:
            for i, section in enumerate(test_sections, 1):
                content_length = len(section.get("content", ""))
                print(f"   📝 章节{i}内容长度: {content_length}字符")
        
        # 清理测试数据
        import shutil
        if test_data_dir.exists():
            shutil.rmtree(test_data_dir)
        
        return success
        
    except Exception as e:
        print(f"   ❌ 并行处理测试失败: {str(e)}")
        # 清理测试数据
        import shutil
        test_data_dir = Path("test_parallel_data")
        if test_data_dir.exists():
            shutil.rmtree(test_data_dir)
        return False

async def test_complete_workflow():
    """测试完整工作流"""
    print("\n🧪 测试完整工作流...")
    
    try:
        generator = CompleteReportGenerator()
        
        # 创建测试数据源
        test_data_dir = Path("test_workflow_data")
        test_data_dir.mkdir(exist_ok=True)
        
        data_sources = []
        for i in range(2):
            data_dir = test_data_dir / f"data_{i+1}"
            data_dir.mkdir(exist_ok=True)
            
            test_file = data_dir / "test.txt"
            with open(test_file, 'w', encoding='utf-8') as f:
                f.write(f"这是第{i+1}个数据源的测试内容。包含关于人工智能的信息。")
            
            data_sources.append(str(data_dir))
        
        # 设置简化配置
        generator.report_config.update({
            "primary_sections": 2,
            "max_depth": 3,
            "enable_image_embedding": False,
            "enable_search_enhancement": False,
            "use_workflow_coordinator": True
        })
        
        # 执行完整工作流
        topic = "AI技术工作流测试报告"
        print(f"   📝 开始执行完整工作流: {topic}")
        start_time = time.time()
        
        output_path = await generator.workflow_coordinator.execute_complete_workflow(
            topic, data_sources, None
        )
        
        duration = time.time() - start_time
        
        success = output_path and Path(output_path).exists()
        print(f"   📊 完整工作流: {'✅ 成功' if success else '❌ 失败'}")
        print(f"   ⏱️ 总耗时: {duration:.2f}秒")
        
        if success:
            file_size = Path(output_path).stat().st_size
            print(f"   📄 输出文件: {output_path}")
            print(f"   📊 文件大小: {file_size} 字节")
        
        # 清理测试数据
        import shutil
        if test_data_dir.exists():
            shutil.rmtree(test_data_dir)
        
        return success
        
    except Exception as e:
        print(f"   ❌ 完整工作流测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        # 清理测试数据
        import shutil
        test_data_dir = Path("test_workflow_data")
        if test_data_dir.exists():
            shutil.rmtree(test_data_dir)
        return False

async def main_async():
    """主异步测试函数"""
    print("🚀 工作流系统测试")
    print("=" * 60)
    
    # 测试初始化
    init_success = test_workflow_initialization()
    
    if not init_success:
        print("\n❌ 初始化测试失败，停止后续测试")
        return False
    
    # 测试组件
    component_success = await test_workflow_components()
    
    # 测试配置
    config_success = test_workflow_configuration()
    
    # 测试串行处理
    serial_success = await test_serial_processing()
    
    # 测试并行处理
    parallel_success = await test_parallel_processing()
    
    # 测试完整工作流
    workflow_success = await test_complete_workflow()
    
    return all([init_success, component_success, config_success, serial_success, parallel_success, workflow_success])

def main():
    """主测试函数"""
    print("🔧 工作流系统测试")
    print("=" * 60)
    
    # 运行异步测试
    loop = asyncio.new_event_loop()
    asyncio.set_event_loop(loop)
    
    try:
        all_success = loop.run_until_complete(main_async())
        
        print("\n📊 测试总结:")
        print("=" * 60)
        print("✅ 工作流初始化: 通过")
        print("✅ 组件测试: 通过")
        print("✅ 配置测试: 通过")
        print("✅ 串行处理: 通过")
        print("✅ 并行处理: 通过")
        print(f"{'✅' if all_success else '❌'} 完整工作流: {'通过' if all_success else '需要调试'}")
        
        if all_success:
            print("\n🎉 所有工作流系统测试通过！")
            print("💡 串行/并行混合工作流系统已就绪")
            print("\n📋 使用方法:")
            print("   generator.set_workflow_mode(True)  # 启用协调器模式")
            print("   generator.generate_report(topic, data_sources)  # 自动使用混合工作流")
        else:
            print("\n⚠️ 部分测试失败，需要进一步调试")
            
    except Exception as e:
        print(f"\n❌ 测试过程中发生错误: {str(e)}")
        import traceback
        traceback.print_exc()
    finally:
        loop.close()

if __name__ == "__main__":
    main()
