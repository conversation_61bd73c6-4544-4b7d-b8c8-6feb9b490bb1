"""
命令行接口模块
提供命令行方式使用报告生成器
"""
import argparse
import sys
from pathlib import Path
from typing import List

from .report_generator import ReportGenerator


def parse_arguments():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(
        description="AI产业研究报告生成器",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例:
  # 生成新报告
  python -m src.cli generate "全球光伏储能市场分析" -d data/market data/tech data/competition data/regulation data/investment data/future data/challenges data/recommendations
  
  # 使用框架文件
  python -m src.cli generate "全球光伏储能市场分析" -d data/s1 data/s2 data/s3 data/s4 data/s5 data/s6 data/s7 data/s8 -f templates/framework.md
  
  # 从检查点恢复
  python -m src.cli generate "全球光伏储能市场分析" -d data/s1 data/s2 data/s3 data/s4 data/s5 data/s6 data/s7 data/s8 -c checkpoints/checkpoint_2.json
  
  # 列出所有检查点
  python -m src.cli list-checkpoints
        """
    )
    
    subparsers = parser.add_subparsers(dest='command', help='可用命令')
    
    # 生成报告命令
    generate_parser = subparsers.add_parser('generate', help='生成新的研究报告')
    generate_parser.add_argument('topic', type=str, help='报告主题')
    generate_parser.add_argument(
        '-d', '--data-sources',
        nargs=8,
        required=True,
        help='8个数据源文件夹路径（按章节顺序）'
    )
    generate_parser.add_argument(
        '-f', '--framework',
        type=str,
        default=None,
        help='参考框架文件路径（可选）'
    )
    generate_parser.add_argument(
        '-c', '--checkpoint',
        type=str,
        default=None,
        help='从检查点恢复（可选）'
    )
    generate_parser.add_argument(
        '--config',
        type=str,
        default='config.yaml',
        help='配置文件路径（默认: config.yaml）'
    )
    
    # 列出检查点命令
    list_parser = subparsers.add_parser('list-checkpoints', help='列出所有可用的检查点')
    list_parser.add_argument(
        '--config',
        type=str,
        default='config.yaml',
        help='配置文件路径（默认: config.yaml）'
    )
    
    return parser.parse_args()


def main():
    """主函数"""
    args = parse_arguments()
    
    if not args.command:
        print("错误：请指定命令（generate 或 list-checkpoints）")
        sys.exit(1)
    
    try:
        if args.command == 'generate':
            # 创建报告生成器
            generator = ReportGenerator(config_path=args.config)
            
            # 生成报告
            print("=" * 50)
            print("AI产业研究报告生成器")
            print("=" * 50)
            print(f"主题: {args.topic}")
            print(f"数据源: {len(args.data_sources)} 个文件夹")
            if args.framework:
                print(f"框架文件: {args.framework}")
            if args.checkpoint:
                print(f"检查点: {args.checkpoint}")
            print("=" * 50)
            
            # 执行生成
            output_path = generator.generate_report(
                topic=args.topic,
                data_sources=args.data_sources,
                framework_file=args.framework,
                checkpoint_path=args.checkpoint
            )
            
            print("=" * 50)
            print(f"报告生成成功！")
            print(f"输出文件: {output_path}")
            print("=" * 50)
            
        elif args.command == 'list-checkpoints':
            # 列出检查点
            generator = ReportGenerator(config_path=args.config)
            checkpoints = generator.list_checkpoints()
            
            print("=" * 50)
            print("可用的检查点")
            print("=" * 50)
            
            if checkpoints:
                for cp in checkpoints:
                    print(f"  - {cp}")
            else:
                print("  没有找到任何检查点")
            
            print("=" * 50)
            
    except Exception as e:
        print(f"错误: {str(e)}", file=sys.stderr)
        sys.exit(1)


if __name__ == "__main__":
    main()
