from typing_extensions import Buffer, TypeAlias

_AdpcmState: TypeAlias = tuple[int, int]
_RatecvState: TypeAlias = tuple[int, tuple[tuple[int, int], ...]]

class error(Exception): ...

def add(fragment1: <PERSON>uffer, fragment2: Buffer, width: int, /) -> bytes: ...
def adpcm2lin(fragment: Buffer, width: int, state: _AdpcmState | None, /) -> tuple[bytes, _AdpcmState]: ...
def alaw2lin(fragment: Buffer, width: int, /) -> bytes: ...
def avg(fragment: Buffer, width: int, /) -> int: ...
def avgpp(fragment: Buffer, width: int, /) -> int: ...
def bias(fragment: Buffer, width: int, bias: int, /) -> bytes: ...
def byteswap(fragment: Buffer, width: int, /) -> bytes: ...
def cross(fragment: Buffer, width: int, /) -> int: ...
def findfactor(fragment: <PERSON><PERSON>er, reference: <PERSON><PERSON><PERSON>, /) -> float: ...
def findfit(fragment: <PERSON><PERSON><PERSON>, reference: <PERSON><PERSON><PERSON>, /) -> tuple[int, float]: ...
def findmax(fragment: Buffer, length: int, /) -> int: ...
def getsample(fragment: Buffer, width: int, index: int, /) -> int: ...
def lin2adpcm(fragment: Buffer, width: int, state: _AdpcmState | None, /) -> tuple[bytes, _AdpcmState]: ...
def lin2alaw(fragment: Buffer, width: int, /) -> bytes: ...
def lin2lin(fragment: Buffer, width: int, newwidth: int, /) -> bytes: ...
def lin2ulaw(fragment: Buffer, width: int, /) -> bytes: ...
def max(fragment: Buffer, width: int, /) -> int: ...
def maxpp(fragment: Buffer, width: int, /) -> int: ...
def minmax(fragment: Buffer, width: int, /) -> tuple[int, int]: ...
def mul(fragment: Buffer, width: int, factor: float, /) -> bytes: ...
def ratecv(
    fragment: Buffer,
    width: int,
    nchannels: int,
    inrate: int,
    outrate: int,
    state: _RatecvState | None,
    weightA: int = 1,
    weightB: int = 0,
    /,
) -> tuple[bytes, _RatecvState]: ...
def reverse(fragment: Buffer, width: int, /) -> bytes: ...
def rms(fragment: Buffer, width: int, /) -> int: ...
def tomono(fragment: Buffer, width: int, lfactor: float, rfactor: float, /) -> bytes: ...
def tostereo(fragment: Buffer, width: int, lfactor: float, rfactor: float, /) -> bytes: ...
def ulaw2lin(fragment: Buffer, width: int, /) -> bytes: ...
