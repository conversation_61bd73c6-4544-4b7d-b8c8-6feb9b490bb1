"""
测试扩展文件格式支持
验证各种文件格式的读取功能
"""
import os
import sys
from pathlib import Path
import tempfile
import json

def create_test_files():
    """创建测试文件"""
    test_dir = Path("test_files")
    test_dir.mkdir(exist_ok=True)
    
    # 创建各种格式的测试文件
    test_files = {}
    
    # 1. 文本文件
    with open(test_dir / "test.txt", "w", encoding="utf-8") as f:
        f.write("这是一个测试文本文件\n包含中文内容")
    test_files["txt"] = test_dir / "test.txt"
    
    # 2. Markdown文件
    with open(test_dir / "test.md", "w", encoding="utf-8") as f:
        f.write("# 测试Markdown文件\n\n这是**粗体**文本和*斜体*文本。")
    test_files["md"] = test_dir / "test.md"
    
    # 3. JSON文件
    with open(test_dir / "test.json", "w", encoding="utf-8") as f:
        json.dump({"name": "测试", "value": 123, "items": ["a", "b", "c"]}, f, ensure_ascii=False, indent=2)
    test_files["json"] = test_dir / "test.json"
    
    # 4. XML文件
    with open(test_dir / "test.xml", "w", encoding="utf-8") as f:
        f.write("""<?xml version="1.0" encoding="UTF-8"?>
<root>
    <item id="1">
        <name>测试项目</name>
        <value>123</value>
    </item>
    <item id="2">
        <name>另一个项目</name>
        <value>456</value>
    </item>
</root>""")
    test_files["xml"] = test_dir / "test.xml"
    
    # 5. YAML文件
    with open(test_dir / "test.yaml", "w", encoding="utf-8") as f:
        f.write("""name: 测试配置
version: 1.0
settings:
  debug: true
  max_items: 100
  languages:
    - 中文
    - English
    - 日本語""")
    test_files["yaml"] = test_dir / "test.yaml"
    
    # 6. CSV文件
    with open(test_dir / "test.csv", "w", encoding="utf-8") as f:
        f.write("姓名,年龄,城市\n张三,25,北京\n李四,30,上海\n王五,28,广州")
    test_files["csv"] = test_dir / "test.csv"
    
    # 7. Python代码文件
    with open(test_dir / "test.py", "w", encoding="utf-8") as f:
        f.write("""# 测试Python文件
def hello_world():
    print("Hello, World!")
    return "测试成功"

if __name__ == "__main__":
    hello_world()""")
    test_files["py"] = test_dir / "test.py"
    
    # 8. 配置文件
    with open(test_dir / "test.ini", "w", encoding="utf-8") as f:
        f.write("""[DEFAULT]
debug = true
log_level = INFO

[database]
host = localhost
port = 3306
name = test_db""")
    test_files["ini"] = test_dir / "test.ini"
    
    return test_files

def test_file_reading():
    """测试文件读取功能"""
    print("🧪 测试扩展文件格式支持")
    print("=" * 60)
    
    try:
        from complete_report_generator import CompleteReportGenerator
        
        # 创建测试文件
        print("📁 创建测试文件...")
        test_files = create_test_files()
        print(f"✅ 创建了 {len(test_files)} 个测试文件")
        
        # 初始化生成器
        generator = CompleteReportGenerator(use_async=False)
        
        # 测试各种文件格式
        success_count = 0
        total_count = len(test_files)
        
        for file_type, file_path in test_files.items():
            print(f"\n📄 测试 {file_type.upper()} 文件: {file_path.name}")
            try:
                content = generator._read_single_framework_file(file_path)
                if content and len(content.strip()) > 0:
                    print(f"   ✅ 读取成功: {len(content)} 字符")
                    print(f"   📝 内容预览: {content[:100]}...")
                    success_count += 1
                else:
                    print(f"   ❌ 读取失败: 内容为空")
            except Exception as e:
                print(f"   ❌ 读取失败: {str(e)}")
        
        print(f"\n📊 测试结果: {success_count}/{total_count} 个文件格式测试成功")
        
        # 测试目录读取
        print(f"\n📁 测试目录读取功能...")
        try:
            content = generator.read_data_source("test_files")
            if content and len(content.strip()) > 0:
                print(f"   ✅ 目录读取成功: {len(content)} 字符")
                file_count = content.count("=== 文件:")
                print(f"   📄 读取到 {file_count} 个文件")
            else:
                print(f"   ❌ 目录读取失败: 内容为空")
        except Exception as e:
            print(f"   ❌ 目录读取失败: {str(e)}")
        
        # 清理测试文件
        print(f"\n🧹 清理测试文件...")
        import shutil
        if Path("test_files").exists():
            shutil.rmtree("test_files")
            print("✅ 测试文件清理完成")
        
        if success_count >= total_count * 0.8:  # 80%以上成功
            print(f"\n🎉 扩展文件格式支持测试通过！")
            return True
        else:
            print(f"\n⚠️ 部分文件格式支持有问题，请检查依赖库安装")
            return False
            
    except ImportError as e:
        print(f"❌ 导入失败: {str(e)}")
        print("💡 请先运行 python install_extended_dependencies.py 安装依赖")
        return False
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        return False

def show_supported_formats():
    """显示支持的文件格式"""
    print("\n📋 当前支持的文件格式:")
    print("=" * 60)
    
    formats = {
        "📄 文本文件": [".txt", ".md", ".rst", ".log"],
        "📊 Office文档": [".docx", ".doc", ".xlsx", ".xls", ".csv", ".tsv", ".pptx", ".ppt"],
        "📑 PDF文件": [".pdf"],
        "🔧 数据文件": [".json", ".xml", ".yaml", ".yml"],
        "💻 代码文件": [".py", ".js", ".html", ".css", ".sql", ".java", ".cpp", ".c", ".h", ".php", ".rb", ".go", ".sh", ".bat"],
        "⚙️ 配置文件": [".ini", ".cfg", ".conf", ".properties", ".toml"],
        "📚 其他格式": [".odt", ".ods", ".epub", ".rtf"]
    }
    
    for category, extensions in formats.items():
        print(f"\n{category}:")
        for ext in extensions:
            print(f"   {ext}")

if __name__ == "__main__":
    # 显示支持的格式
    show_supported_formats()
    
    # 运行测试
    success = test_file_reading()
    
    if success:
        print(f"\n🚀 所有测试通过！现在可以使用扩展的文件格式支持功能。")
    else:
        print(f"\n💡 如果测试失败，请运行以下命令安装依赖:")
        print(f"   python install_extended_dependencies.py")
    
    sys.exit(0 if success else 1)
