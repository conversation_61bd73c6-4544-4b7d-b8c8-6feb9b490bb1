#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
串行/并行混合工作流使用示例
演示如何使用新的工作流系统
"""

import sys
import asyncio
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from core.generator import CompleteReportGenerator

def example_basic_usage():
    """基本使用示例"""
    print("📋 示例1：基本使用")
    print("=" * 50)
    
    # 初始化生成器
    generator = CompleteReportGenerator()
    
    # 检查工作流状态
    print("🔍 检查工作流状态:")
    status = generator.get_workflow_status()
    for key, value in status.items():
        print(f"   {key}: {value}")
    
    # 打印工作流摘要
    print("\n📊 工作流摘要:")
    generator.print_workflow_summary()

def example_configuration():
    """配置示例"""
    print("\n📋 示例2：工作流配置")
    print("=" * 50)
    
    generator = CompleteReportGenerator()
    
    # 配置工作流参数
    print("🔧 配置工作流参数:")
    generator.report_config.update({
        "primary_sections": 6,           # 6个一级章节
        "max_depth": 4,                  # 最大4级标题
        "target_words": 30000,           # 目标3万字
        "enable_image_embedding": True,   # 启用图片嵌入
        "enable_search_enhancement": True # 启用搜索增强
    })
    
    print(f"   一级章节数: {generator.report_config['primary_sections']}")
    print(f"   最大深度: {generator.report_config['max_depth']}")
    print(f"   目标字数: {generator.report_config['target_words']:,}")
    
    # 切换工作流模式
    print("\n🔄 工作流模式切换:")
    generator.set_workflow_mode(False)  # 传统模式
    generator.set_workflow_mode(True)   # 协调器模式

async def example_serial_processing():
    """串行处理示例"""
    print("\n📋 示例3：串行处理")
    print("=" * 50)
    
    generator = CompleteReportGenerator()
    
    # 设置简化配置用于演示
    generator.report_config.update({
        "primary_sections": 3,
        "max_depth": 3
    })
    
    print("🔄 执行串行框架生成...")
    try:
        framework = await generator.workflow_coordinator.serial_processor.generate_framework_stage(
            "人工智能技术发展报告", None
        )
        
        print(f"✅ 串行框架生成成功:")
        print(f"   章节数量: {len(framework.get('sections', []))}")
        
        # 显示章节标题
        for i, section in enumerate(framework.get('sections', []), 1):
            title = section.get('title', f'章节{i}')
            print(f"   {i}. {title}")
            
    except Exception as e:
        print(f"❌ 串行处理失败: {str(e)}")

async def example_parallel_processing():
    """并行处理示例"""
    print("\n📋 示例4：并行处理")
    print("=" * 50)
    
    generator = CompleteReportGenerator()
    
    # 创建测试数据
    test_sections = [
        {
            "title": "人工智能概述",
            "level": 1,
            "task_instruction": "介绍人工智能的基本概念、发展历程和主要应用领域",
            "children": []
        },
        {
            "title": "技术发展趋势",
            "level": 1,
            "task_instruction": "分析当前AI技术的发展趋势和未来展望",
            "children": []
        }
    ]
    
    # 创建测试数据源
    test_data_dir = Path("example_data")
    test_data_dir.mkdir(exist_ok=True)
    
    data_sources = []
    for i in range(2):
        data_dir = test_data_dir / f"section_{i+1}"
        data_dir.mkdir(exist_ok=True)
        
        test_file = data_dir / "content.txt"
        with open(test_file, 'w', encoding='utf-8') as f:
            f.write(f"这是第{i+1}个章节的数据源内容。包含关于人工智能的详细信息。")
        
        data_sources.append(str(data_dir))
    
    print("⚡ 执行并行内容生成...")
    try:
        await generator.workflow_coordinator.parallel_processor.content_generation_stage(
            test_sections, data_sources
        )
        
        print("✅ 并行内容生成成功:")
        for i, section in enumerate(test_sections, 1):
            content_length = len(section.get("content", ""))
            print(f"   章节{i}: {content_length} 字符")
            
    except Exception as e:
        print(f"❌ 并行处理失败: {str(e)}")
    finally:
        # 清理测试数据
        import shutil
        if test_data_dir.exists():
            shutil.rmtree(test_data_dir)

async def example_complete_workflow():
    """完整工作流示例"""
    print("\n📋 示例5：完整工作流")
    print("=" * 50)
    
    generator = CompleteReportGenerator()
    
    # 创建示例数据源
    example_data_dir = Path("example_workflow_data")
    example_data_dir.mkdir(exist_ok=True)
    
    data_sources = []
    topics = ["AI基础理论", "机器学习应用", "深度学习发展"]
    
    for i, topic in enumerate(topics):
        data_dir = example_data_dir / f"data_{i+1}"
        data_dir.mkdir(exist_ok=True)
        
        # 创建多个文件模拟真实数据源
        for j in range(3):
            test_file = data_dir / f"file_{j+1}.txt"
            with open(test_file, 'w', encoding='utf-8') as f:
                f.write(f"""
{topic}相关内容 - 文件{j+1}

这是关于{topic}的详细信息。包含以下要点：
1. 基本概念和定义
2. 技术原理和方法
3. 应用场景和案例
4. 发展趋势和挑战

数据来源：模拟数据源{i+1}
更新时间：2024年
""")
        
        data_sources.append(str(data_dir))
    
    # 配置简化参数用于演示
    generator.report_config.update({
        "primary_sections": 3,
        "max_depth": 3,
        "target_words": 10000,
        "enable_image_embedding": False,
        "enable_search_enhancement": False
    })
    
    print("🚀 执行完整工作流...")
    print("📋 配置信息:")
    print(f"   主题: 人工智能技术发展报告")
    print(f"   数据源: {len(data_sources)} 个")
    print(f"   一级章节: {generator.report_config['primary_sections']} 个")
    print(f"   最大深度: {generator.report_config['max_depth']} 级")
    
    try:
        import time
        start_time = time.time()
        
        output_path = await generator.workflow_coordinator.execute_complete_workflow(
            topic="人工智能技术发展报告",
            data_sources=data_sources,
            framework_file_path=None
        )
        
        duration = time.time() - start_time
        
        print(f"\n✅ 完整工作流执行成功!")
        print(f"   输出文件: {output_path}")
        print(f"   总耗时: {duration:.1f}秒")
        
        if output_path and Path(output_path).exists():
            file_size = Path(output_path).stat().st_size
            print(f"   文件大小: {file_size:,} 字节")
        
        return True
        
    except Exception as e:
        print(f"❌ 完整工作流失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        # 清理测试数据
        import shutil
        if example_data_dir.exists():
            shutil.rmtree(example_data_dir)

def example_mode_comparison():
    """模式对比示例"""
    print("\n📋 示例6：模式对比")
    print("=" * 50)
    
    generator = CompleteReportGenerator()
    
    print("🔄 工作流模式对比:")
    print()
    
    # 传统模式
    generator.set_workflow_mode(False)
    print("📊 传统模式特点:")
    print("   • 完全串行处理")
    print("   • 稳定可靠")
    print("   • 处理速度较慢")
    print("   • 适合小规模报告")
    
    # 协调器模式
    generator.set_workflow_mode(True)
    print("\n📊 协调器模式特点:")
    print("   • 串行/并行混合")
    print("   • 性能优化")
    print("   • 处理速度快2-3倍")
    print("   • 适合大规模报告")
    
    print("\n💡 选择建议:")
    print("   • 快速测试: 使用传统模式")
    print("   • 生产环境: 使用协调器模式")
    print("   • 大型报告: 使用协调器模式")

async def main():
    """主函数"""
    print("🚀 串行/并行混合工作流使用示例")
    print("=" * 60)
    
    # 示例1：基本使用
    example_basic_usage()
    
    # 示例2：配置
    example_configuration()
    
    # 示例3：串行处理
    await example_serial_processing()
    
    # 示例4：并行处理
    await example_parallel_processing()
    
    # 示例5：完整工作流
    workflow_success = await example_complete_workflow()
    
    # 示例6：模式对比
    example_mode_comparison()
    
    print("\n📊 示例总结:")
    print("=" * 60)
    print("✅ 基本使用: 演示完成")
    print("✅ 配置示例: 演示完成")
    print("✅ 串行处理: 演示完成")
    print("✅ 并行处理: 演示完成")
    print(f"{'✅' if workflow_success else '❌'} 完整工作流: {'演示完成' if workflow_success else '需要调试'}")
    print("✅ 模式对比: 演示完成")
    
    if workflow_success:
        print("\n🎉 所有示例运行成功!")
        print("💡 您现在可以开始使用串行/并行混合工作流系统了")
        
        print("\n📋 快速开始:")
        print("```python")
        print("from core.generator import CompleteReportGenerator")
        print("")
        print("generator = CompleteReportGenerator()")
        print("output = generator.generate_report(")
        print("    topic='您的报告主题',")
        print("    data_sources=['数据源1', '数据源2']")
        print(")")
        print("```")
    else:
        print("\n⚠️ 部分示例需要进一步调试")

if __name__ == "__main__":
    # 运行示例
    loop = asyncio.new_event_loop()
    asyncio.set_event_loop(loop)
    
    try:
        loop.run_until_complete(main())
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断示例运行")
    except Exception as e:
        print(f"\n❌ 示例运行失败: {str(e)}")
        import traceback
        traceback.print_exc()
    finally:
        loop.close()
