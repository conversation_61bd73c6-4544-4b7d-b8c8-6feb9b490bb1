"""
搜索API配置管理
管理Google Search API和Bing Search API的配置
"""
import os
from pathlib import Path

def setup_search_apis():
    """设置搜索API配置"""
    print("🔧 搜索API配置向导")
    print("=" * 50)
    
    config_updated = False

    # Metaso Search API配置 (推荐)
    print("\n🔍 Metaso Search API 配置 (推荐)")
    print("特点:")
    print("• 支持中文搜索优化")
    print("• 同时支持网页和学术论文搜索")
    print("• 高质量的搜索结果")
    print("• 已提供测试API Key")

    metaso_api_key = os.getenv('METASO_API_KEY', 'mk-988A8E4DC50C53312E3D1A8729687F4C')

    if metaso_api_key:
        print(f"✅ Metaso Search API 已配置")
        print(f"   API Key: {metaso_api_key[:15]}...")

        # 测试API
        try:
            test_metaso_api(metaso_api_key)
        except Exception as e:
            print(f"   ⚠️ API测试失败: {str(e)}")
    else:
        print("❌ Metaso Search API 未配置")

        setup_metaso = input("是否现在配置Metaso Search API? (y/n): ").strip().lower()
        if setup_metaso in ['y', 'yes']:
            print("您可以:")
            print("1. 使用提供的测试API Key (推荐)")
            print("2. 输入您自己的API Key")

            choice = input("请选择 (1/2): ").strip()
            if choice == '1':
                new_api_key = 'mk-988A8E4DC50C53312E3D1A8729687F4C'
                set_env_var('METASO_API_KEY', new_api_key)
                print("✅ Metaso Search API 配置完成 (使用测试Key)")
                config_updated = True
            elif choice == '2':
                new_api_key = input("请输入您的Metaso API Key: ").strip()
                if new_api_key:
                    set_env_var('METASO_API_KEY', new_api_key)
                    print("✅ Metaso Search API 配置完成")
                    config_updated = True

    # Google Custom Search API配置
    print("\n📍 Google Custom Search API 配置")
    print("获取方式:")
    print("1. 访问 https://developers.google.com/custom-search/v1/introduction")
    print("2. 创建项目并启用Custom Search API")
    print("3. 获取API Key")
    print("4. 创建自定义搜索引擎获取CX ID")
    
    google_api_key = os.getenv('GOOGLE_SEARCH_API_KEY')
    google_cx = os.getenv('GOOGLE_SEARCH_CX')
    
    if google_api_key and google_cx:
        print(f"✅ Google Search API 已配置")
        print(f"   API Key: {google_api_key[:10]}...")
        print(f"   CX ID: {google_cx}")
    else:
        print("❌ Google Search API 未配置")
        
        setup_google = input("是否现在配置Google Search API? (y/n): ").strip().lower()
        if setup_google in ['y', 'yes']:
            new_api_key = input("请输入Google Search API Key: ").strip()
            new_cx = input("请输入Google Custom Search CX ID: ").strip()
            
            if new_api_key and new_cx:
                set_env_var('GOOGLE_SEARCH_API_KEY', new_api_key)
                set_env_var('GOOGLE_SEARCH_CX', new_cx)
                print("✅ Google Search API 配置完成")
                config_updated = True
    
    # Bing Search API配置
    print("\n🔍 Bing Search API 配置")
    print("获取方式:")
    print("1. 访问 https://www.microsoft.com/en-us/bing/apis/bing-web-search-api")
    print("2. 创建Azure账户并订阅Bing Search API")
    print("3. 获取订阅密钥")
    
    bing_api_key = os.getenv('BING_SEARCH_API_KEY')
    
    if bing_api_key:
        print(f"✅ Bing Search API 已配置")
        print(f"   API Key: {bing_api_key[:10]}...")
    else:
        print("❌ Bing Search API 未配置")
        
        setup_bing = input("是否现在配置Bing Search API? (y/n): ").strip().lower()
        if setup_bing in ['y', 'yes']:
            new_api_key = input("请输入Bing Search API Key: ").strip()
            
            if new_api_key:
                set_env_var('BING_SEARCH_API_KEY', new_api_key)
                print("✅ Bing Search API 配置完成")
                config_updated = True
    
    # 总结
    print(f"\n📊 配置总结:")
    metaso_status = "✅ 已配置" if os.getenv('METASO_API_KEY') else "❌ 未配置"
    google_status = "✅ 已配置" if (os.getenv('GOOGLE_SEARCH_API_KEY') and os.getenv('GOOGLE_SEARCH_CX')) else "❌ 未配置"
    bing_status = "✅ 已配置" if os.getenv('BING_SEARCH_API_KEY') else "❌ 未配置"

    print(f"   Metaso Search API: {metaso_status}")
    print(f"   Google Search API: {google_status}")
    print(f"   Bing Search API: {bing_status}")
    
    if config_updated:
        print(f"\n💡 配置已更新，请重启程序以使配置生效")
    
    return config_updated

def set_env_var(key, value):
    """设置环境变量"""
    try:
        # 设置当前会话的环境变量
        os.environ[key] = value
        
        # 尝试写入.env文件
        env_file = Path('.env')
        
        # 读取现有内容
        existing_lines = []
        if env_file.exists():
            with open(env_file, 'r', encoding='utf-8') as f:
                existing_lines = f.readlines()
        
        # 检查是否已存在该变量
        key_exists = False
        for i, line in enumerate(existing_lines):
            if line.startswith(f"{key}="):
                existing_lines[i] = f"{key}={value}\n"
                key_exists = True
                break
        
        # 如果不存在，添加新行
        if not key_exists:
            existing_lines.append(f"{key}={value}\n")
        
        # 写入文件
        with open(env_file, 'w', encoding='utf-8') as f:
            f.writelines(existing_lines)
        
        print(f"   ✅ 环境变量 {key} 已保存到 .env 文件")
        
    except Exception as e:
        print(f"   ⚠️ 保存环境变量失败: {str(e)}")

def load_env_file():
    """加载.env文件"""
    env_file = Path('.env')
    
    if env_file.exists():
        try:
            with open(env_file, 'r', encoding='utf-8') as f:
                for line in f:
                    line = line.strip()
                    if line and '=' in line and not line.startswith('#'):
                        key, value = line.split('=', 1)
                        os.environ[key] = value
            
            print("✅ .env 文件已加载")
            return True
            
        except Exception as e:
            print(f"⚠️ 加载.env文件失败: {str(e)}")
            return False
    
    return False

def check_search_api_status():
    """检查搜索API状态"""
    print("🔍 搜索API状态检查")
    print("=" * 30)
    
    # 加载环境变量
    load_env_file()

    # Metaso Search API
    metaso_api_key = os.getenv('METASO_API_KEY')

    if metaso_api_key:
        print("✅ Metaso Search API: 已配置")

        # 测试API
        try:
            test_metaso_api(metaso_api_key)
        except Exception as e:
            print(f"   ⚠️ API测试失败: {str(e)}")
    else:
        print("❌ Metaso Search API: 未配置")

    # Google Search API
    google_api_key = os.getenv('GOOGLE_SEARCH_API_KEY')
    google_cx = os.getenv('GOOGLE_SEARCH_CX')
    
    if google_api_key and google_cx:
        print("✅ Google Search API: 已配置")
        
        # 测试API
        try:
            test_google_api(google_api_key, google_cx)
        except Exception as e:
            print(f"   ⚠️ API测试失败: {str(e)}")
    else:
        print("❌ Google Search API: 未配置")
    
    # Bing Search API
    bing_api_key = os.getenv('BING_SEARCH_API_KEY')
    
    if bing_api_key:
        print("✅ Bing Search API: 已配置")
        
        # 测试API
        try:
            test_bing_api(bing_api_key)
        except Exception as e:
            print(f"   ⚠️ API测试失败: {str(e)}")
    else:
        print("❌ Bing Search API: 未配置")

def test_google_api(api_key, cx):
    """测试Google Search API"""
    try:
        import requests
        
        url = "https://www.googleapis.com/customsearch/v1"
        params = {
            'key': api_key,
            'cx': cx,
            'q': 'test',
            'num': 1
        }
        
        response = requests.get(url, params=params, timeout=10)
        
        if response.status_code == 200:
            print("   ✅ Google API 测试成功")
        else:
            print(f"   ❌ Google API 测试失败: HTTP {response.status_code}")
            
    except ImportError:
        print("   ⚠️ 需要安装requests库: pip install requests")
    except Exception as e:
        print(f"   ❌ Google API 测试异常: {str(e)}")

def test_bing_api(api_key):
    """测试Bing Search API"""
    try:
        import requests
        
        url = "https://api.bing.microsoft.com/v7.0/search"
        headers = {
            'Ocp-Apim-Subscription-Key': api_key
        }
        params = {
            'q': 'test',
            'count': 1
        }
        
        response = requests.get(url, headers=headers, params=params, timeout=10)
        
        if response.status_code == 200:
            print("   ✅ Bing API 测试成功")
        else:
            print(f"   ❌ Bing API 测试失败: HTTP {response.status_code}")
            
    except ImportError:
        print("   ⚠️ 需要安装requests库: pip install requests")
    except Exception as e:
        print(f"   ❌ Bing API 测试异常: {str(e)}")

def test_metaso_api(api_key):
    """测试Metaso Search API"""
    try:
        import http.client
        import json

        conn = http.client.HTTPSConnection("metaso.cn")
        payload = json.dumps({
            "q": "人工智能",
            "scope": "webpage",
            "includeSummary": True,
            "size": "1"
        })
        headers = {
            'Authorization': f'Bearer {api_key}',
            'Accept': 'application/json',
            'Content-Type': 'application/json'
        }

        conn.request("POST", "/api/v1/search", payload, headers)
        res = conn.getresponse()

        if res.status == 200:
            data = res.read()
            response_data = json.loads(data.decode("utf-8"))

            if response_data.get('data', {}).get('results'):
                print("   ✅ Metaso API 测试成功")
            else:
                print("   ⚠️ Metaso API 返回空结果")
        else:
            print(f"   ❌ Metaso API 测试失败: HTTP {res.status}")

    except Exception as e:
        print(f"   ❌ Metaso API 测试异常: {str(e)}")

def create_env_template():
    """创建.env模板文件"""
    template_content = """# 搜索API配置文件
# 请填入您的API密钥

# Metaso Search API (推荐 - 支持中文和学术搜索)
# 已提供测试API Key，可直接使用
METASO_API_KEY=mk-988A8E4DC50C53312E3D1A8729687F4C

# Google Custom Search API
# 获取地址: https://developers.google.com/custom-search/v1/introduction
GOOGLE_SEARCH_API_KEY=your_google_api_key_here
GOOGLE_SEARCH_CX=your_google_cx_id_here

# Bing Search API
# 获取地址: https://www.microsoft.com/en-us/bing/apis/bing-web-search-api
BING_SEARCH_API_KEY=your_bing_api_key_here

# 其他配置
# ENABLE_SEARCH_ENHANCEMENT=true
# MAX_SEARCH_RESULTS=5
"""
    
    env_template_file = Path('.env.template')
    
    with open(env_template_file, 'w', encoding='utf-8') as f:
        f.write(template_content)
    
    print(f"✅ 已创建配置模板文件: {env_template_file}")
    print("💡 请复制为.env文件并填入您的API密钥")

def main():
    """主函数"""
    print("🚀 搜索API配置工具")
    print("=" * 50)
    
    options = [
        ("检查API状态", check_search_api_status),
        ("配置搜索API", setup_search_apis),
        ("创建配置模板", create_env_template),
        ("加载环境变量", load_env_file)
    ]
    
    print("请选择操作:")
    for i, (name, _) in enumerate(options, 1):
        print(f"   {i}. {name}")
    
    try:
        choice = int(input("\n请输入选项 (1-4): ").strip())
        
        if 1 <= choice <= len(options):
            _, func = options[choice - 1]
            func()
        else:
            print("❌ 无效选项")
            
    except ValueError:
        print("❌ 请输入有效数字")
    except KeyboardInterrupt:
        print("\n❌ 用户取消操作")

if __name__ == "__main__":
    main()
