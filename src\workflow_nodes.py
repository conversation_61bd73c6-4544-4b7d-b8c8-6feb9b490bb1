"""
工作流节点实现模块
实现LangGraph状态机中的各个节点
"""
import os
import re
import json
import time
from datetime import datetime
from typing import Dict, Any, List, Optional
from pathlib import Path

from .state import ReportState
from .config import Config
from .llm_client import LL<PERSON>lient
from .context_manager import ContextManager
from .logger import ProcessLogger


class WorkflowNodes:
    """
    工作流节点类
    包含所有LangGraph状态机节点的实现
    """
    
    def __init__(self, config: Config, llm_client: LLMClient, context_manager: ContextManager, logger: ProcessLogger):
        self.config = config
        self.llm_client = llm_client
        self.context_manager = context_manager
        self.logger = logger
    
    def _save_checkpoint_on_error(self, state: ReportState, error_context: str = ""):
        """
        在发生错误时保存检查点
        """
        if self.config.persistence.enabled:
            try:
                checkpoint_data = state.to_checkpoint_dict()
                timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                checkpoint_file = Path(self.config.persistence.checkpoint_dir) / f"error_checkpoint_{timestamp}_{error_context}.json"
                checkpoint_file.parent.mkdir(parents=True, exist_ok=True)
                
                with open(checkpoint_file, 'w', encoding='utf-8') as f:
                    json.dump(checkpoint_data, f, ensure_ascii=False, indent=2)
                
                state.last_checkpoint = str(checkpoint_file)
                self.logger.log_state_checkpoint(f"Error checkpoint saved: {checkpoint_file}")
                self.logger.logger.info(f"Checkpoint saved due to error in {error_context}: {checkpoint_file}")
            except Exception as checkpoint_error:
                self.logger.logger.error(f"Failed to save error checkpoint: {checkpoint_error}")
    
    def setup_and_prepare_sources(self, state: ReportState) -> ReportState:
        """
        Node 1: 验证数据源路径
        """
        self.logger.log_node_start("setup_and_prepare_sources")
        start_time = time.time()
        
        try:
            # 验证数据源数量
            if len(state.data_sources) != self.config.report.num_top_level_sections:
                error_msg = f"数据源数量不匹配：期望{self.config.report.num_top_level_sections}个，实际{len(state.data_sources)}个"
                state.add_error(error_msg, "setup_and_prepare_sources")
                raise ValueError(error_msg)
            
            # 验证每个路径是否存在
            for idx, source_path in enumerate(state.data_sources):
                if not os.path.exists(source_path):
                    state.add_warning(f"数据源路径不存在: {source_path}")
                elif not os.path.isdir(source_path):
                    state.add_error(f"路径不是目录: {source_path}", "setup_and_prepare_sources")
                    raise ValueError(f"路径不是目录: {source_path}")
            
            state.update_phase("sources_validated")
            
            duration = time.time() - start_time
            self.logger.log_node_complete("setup_and_prepare_sources", duration)
            
        except Exception as e:
            self.logger.log_node_error("setup_and_prepare_sources", str(e))
            raise
        
        return state
    
    def design_framework(self, state: ReportState) -> ReportState:
        """
        Node 2: 设计报告框架
        """
        self.logger.log_node_start("design_framework")
        start_time = time.time()
        
        try:
            # 读取框架模板（如果提供）
            framework_content = ""
            if state.framework_file and os.path.exists(state.framework_file):
                with open(state.framework_file, 'r', encoding='utf-8') as f:
                    framework_content = f.read()
            
            # 构建提示词
            prompt = f"""
你是一位专业的产业研究分析师。请为主题"{state.topic}"设计一份详细的研究报告框架。

要求：
1. 必须包含恰好8个一级标题
2. 每个一级标题下必须完整扩展到六级子标题（例如：1.1.1.1.1.1）
3. 标题层级必须连贯，不能跳级
4. 每个标题都应该有明确的title字段

{f"请参考以下现有框架的风格和结构：{framework_content}" if framework_content else ""}

请以JSON格式返回，结构如下：
{{
    "sections": [
        {{
            "title": "一级标题1",
            "level": 1,
            "children": [
                {{
                    "title": "二级标题1.1",
                    "level": 2,
                    "children": [...]
                }}
            ]
        }}
    ]
}}

确保返回的是有效的JSON格式。
"""
            
            # 调用LLM生成框架
            response = self.llm_client.call_llm(
                prompt=prompt,
                model_type="orchestrator",
                response_format="json"
            )
            
            # 验证响应结构
            if not self._validate_framework_structure(response):
                raise ValueError("生成的框架结构不符合要求")
            
            state.report_structure = response
            state.update_phase("framework_designed")
            
            duration = time.time() - start_time
            self.logger.log_node_complete("design_framework", duration)
            
        except Exception as e:
            self.logger.log_node_error("design_framework", str(e))
            state.add_error(str(e), "design_framework")
            raise
        
        return state
    
    def generate_content_top_down(self, state: ReportState) -> ReportState:
        """
        Node 3: 自上而下生成内容
        """
        self.logger.log_node_start("generate_content_top_down")
        start_time = time.time()
        
        try:
            sections = state.report_structure.get("sections", [])
            
            # 按顺序处理每个一级标题
            for idx, section in enumerate(sections):
                if idx >= len(state.data_sources):
                    state.add_warning(f"章节{idx}没有对应的数据源")
                    continue
                
                # 获取该章节的专属数据源
                source_path = state.data_sources[idx]
                
                # 准备上下文
                context = self.context_manager.prepare_chapter_context(
                    chapter_title=section["title"],
                    source_path=source_path
                )
                
                # 生成该章节的内容
                self._generate_section_content(section, context, state)
            
            state.update_phase("content_generated_top_down")
            state.increment_iteration()
            
            duration = time.time() - start_time
            self.logger.log_node_complete("generate_content_top_down", duration)
            
        except Exception as e:
            self.logger.log_node_error("generate_content_top_down", str(e))
            state.add_error(str(e), "generate_content_top_down")
            # 保存错误时的检查点
            self._save_checkpoint_on_error(state, "generate_content_top_down")
            raise
        
        return state
    
    def review_and_refine_bottom_up(self, state: ReportState) -> ReportState:
        """
        Node 4: 自下而上审核和优化
        """
        self.logger.log_node_start("review_and_refine_bottom_up")
        start_time = time.time()
        
        try:
            sections = state.report_structure.get("sections", [])
            
            # 从最深层级开始，逐层向上优化
            for section in sections:
                self._refine_section_bottom_up(section, state)
            
            state.update_phase("content_refined_bottom_up")
            
            duration = time.time() - start_time
            self.logger.log_node_complete("review_and_refine_bottom_up", duration)
            
        except Exception as e:
            self.logger.log_node_error("review_and_refine_bottom_up", str(e))
            state.add_error(str(e), "review_and_refine_bottom_up")
            # 保存错误时的检查点
            self._save_checkpoint_on_error(state, "review_and_refine_bottom_up")
            raise
        
        return state
    
    def final_review_and_structured_output(self, state: ReportState) -> ReportState:
        """
        Node 5: 最终审核和结构化输出
        """
        self.logger.log_node_start("final_review_and_structured_output")
        start_time = time.time()
        
        try:
            # 构建完整的报告内容用于最终审核
            report_json = json.dumps(state.report_structure, ensure_ascii=False, indent=2)
            
            prompt = f"""
你是一位资深的报告编辑。请对以下报告进行最终审核和优化。

任务要求：
1. 确保内容的连贯性和逻辑性
2. 消除不同章节间的重复内容
3. 统一专业术语和表述风格
4. 保持所有引用标记[来源: xxx]不变
5. 必须保持与输入完全相同的JSON结构，只修改content字段的值

当前报告内容：
{report_json}

请返回优化后的完整JSON结构。
"""
            
            # 调用LLM进行最终审核
            response = self.llm_client.call_llm(
                prompt=prompt,
                model_type="orchestrator",
                response_format="json"
            )
            
            state.report_structure = response
            state.update_phase("final_review_completed")
            
            # 提取所有引用来源
            self._extract_all_citations(state)
            
            duration = time.time() - start_time
            self.logger.log_node_complete("final_review_and_structured_output", duration)
            
        except Exception as e:
            self.logger.log_node_error("final_review_and_structured_output", str(e))
            state.add_error(str(e), "final_review_and_structured_output")
            # 保存错误时的检查点
            self._save_checkpoint_on_error(state, "final_review_and_structured_output")
            raise
        
        return state
    
    def _validate_framework_structure(self, framework: Dict[str, Any]) -> bool:
        """
        验证框架结构是否符合要求
        """
        if "sections" not in framework:
            return False
        
        sections = framework["sections"]
        if len(sections) != self.config.report.num_top_level_sections:
            return False
        
        # 随机抽取验证深度
        import random
        sample_sections = random.sample(sections, min(2, len(sections)))
        
        for section in sample_sections:
            if not self._check_depth(section, 1, self.config.report.max_heading_depth):
                return False
        
        return True
    
    def _check_depth(self, node: Dict[str, Any], current_level: int, target_depth: int) -> bool:
        """
        递归检查节点深度
        """
        if current_level == target_depth:
            return "children" not in node or len(node.get("children", [])) == 0
        
        if "children" not in node or not node["children"]:
            return False
        
        for child in node["children"]:
            if not self._check_depth(child, current_level + 1, target_depth):
                return False
        
        return True
    
    def _generate_section_content(self, section: Dict[str, Any], context: Dict[str, Any], state: ReportState, depth: int = 1):
        """
        递归生成章节内容
        """
        # 生成当前节点的内容
        section_key = self._get_section_key(section)
        
        if "parts" in context and context["parts"]:
            # 根据章节层级调整提示词
            if depth == 1:
                prompt = f"基于提供的所有资料，为一级章节'{section['title']}'生成综合性的概述内容。请确保内容涵盖该章节的核心要点。"
            elif depth <= 3:
                prompt = f"基于提供的资料，为'{section['title']}'章节生成详细的分析内容。"
            else:
                prompt = f"基于提供的资料，为'{section['title']}'子章节生成具体的说明内容。"
            
            content = self.llm_client.call_llm(
                prompt=prompt,
                context=context,
                model_type="executor",
                response_format="text"
            )
            
            section["content"] = content
            state.generated_content[section_key] = content
        else:
            section["content"] = "根据指定数据源，未找到相关信息。"
            state.generated_content[section_key] = section["content"]
        
        # 递归处理子节点 - 使用相同的上下文
        if "children" in section:
            for child in section["children"]:
                child["level"] = depth + 1  # 确保层级正确
                self._generate_section_content(child, context, state, depth + 1)
    
    def _refine_section_bottom_up(self, section: Dict[str, Any], state: ReportState):
        """
        自下而上优化章节内容
        """
        # 先处理所有子节点
        if "children" in section and section["children"]:
            for child in section["children"]:
                self._refine_section_bottom_up(child, state)
            
            # 基于子节点内容优化父节点
            children_contents = [child.get("content", "") for child in section["children"]]
            if any(children_contents):
                prompt = f"""
基于以下子章节内容，优化父章节"{section['title']}"的内容，使其更好地概括和引导子章节：

子章节内容：
{chr(10).join(children_contents)}

当前父章节内容：
{section.get('content', '')}

请提供优化后的父章节内容。
"""
                
                refined_content = self.llm_client.call_llm(
                    prompt=prompt,
                    model_type="executor",
                    response_format="text"
                )
                
                section["content"] = refined_content
                section_key = self._get_section_key(section)
                state.generated_content[section_key] = refined_content
    
    def _get_section_key(self, section: Dict[str, Any]) -> str:
        """
        生成章节的唯一键
        """
        return f"level_{section.get('level', 0)}_{section.get('title', '')}"
    
    def _extract_all_citations(self, state: ReportState):
        """
        提取所有引用来源
        """
        citation_pattern = re.compile(self.config.report.citation_pattern)
        
        def extract_from_section(section: Dict[str, Any]):
            content = section.get("content", "")
            citations = citation_pattern.findall(content)
            
            section_key = self._get_section_key(section)
            if citations:
                state.source_citations[section_key] = citations
            
            # 递归处理子节点
            if "children" in section:
                for child in section["children"]:
                    extract_from_section(child)
        
        sections = state.report_structure.get("sections", [])
        for section in sections:
            extract_from_section(section)
