"""
测试Token限制功能
验证分批处理和Token管理
"""
import asyncio
import sys
from pathlib import Path

def test_token_manager():
    """测试Token管理器"""
    print("🧪 测试Token管理器")
    print("=" * 60)
    
    try:
        from complete_report_generator import TokenManager
        
        # 测试不同的Token限制
        test_cases = [
            (250000, "默认限制"),
            (100000, "较小限制"),
            (500000, "较大限制")
        ]
        
        for max_tokens, description in test_cases:
            print(f"\n📊 测试 {description} ({max_tokens:,} tokens)")
            
            manager = TokenManager(max_tokens)
            
            # 测试短文本
            short_text = "这是一个短文本测试。" * 100
            short_info = manager.get_token_info(short_text)
            print(f"   短文本: {short_info['estimated_tokens']:,} tokens, 需要分批: {short_info['needs_splitting']}")
            
            # 测试长文本
            long_text = "这是一个很长的文本测试，用于验证Token限制功能。" * 10000
            long_info = manager.get_token_info(long_text)
            print(f"   长文本: {long_info['estimated_tokens']:,} tokens, 需要分批: {long_info['needs_splitting']}")
            
            if long_info['needs_splitting']:
                chunks = manager.split_text_by_tokens(long_text)
                print(f"   分割结果: {len(chunks)} 个块")
                
                # 验证每个块的大小
                for i, chunk in enumerate(chunks):
                    chunk_tokens = manager.estimate_tokens(chunk)
                    print(f"      块 {i+1}: {chunk_tokens:,} tokens")
        
        print(f"\n✅ Token管理器测试通过")
        return True
        
    except Exception as e:
        print(f"❌ Token管理器测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

async def test_token_limit_api_calls():
    """测试带Token限制的API调用"""
    print("\n🧪 测试带Token限制的API调用")
    print("=" * 60)
    
    try:
        from complete_report_generator import CompleteReportGenerator
        
        # 创建生成器，设置较小的Token限制用于测试
        generator = CompleteReportGenerator(use_async=True, max_tokens=50000)
        
        # 测试短文本（不需要分批）
        short_prompt = "请简要介绍人工智能的发展历程。"
        print(f"📝 测试短文本: {short_prompt}")
        
        try:
            result = await generator.generate_content_with_token_limit_async(
                short_prompt, "gemini-2.5-flash"
            )
            print(f"✅ 短文本处理成功，结果长度: {len(result)} 字符")
        except Exception as e:
            print(f"❌ 短文本处理失败: {str(e)}")
            return False
        
        # 测试长文本（需要分批）
        long_prompt = """
        请详细分析以下内容并生成完整的报告：
        """ + "人工智能技术在各个领域的应用和发展前景分析。" * 2000
        
        print(f"\n📝 测试长文本 (约 {len(long_prompt)} 字符)")
        
        try:
            result = await generator.generate_content_with_token_limit_async(
                long_prompt, "gemini-2.5-flash"
            )
            print(f"✅ 长文本处理成功，结果长度: {len(result)} 字符")
        except Exception as e:
            print(f"❌ 长文本处理失败: {str(e)}")
            return False
        
        print(f"\n✅ 带Token限制的API调用测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 带Token限制的API调用测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_token_limit_sync():
    """测试同步版本的Token限制"""
    print("\n🧪 测试同步版本的Token限制")
    print("=" * 60)
    
    try:
        from complete_report_generator import CompleteReportGenerator
        
        # 创建同步生成器
        generator = CompleteReportGenerator(use_async=False, max_tokens=50000)
        
        # 测试短文本
        short_prompt = "请简要介绍机器学习的基本概念。"
        print(f"📝 测试短文本: {short_prompt}")
        
        try:
            result = generator.generate_content_with_token_limit_sync(
                short_prompt, "gemini-2.5-flash"
            )
            print(f"✅ 同步短文本处理成功，结果长度: {len(result)} 字符")
        except Exception as e:
            print(f"❌ 同步短文本处理失败: {str(e)}")
            return False
        
        print(f"\n✅ 同步版本Token限制测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 同步版本Token限制测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_token_calculation_examples():
    """测试Token计算示例"""
    print("\n🧪 测试Token计算示例")
    print("=" * 60)
    
    try:
        from complete_report_generator import TokenManager
        import math
        
        manager = TokenManager(250000)
        
        # 测试用户提到的示例
        test_cases = [
            (330000, "330,000 tokens示例"),
            (560000, "560,000 tokens示例"),
            (100000, "100,000 tokens示例"),
            (750000, "750,000 tokens示例")
        ]
        
        print(f"Token限制: {manager.max_tokens:,}")
        print(f"分批计算公式: math.ceil(tokens / max_tokens)")
        
        for tokens, description in test_cases:
            expected_batches = math.ceil(tokens / manager.max_tokens)
            
            # 创建模拟文本（简单估算）
            # 假设平均1个token = 1.5个中文字符
            simulated_text = "测试文本内容" * (tokens // 6)  # 6个字符约4个token
            
            actual_info = manager.get_token_info(simulated_text)
            actual_batches = actual_info['batches_needed']
            
            print(f"\n📊 {description}:")
            print(f"   预期tokens: {tokens:,}")
            print(f"   预期批次: {expected_batches}")
            print(f"   实际估算tokens: {actual_info['estimated_tokens']:,}")
            print(f"   实际批次: {actual_batches}")
            print(f"   计算正确: {'✅' if actual_batches == expected_batches else '⚠️'}")
        
        print(f"\n✅ Token计算示例测试完成")
        return True
        
    except Exception as e:
        print(f"❌ Token计算示例测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_user_interface():
    """测试用户接口"""
    print("\n🧪 测试用户接口")
    print("=" * 60)
    
    try:
        from complete_report_generator import get_user_inputs
        import inspect
        
        # 检查函数签名
        source = inspect.getsource(get_user_inputs)
        
        if "Token限制配置" in source:
            print("✅ 用户接口包含Token限制配置")
        else:
            print("❌ 用户接口缺少Token限制配置")
            return False
        
        if "max_tokens" in source:
            print("✅ 用户接口包含max_tokens变量")
        else:
            print("❌ 用户接口缺少max_tokens变量")
            return False
        
        if "分批处理示例" in source:
            print("✅ 用户接口包含分批处理示例")
        else:
            print("❌ 用户接口缺少分批处理示例")
            return False
        
        print(f"✅ 用户接口测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 用户接口测试失败: {str(e)}")
        return False

async def main():
    """主测试函数"""
    print("🚀 Token限制功能测试")
    print("=" * 60)
    
    tests = [
        ("Token管理器", test_token_manager, False),
        ("Token限制API调用", test_token_limit_api_calls, True),
        ("同步版本Token限制", test_token_limit_sync, False),
        ("Token计算示例", test_token_calculation_examples, False),
        ("用户接口", test_user_interface, False)
    ]
    
    results = []
    
    for test_name, test_func, is_async in tests:
        try:
            print(f"\n{'='*60}")
            
            if is_async:
                result = await test_func()
            else:
                result = test_func()
            
            results.append((test_name, result))
            
            if result:
                print(f"✅ {test_name}: 测试通过")
            else:
                print(f"❌ {test_name}: 测试失败")
                
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {str(e)}")
            results.append((test_name, False))
    
    # 总结
    print(f"\n📊 Token限制功能测试总结:")
    print("=" * 60)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {test_name}: {status}")
    
    print(f"\n🎯 总体结果: {passed}/{total} 个测试通过")
    
    if passed == total:
        print(f"\n🎉 Token限制功能测试全部通过！")
        print(f"\n💡 功能特性:")
        print(f"   1. ✅ 智能Token估算和分批处理")
        print(f"   2. ✅ 支持异步和同步两种模式")
        print(f"   3. ✅ 用户可配置Token限制（默认250,000）")
        print(f"   4. ✅ 自动分批处理超长内容")
        print(f"   5. ✅ 详细的处理状态显示")
        print(f"   6. ✅ 完整的错误处理机制")
    else:
        print(f"\n💡 如果测试失败，请检查:")
        print(f"   1. API密钥配置是否正确")
        print(f"   2. 网络连接是否稳定")
        print(f"   3. 是否有API配额限制")
    
    return passed == total

if __name__ == "__main__":
    try:
        success = asyncio.run(main())
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\n❌ 用户中断测试")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 测试程序错误: {str(e)}")
        sys.exit(1)
