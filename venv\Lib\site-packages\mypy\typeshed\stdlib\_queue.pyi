from types import GenericAlias
from typing import Any, Generic, TypeVar

_T = TypeVar("_T")

class Empty(Exception): ...

class SimpleQueue(Generic[_T]):
    def __init__(self) -> None: ...
    def empty(self) -> bool: ...
    def get(self, block: bool = True, timeout: float | None = None) -> _T: ...
    def get_nowait(self) -> _T: ...
    def put(self, item: _T, block: bool = True, timeout: float | None = None) -> None: ...
    def put_nowait(self, item: _T) -> None: ...
    def qsize(self) -> int: ...
    def __class_getitem__(cls, item: Any, /) -> GenericAlias: ...
