#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
报告优化模块
包含所有优化相关的功能
"""

import asyncio
import time
from typing import List, Dict, Any, Optional
from pathlib import Path

try:
    from .config import MODEL_NAMES
except ImportError:
    from config import MODEL_NAMES


class ReportOptimizer:
    """报告优化器"""
    
    def __init__(self, generator):
        self.generator = generator
    
    def _enhance_content_quality(self, framework: Dict[str, Any], topic: str) -> Dict[str, Any]:
        """第三轮：质量提升优化"""
        print("     ✨ 质量提升优化")
        
        sections = framework.get("sections", [])
        
        for i, section in enumerate(sections):
            print(f"     📝 提升第{i+1}个章节质量: {section.get('title', '')}")
            section = self._enhance_section_quality(section, topic)
            sections[i] = section
        
        framework["sections"] = sections
        return framework

    async def _enhance_content_quality_async(self, framework: Dict[str, Any], topic: str) -> Dict[str, Any]:
        """第三轮：异步质量提升优化"""
        print("     ✨ 异步质量提升优化")
        
        sections = framework.get("sections", [])
        
        # 创建异步任务
        tasks = []
        for i, section in enumerate(sections):
            task = self._enhance_section_quality_async(section, topic, i+1)
            tasks.append(task)
        
        # 并行执行
        enhanced_sections = await asyncio.gather(*tasks)
        
        framework["sections"] = enhanced_sections
        return framework

    def _enhance_section_quality(self, section: Dict[str, Any], topic: str) -> Dict[str, Any]:
        """提升单个章节的质量"""
        title = section.get("title", "")
        content = section.get("content", "")
        
        prompt = f"""
您是一位资深的产业研究专家，需要提升"{topic}"研究报告中"{title}"章节的内容质量。

当前内容：
{content}

要求：
1. 提升内容的专业性和深度
2. 增加具体的数据、案例和分析
3. 优化表述方式，使其更加准确和生动
4. 确保内容的权威性和可信度
5. 保持原有的结构和逻辑

请输出质量提升后的内容：
"""
        
        try:
            enhanced_content = self.generator.call_orchestrator_model(prompt)
            if enhanced_content:
                section["content"] = self.generator.content_cleaner.clean_content_thoroughly(enhanced_content)
        except Exception as e:
            print(f"⚠️ 质量提升失败 {title}: {str(e)}")
        
        # 递归处理子章节
        children = section.get("children", [])
        for i, child in enumerate(children):
            children[i] = self._enhance_section_quality(child, topic)
        
        section["children"] = children
        return section

    async def _enhance_section_quality_async(self, section: Dict[str, Any], topic: str, section_num: int) -> Dict[str, Any]:
        """异步提升单个章节的质量"""
        print(f"     📝 异步提升第{section_num}个章节质量: {section.get('title', '')}")
        
        title = section.get("title", "")
        content = section.get("content", "")
        
        prompt = f"""
您是一位资深的产业研究专家，需要提升"{topic}"研究报告中"{title}"章节的内容质量。

当前内容：
{content}

要求：
1. 提升内容的专业性和深度
2. 增加具体的数据、案例和分析
3. 优化表述方式，使其更加准确和生动
4. 确保内容的权威性和可信度
5. 保持原有的结构和逻辑

请输出质量提升后的内容：
"""
        
        try:
            enhanced_content = await self.generator.call_orchestrator_model_async(prompt)
            if enhanced_content:
                section["content"] = self.generator.content_cleaner.clean_content_thoroughly(enhanced_content)
        except Exception as e:
            print(f"⚠️ 异步质量提升失败 {title}: {str(e)}")
        
        # 递归处理子章节
        children = section.get("children", [])
        child_tasks = []
        for child in children:
            task = self._enhance_section_quality_async(child, topic, section_num)
            child_tasks.append(task)
        
        if child_tasks:
            enhanced_children = await asyncio.gather(*child_tasks)
            section["children"] = enhanced_children
        
        return section

    def _optimize_with_reference_report(self, framework: Dict[str, Any], reference_path: str, topic: str) -> Dict[str, Any]:
        """基于参考报告进行优化"""
        print(f"     📚 基于参考报告优化: {reference_path}")
        
        try:
            # 读取参考报告
            reference_content = self._read_reference_report(reference_path)
            if not reference_content:
                print("⚠️ 参考报告读取失败，跳过参考优化")
                return framework
            
            # 分析参考报告
            reference_analysis = self._analyze_reference_report(reference_content, topic)
            
            # 应用参考学习
            framework = self._apply_reference_learning(framework, reference_analysis, topic)
            
            return framework
            
        except Exception as e:
            print(f"❌ 参考报告优化失败: {str(e)}")
            return framework

    def _read_reference_report(self, reference_path: str) -> str:
        """读取参考报告"""
        try:
            ref_path = Path(reference_path)
            if not ref_path.exists():
                return ""
            
            # 根据文件类型读取
            if ref_path.suffix.lower() == '.pdf':
                # PDF文件处理
                return self.generator._process_pdf_with_images(ref_path)["text"]
            else:
                # 其他文件类型
                from utils.file_reader import FileReader
                file_reader = FileReader()
                return file_reader.read_file(str(ref_path))
                
        except Exception as e:
            print(f"⚠️ 读取参考报告失败: {str(e)}")
            return ""

    def _analyze_reference_report(self, reference_content: str, topic: str) -> Dict[str, Any]:
        """分析参考报告"""
        prompt = f"""
您是一位资深的产业研究专家，需要分析一份关于"{topic}"的参考报告，提取其优秀的写作特点和结构特征。

参考报告内容：
{reference_content[:8000]}  # 限制长度

要求：
1. 分析报告的结构特点
2. 提取优秀的表述方式
3. 识别专业的分析方法
4. 总结可借鉴的写作技巧
5. 返回JSON格式的分析结果

请返回以下JSON格式：
{{
    "structure_features": ["结构特点1", "结构特点2"],
    "writing_style": ["写作特点1", "写作特点2"],
    "analysis_methods": ["分析方法1", "分析方法2"],
    "key_insights": ["关键见解1", "关键见解2"]
}}
"""
        
        try:
            response = self.generator.call_orchestrator_model(prompt)
            analysis = self.generator._parse_framework_response(response)
            
            if not analysis:
                # 返回默认分析
                analysis = {
                    "structure_features": ["逻辑清晰", "层次分明"],
                    "writing_style": ["专业严谨", "数据支撑"],
                    "analysis_methods": ["定量分析", "定性分析"],
                    "key_insights": ["市场趋势", "技术发展"]
                }
            
            return analysis
            
        except Exception as e:
            print(f"⚠️ 参考报告分析失败: {str(e)}")
            return {}

    def _apply_reference_learning(self, framework: Dict[str, Any], reference_analysis: Dict[str, Any], topic: str) -> Dict[str, Any]:
        """应用参考学习"""
        if not reference_analysis:
            return framework
        
        sections = framework.get("sections", [])
        
        for i, section in enumerate(sections):
            print(f"     📝 应用参考学习到第{i+1}个章节: {section.get('title', '')}")
            section = self._apply_reference_to_section(section, reference_analysis, topic)
            sections[i] = section
        
        framework["sections"] = sections
        return framework

    def _apply_reference_to_section(self, section: Dict[str, Any], reference_analysis: Dict[str, Any], topic: str) -> Dict[str, Any]:
        """将参考学习应用到章节"""
        title = section.get("title", "")
        content = section.get("content", "")
        
        # 提取参考特征
        structure_features = reference_analysis.get("structure_features", [])
        writing_style = reference_analysis.get("writing_style", [])
        analysis_methods = reference_analysis.get("analysis_methods", [])
        
        prompt = f"""
您是一位资深的产业研究专家，需要基于参考报告的优秀特征来优化"{topic}"研究报告中"{title}"章节的内容。

当前内容：
{content}

参考报告的优秀特征：
- 结构特点：{', '.join(structure_features)}
- 写作风格：{', '.join(writing_style)}
- 分析方法：{', '.join(analysis_methods)}

要求：
1. 借鉴参考报告的优秀特征
2. 保持内容的原创性和准确性
3. 提升内容的专业性和可读性
4. 不改变核心观点和主要内容
5. 确保与整体报告风格一致

请输出优化后的内容：
"""
        
        try:
            optimized_content = self.generator.call_orchestrator_model(prompt)
            if optimized_content:
                section["content"] = self.generator.content_cleaner.clean_content_thoroughly(optimized_content)
        except Exception as e:
            print(f"⚠️ 参考学习应用失败 {title}: {str(e)}")
        
        # 递归处理子章节
        children = section.get("children", [])
        for i, child in enumerate(children):
            children[i] = self._apply_reference_to_section(child, reference_analysis, topic)
        
        section["children"] = children
        return section

    def _control_final_word_count(self, framework: Dict[str, Any], target_words: int, topic: str) -> Dict[str, Any]:
        """字数控制"""
        print(f"     📊 字数控制，目标: {target_words:,} 字")
        
        # 统计当前字数
        current_words = self._count_total_words(framework)
        print(f"     📊 当前字数: {current_words:,} 字")
        
        if abs(current_words - target_words) > target_words * 0.1:  # 超过10%差异
            if current_words < target_words * 0.9:
                # 字数不足，需要扩展
                print("     📈 字数不足，进行扩展")
                framework = self._expand_to_target_words(framework, target_words, topic)
            elif current_words > target_words * 1.1:
                # 字数过多，需要压缩
                print("     📉 字数过多，进行压缩")
                framework = self._compress_to_target_words(framework, target_words, topic)
        
        # 最终统计
        final_words = self._count_total_words(framework)
        print(f"     ✅ 最终字数: {final_words:,} 字")
        
        return framework

    async def _control_final_word_count_async(self, framework: Dict[str, Any], target_words: int, topic: str) -> Dict[str, Any]:
        """异步字数控制"""
        print(f"     📊 异步字数控制，目标: {target_words:,} 字")
        
        # 统计当前字数
        current_words = self._count_total_words(framework)
        print(f"     📊 当前字数: {current_words:,} 字")
        
        if abs(current_words - target_words) > target_words * 0.1:  # 超过10%差异
            if current_words < target_words * 0.9:
                # 字数不足，需要扩展
                print("     📈 字数不足，进行异步扩展")
                framework = await self._expand_to_target_words_async(framework, target_words, topic)
            elif current_words > target_words * 1.1:
                # 字数过多，需要压缩
                print("     📉 字数过多，进行异步压缩")
                framework = await self._compress_to_target_words_async(framework, target_words, topic)
        
        # 最终统计
        final_words = self._count_total_words(framework)
        print(f"     ✅ 最终字数: {final_words:,} 字")
        
        return framework

    def _count_total_words(self, framework: Dict[str, Any]) -> int:
        """统计总字数"""
        total_words = 0
        
        def count_recursive(node):
            nonlocal total_words
            content = node.get("content", "")
            total_words += len(content)
            
            for child in node.get("children", []):
                count_recursive(child)
        
        for section in framework.get("sections", []):
            count_recursive(section)
        
        return total_words
