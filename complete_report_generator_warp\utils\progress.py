from tqdm.asyncio import tqdm as async_tqdm

class ProgressBar:
    """
    进度条管理 - 支持异步函数中的tqdm
    """
    def __init__(self, total: int, desc: str = "", **kwargs):
        self.desc = desc
        self.total = total
        self.pbar = None
        self.kwargs = kwargs

    async def __aenter__(self):
        self.pbar = async_tqdm(total=self.total, desc=self.desc, **self.kwargs)
        return self.pbar

    async def __aexit__(self, exc_type, exc_value, traceback):
        if self.pbar:
            self.pbar.close()
        
    async def update(self, n: int):
        if self.pbar:
            self.pbar.update(n)
