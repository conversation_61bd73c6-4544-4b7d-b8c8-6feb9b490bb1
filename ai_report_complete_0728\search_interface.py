#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
联网搜索接口
简单易用的搜索入口，输入内容即开启搜索
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from core.web_search_module import WebSearchModule
from core.smart_search_assistant import SmartSearchAssistant


class SearchInterface:
    """
    联网搜索接口
    
    提供简单易用的搜索功能：
    1. 基础搜索：输入内容即搜索
    2. 智能搜索：自动分析搜索意图
    3. 增强搜索：基于内容缺口搜索
    4. 批量搜索：多个查询批量处理
    """

    def __init__(self):
        """初始化搜索接口"""
        print("🔍 初始化联网搜索接口...")
        
        try:
            self.web_search = WebSearchModule()
            self.smart_assistant = SmartSearchAssistant()
            print("✅ 联网搜索接口初始化成功")
            self._print_welcome_message()
        except Exception as e:
            print(f"❌ 搜索接口初始化失败: {str(e)}")
            raise

    def _print_welcome_message(self):
        """打印欢迎信息"""
        print("\n" + "="*60)
        print("🔍 联网搜索系统已就绪")
        print("="*60)
        print("📋 可用功能:")
        print("   • search(content)           - 基础搜索")
        print("   • smart_search(content)     - 智能搜索")
        print("   • enhance_content(content)  - 内容增强搜索")
        print("   • batch_search(queries)     - 批量搜索")
        print("   • test_apis()              - 测试搜索API")
        print("="*60)

    # ==================== 主要搜索方法 ====================

    def search(self, content: str, num_results: int = 10, engine: str = "auto") -> list:
        """
        基础搜索（主要入口）
        输入内容即开启搜索
        
        Args:
            content: 搜索内容
            num_results: 结果数量
            engine: 搜索引擎 ("auto", "metaso", "google", "bing")
            
        Returns:
            搜索结果列表
        """
        if not content.strip():
            print("❌ 搜索内容不能为空")
            return []

        print(f"\n🔍 开始搜索: {content}")
        print("-" * 50)
        
        try:
            results = self.web_search.search(content, num_results, engine)
            
            if results:
                print(f"✅ 搜索完成，找到 {len(results)} 个结果")
                self._print_search_results(results[:5])  # 显示前5个结果
            else:
                print("❌ 未找到相关结果")
            
            return results
            
        except Exception as e:
            print(f"❌ 搜索失败: {str(e)}")
            return []

    def smart_search(self, content: str, search_type: str = "auto") -> dict:
        """
        智能搜索
        自动分析搜索意图并优化搜索策略
        
        Args:
            content: 搜索内容
            search_type: 搜索类型
            
        Returns:
            包含搜索结果和分析的字典
        """
        if not content.strip():
            print("❌ 搜索内容不能为空")
            return {}

        print(f"\n🤖 智能搜索: {content}")
        print("-" * 50)
        
        try:
            result = self.smart_assistant.intelligent_search(content, search_type)
            
            if result and result.get('analysis', {}).get('filtered_results'):
                analysis = result['analysis']
                print(f"✅ 智能搜索完成")
                print(f"   搜索类型: {result['search_type']}")
                print(f"   质量分数: {analysis['quality_score']:.1f}/100")
                print(f"   相关性分数: {analysis['relevance_score']:.1f}/100")
                print(f"   时效性分数: {analysis['freshness_score']:.1f}/100")
                print(f"   高质量结果: {len(analysis['filtered_results'])} 个")
                
                self._print_search_results(analysis['filtered_results'][:3])
                
                if result.get('recommendations'):
                    print("\n💡 搜索建议:")
                    for rec in result['recommendations']:
                        print(f"   • {rec}")
            else:
                print("❌ 智能搜索未找到相关结果")
            
            return result
            
        except Exception as e:
            print(f"❌ 智能搜索失败: {str(e)}")
            return {}

    def enhance_content(self, content: str, topic: str) -> dict:
        """
        内容增强搜索
        基于内容缺口进行搜索增强
        
        Args:
            content: 现有内容
            topic: 主题
            
        Returns:
            增强建议和搜索结果
        """
        if not content.strip() or not topic.strip():
            print("❌ 内容和主题都不能为空")
            return {}

        print(f"\n🔍 内容增强搜索: {topic}")
        print("-" * 50)
        
        try:
            result = self.smart_assistant.enhance_content_with_search(content, topic)
            
            if result['status'] == 'no_gaps':
                print("✅ 内容已较为完整，无需搜索增强")
            elif result['status'] == 'enhanced':
                gaps = result['gaps']
                print(f"✅ 发现 {len(gaps)} 个内容缺口，已完成搜索增强")
                
                for i, gap in enumerate(gaps, 1):
                    print(f"\n   缺口 {i}: {gap['type']}")
                    print(f"   原因: {gap['reason']}")
                    print(f"   优先级: {gap['priority']}")
            
            return result
            
        except Exception as e:
            print(f"❌ 内容增强搜索失败: {str(e)}")
            return {}

    def batch_search(self, queries: list, search_type: str = "auto") -> list:
        """
        批量搜索
        
        Args:
            queries: 搜索查询列表
            search_type: 搜索类型
            
        Returns:
            搜索结果列表
        """
        if not queries:
            print("❌ 查询列表不能为空")
            return []

        print(f"\n📦 批量搜索: {len(queries)} 个查询")
        print("-" * 50)
        
        try:
            results = self.smart_assistant.batch_search(queries, search_type)
            
            print(f"✅ 批量搜索完成，处理了 {len(results)} 个查询")
            
            # 显示摘要
            for i, result in enumerate(results, 1):
                analysis = result.get('analysis', {})
                filtered_count = len(analysis.get('filtered_results', []))
                print(f"   查询 {i}: {filtered_count} 个高质量结果")
            
            return results
            
        except Exception as e:
            print(f"❌ 批量搜索失败: {str(e)}")
            return []

    # ==================== 便捷搜索方法 ====================

    def search_latest(self, topic: str) -> dict:
        """搜索最新信息"""
        return self.smart_search(f"{topic} 最新", "latest_data")

    def search_market(self, topic: str) -> dict:
        """搜索市场信息"""
        return self.smart_search(f"{topic} 市场", "market_analysis")

    def search_tech(self, topic: str) -> dict:
        """搜索技术信息"""
        return self.smart_search(f"{topic} 技术", "technology_trends")

    def search_policy(self, topic: str) -> dict:
        """搜索政策信息"""
        return self.smart_search(f"{topic} 政策", "policy_regulations")

    # ==================== 工具方法 ====================

    def test_apis(self) -> dict:
        """测试所有搜索API"""
        print("\n🧪 测试搜索API...")
        print("-" * 50)
        
        try:
            results = self.web_search.test_all_apis()
            
            print("📊 API测试结果:")
            for engine, status in results.items():
                print(f"   {engine}: {'✅ 正常' if status else '❌ 异常'}")
            
            working_apis = sum(1 for status in results.values() if status)
            print(f"\n📈 可用API: {working_apis}/{len(results)}")
            
            return results
            
        except Exception as e:
            print(f"❌ API测试失败: {str(e)}")
            return {}

    def get_status(self) -> dict:
        """获取搜索系统状态"""
        try:
            api_status = self.web_search.get_api_status()
            search_stats = self.smart_assistant.get_search_statistics()
            
            return {
                'api_status': api_status,
                'search_statistics': search_stats
            }
        except Exception as e:
            print(f"❌ 获取状态失败: {str(e)}")
            return {}

    def _print_search_results(self, results: list):
        """打印搜索结果"""
        if not results:
            return
        
        print("\n📋 搜索结果:")
        for i, result in enumerate(results, 1):
            title = result.get('title', '无标题')[:60]
            url = result.get('url', '无链接')
            snippet = result.get('snippet', '无摘要')[:100]
            source = result.get('source', '未知')
            
            print(f"\n   {i}. {title}")
            print(f"      来源: {source}")
            print(f"      链接: {url}")
            print(f"      摘要: {snippet}...")

    def help(self):
        """显示帮助信息"""
        print("\n📖 联网搜索系统帮助")
        print("="*60)
        print("🔍 基础搜索:")
        print("   search('人工智能')                    # 基础搜索")
        print("   search('AI技术', num_results=20)      # 指定结果数量")
        print("   search('机器学习', engine='google')    # 指定搜索引擎")
        print()
        print("🤖 智能搜索:")
        print("   smart_search('人工智能最新发展')       # 自动分析搜索意图")
        print("   smart_search('AI市场', 'market_analysis')  # 指定搜索类型")
        print()
        print("🔍 内容增强:")
        print("   enhance_content(content, 'AI报告')    # 基于内容缺口搜索")
        print()
        print("📦 批量搜索:")
        print("   batch_search(['AI', '机器学习', '深度学习'])  # 批量处理")
        print()
        print("🛠️ 工具方法:")
        print("   test_apis()                          # 测试API状态")
        print("   get_status()                         # 获取系统状态")
        print("   help()                               # 显示帮助")
        print("="*60)


# ==================== 全局搜索实例 ====================

# 创建全局搜索实例
try:
    search_interface = SearchInterface()
    
    # 导出便捷函数
    def search(content: str, num_results: int = 10, engine: str = "auto") -> list:
        """快速搜索函数"""
        return search_interface.search(content, num_results, engine)
    
    def smart_search(content: str, search_type: str = "auto") -> dict:
        """智能搜索函数"""
        return search_interface.smart_search(content, search_type)
    
    def enhance_content(content: str, topic: str) -> dict:
        """内容增强函数"""
        return search_interface.enhance_content(content, topic)
    
    def search_latest(topic: str) -> dict:
        """搜索最新信息"""
        return search_interface.search_latest(topic)
    
    def search_market(topic: str) -> dict:
        """搜索市场信息"""
        return search_interface.search_market(topic)
    
    def test_search_apis() -> dict:
        """测试搜索API"""
        return search_interface.test_apis()

except Exception as e:
    print(f"❌ 搜索接口初始化失败: {str(e)}")
    search_interface = None


# ==================== 命令行接口 ====================

def main():
    """命令行主函数"""
    if not search_interface:
        print("❌ 搜索接口未初始化，无法使用")
        return
    
    print("🔍 联网搜索命令行界面")
    print("输入 'help' 查看帮助，输入 'quit' 退出")
    
    while True:
        try:
            user_input = input("\n🔍 请输入搜索内容: ").strip()
            
            if not user_input:
                continue
            elif user_input.lower() in ['quit', 'exit', '退出']:
                print("👋 再见！")
                break
            elif user_input.lower() == 'help':
                search_interface.help()
            elif user_input.lower() == 'test':
                search_interface.test_apis()
            elif user_input.lower() == 'status':
                status = search_interface.get_status()
                print(f"📊 系统状态: {status}")
            else:
                # 执行搜索
                search_interface.search(user_input)
                
        except KeyboardInterrupt:
            print("\n👋 再见！")
            break
        except Exception as e:
            print(f"❌ 发生错误: {str(e)}")


if __name__ == "__main__":
    main()
