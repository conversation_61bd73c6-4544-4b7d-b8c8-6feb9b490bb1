"""
检查点管理器 - 严格按照原始代码的CheckpointManager实现
处理报告生成过程中的进度保存和恢复
"""

import os
import json
import time
from pathlib import Path
from typing import Dict, Any, Optional, List
from datetime import datetime
from ..config import CHECKPOINT_DIR, ENABLE_CHECKPOINTS


class CheckpointManager:
    """检查点管理器 - 与原始代码完全相同"""
    
    def __init__(self, report_id: str):
        """
        初始化检查点管理器
        
        Args:
            report_id: 报告ID，用于唯一标识
        """
        self.report_id = report_id
        self.checkpoint_dir = Path(CHECKPOINT_DIR) / report_id
        self.checkpoint_dir.mkdir(parents=True, exist_ok=True)
        self.current_checkpoint = None
    
    def save_checkpoint(self, data: Dict[str, Any], checkpoint_name: str = None) -> str:
        """
        保存检查点 - 与原始代码完全相同
        
        Args:
            data: 要保存的数据
            checkpoint_name: 检查点名称（可选）
            
        Returns:
            检查点文件路径
        """
        if not ENABLE_CHECKPOINTS:
            return ""
        
        if checkpoint_name is None:
            checkpoint_name = f"checkpoint_{int(time.time())}"
        
        checkpoint_file = self.checkpoint_dir / f"{checkpoint_name}.json"
        
        checkpoint_data = {
            "report_id": self.report_id,
            "timestamp": datetime.now().isoformat(),
            "checkpoint_name": checkpoint_name,
            "data": data
        }
        
        try:
            with open(checkpoint_file, 'w', encoding='utf-8') as f:
                json.dump(checkpoint_data, f, ensure_ascii=False, indent=2)
            
            self.current_checkpoint = checkpoint_name
            print(f"   💾 检查点已保存: {checkpoint_file}")
            return str(checkpoint_file)
            
        except Exception as e:
            print(f"   ❌ 保存检查点失败: {str(e)}")
            return ""
    
    def load_checkpoint(self, checkpoint_name: str = None) -> Optional[Dict[str, Any]]:
        """
        加载检查点 - 与原始代码完全相同
        
        Args:
            checkpoint_name: 检查点名称（可选，默认加载最新的）
            
        Returns:
            检查点数据，如果没有找到则返回None
        """
        if not ENABLE_CHECKPOINTS:
            return None
        
        try:
            if checkpoint_name is None:
                # 找到最新的检查点
                checkpoint_files = list(self.checkpoint_dir.glob("checkpoint_*.json"))
                if not checkpoint_files:
                    print("   ℹ️ 没有找到检查点")
                    return None
                
                # 按修改时间排序，获取最新的
                checkpoint_files.sort(key=lambda x: x.stat().st_mtime, reverse=True)
                checkpoint_file = checkpoint_files[0]
            else:
                checkpoint_file = self.checkpoint_dir / f"{checkpoint_name}.json"
                if not checkpoint_file.exists():
                    print(f"   ❌ 检查点不存在: {checkpoint_name}")
                    return None
            
            with open(checkpoint_file, 'r', encoding='utf-8') as f:
                checkpoint_data = json.load(f)
            
            print(f"   ✅ 已加载检查点: {checkpoint_file}")
            self.current_checkpoint = checkpoint_data["checkpoint_name"]
            return checkpoint_data["data"]
            
        except Exception as e:
            print(f"   ❌ 加载检查点失败: {str(e)}")
            return None
    
    def list_checkpoints(self) -> List[Dict[str, Any]]:
        """
        列出所有检查点 - 与原始代码完全相同
        
        Returns:
            检查点信息列表
        """
        checkpoints = []
        
        try:
            checkpoint_files = list(self.checkpoint_dir.glob("*.json"))
            
            for checkpoint_file in checkpoint_files:
                try:
                    with open(checkpoint_file, 'r', encoding='utf-8') as f:
                        checkpoint_data = json.load(f)
                    
                    checkpoints.append({
                        "name": checkpoint_data["checkpoint_name"],
                        "timestamp": checkpoint_data["timestamp"],
                        "file": str(checkpoint_file),
                        "size": checkpoint_file.stat().st_size
                    })
                except:
                    continue
            
            # 按时间戳排序
            checkpoints.sort(key=lambda x: x["timestamp"], reverse=True)
            
        except Exception as e:
            print(f"   ❌ 列出检查点失败: {str(e)}")
        
        return checkpoints
    
    def delete_checkpoint(self, checkpoint_name: str) -> bool:
        """
        删除检查点 - 与原始代码完全相同
        
        Args:
            checkpoint_name: 检查点名称
            
        Returns:
            是否删除成功
        """
        try:
            checkpoint_file = self.checkpoint_dir / f"{checkpoint_name}.json"
            
            if checkpoint_file.exists():
                checkpoint_file.unlink()
                print(f"   ✅ 已删除检查点: {checkpoint_name}")
                
                if self.current_checkpoint == checkpoint_name:
                    self.current_checkpoint = None
                
                return True
            else:
                print(f"   ❌ 检查点不存在: {checkpoint_name}")
                return False
                
        except Exception as e:
            print(f"   ❌ 删除检查点失败: {str(e)}")
            return False
    
    def cleanup_old_checkpoints(self, keep_count: int = 5):
        """
        清理旧的检查点，只保留最新的几个 - 与原始代码完全相同
        
        Args:
            keep_count: 保留的检查点数量
        """
        try:
            checkpoints = self.list_checkpoints()
            
            if len(checkpoints) > keep_count:
                # 删除旧的检查点
                for checkpoint in checkpoints[keep_count:]:
                    checkpoint_name = checkpoint["name"]
                    self.delete_checkpoint(checkpoint_name)
                
                print(f"   🧹 已清理旧检查点，保留最新的 {keep_count} 个")
                
        except Exception as e:
            print(f"   ❌ 清理检查点失败: {str(e)}")
    
    def save_framework_checkpoint(self, framework: Dict[str, Any], stage: str):
        """
        保存框架检查点 - 与原始代码完全相同
        
        Args:
            framework: 报告框架数据
            stage: 当前阶段名称
        """
        checkpoint_data = {
            "stage": stage,
            "framework": framework,
            "timestamp": datetime.now().isoformat()
        }
        
        checkpoint_name = f"framework_{stage}_{int(time.time())}"
        self.save_checkpoint(checkpoint_data, checkpoint_name)
    
    def save_content_checkpoint(self, framework: Dict[str, Any], completed_nodes: List[str], stage: str):
        """
        保存内容生成检查点 - 与原始代码完全相同
        
        Args:
            framework: 当前的报告框架（包含已生成的内容）
            completed_nodes: 已完成的节点列表
            stage: 当前阶段名称
        """
        checkpoint_data = {
            "stage": stage,
            "framework": framework,
            "completed_nodes": completed_nodes,
            "timestamp": datetime.now().isoformat()
        }
        
        checkpoint_name = f"content_{stage}_{int(time.time())}"
        self.save_checkpoint(checkpoint_data, checkpoint_name)
