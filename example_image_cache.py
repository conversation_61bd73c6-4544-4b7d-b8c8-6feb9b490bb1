"""
图片内容缓存使用示例
演示如何使用图片内容的智能索引和缓存功能
"""
import os
import sys
import time
import shutil
from pathlib import Path
from PIL import Image, ImageDraw, ImageFont

def create_comprehensive_test_data():
    """创建综合测试数据（包含文档和图片）"""
    print("📁 创建综合测试数据...")
    
    test_dir = Path("comprehensive_test_data")
    if test_dir.exists():
        shutil.rmtree(test_dir)
    test_dir.mkdir()
    
    files_created = []
    
    # 1. 创建文本文档
    with open(test_dir / "report_summary.txt", "w", encoding="utf-8") as f:
        f.write("""数据分析报告摘要

本报告包含以下内容：
1. 销售数据图表分析
2. 用户行为流程图
3. 系统架构示意图
4. 财务数据表格

请参考相关图片文件获取详细信息。
""")
    files_created.append("report_summary.txt")
    
    # 2. 创建JSON配置文件
    import json
    config_data = {
        "report_config": {
            "include_images": True,
            "image_analysis": True,
            "ocr_enabled": True
        },
        "image_types": ["charts", "diagrams", "tables", "screenshots"]
    }
    with open(test_dir / "config.json", "w", encoding="utf-8") as f:
        json.dump(config_data, f, ensure_ascii=False, indent=2)
    files_created.append("config.json")
    
    # 3. 创建各种类型的图片
    images_created = []
    
    # 销售图表
    try:
        img = Image.new('RGB', (800, 600), color='white')
        draw = ImageDraw.Draw(img)
        
        # 绘制柱状图
        bars = [
            (100, 400, 150, 500, 'blue'),   # Q1
            (200, 350, 250, 500, 'green'),  # Q2
            (300, 300, 350, 500, 'red'),    # Q3
            (400, 250, 450, 500, 'orange'), # Q4
        ]
        
        for x1, y1, x2, y2, color in bars:
            draw.rectangle([x1, y1, x2, y2], fill=color)
        
        # 添加标题和标签
        try:
            font = ImageFont.load_default()
            draw.text((300, 50), "2024年季度销售额", fill='black', font=font)
            draw.text((100, 520), "Q1", fill='black', font=font)
            draw.text((200, 520), "Q2", fill='black', font=font)
            draw.text((300, 520), "Q3", fill='black', font=font)
            draw.text((400, 520), "Q4", fill='black', font=font)
        except:
            pass
        
        img_path = test_dir / "sales_chart_2024.png"
        img.save(img_path)
        images_created.append("sales_chart_2024.png")
        print(f"   ✅ 创建销售图表: {img_path}")
        
    except Exception as e:
        print(f"   ⚠️ 创建销售图表失败: {str(e)}")
    
    # 用户流程图
    try:
        img = Image.new('RGB', (900, 400), color='white')
        draw = ImageDraw.Draw(img)
        
        # 绘制流程框
        boxes = [
            (50, 150, 150, 200, "用户登录"),
            (250, 150, 350, 200, "选择产品"),
            (450, 150, 550, 200, "添加购物车"),
            (650, 150, 750, 200, "结算支付")
        ]
        
        for x1, y1, x2, y2, text in boxes:
            draw.rectangle([x1, y1, x2, y2], outline='black', width=2)
            try:
                font = ImageFont.load_default()
                draw.text((x1+10, y1+20), text, fill='black', font=font)
            except:
                pass
        
        # 绘制箭头
        arrows = [(150, 175, 250, 175), (350, 175, 450, 175), (550, 175, 650, 175)]
        for x1, y1, x2, y2 in arrows:
            draw.line([(x1, y1), (x2, y2)], fill='black', width=2)
            # 箭头头部
            draw.polygon([(x2-10, y2-5), (x2, y2), (x2-10, y2+5)], fill='black')
        
        # 添加标题
        try:
            font = ImageFont.load_default()
            draw.text((350, 50), "用户购买流程图", fill='black', font=font)
        except:
            pass
        
        img_path = test_dir / "user_flow_diagram.png"
        img.save(img_path)
        images_created.append("user_flow_diagram.png")
        print(f"   ✅ 创建流程图: {img_path}")
        
    except Exception as e:
        print(f"   ⚠️ 创建流程图失败: {str(e)}")
    
    # 数据表格
    try:
        img = Image.new('RGB', (700, 500), color='white')
        draw = ImageDraw.Draw(img)
        
        # 绘制表格
        rows, cols = 6, 5
        cell_width, cell_height = 120, 60
        start_x, start_y = 50, 100
        
        # 绘制网格
        for i in range(rows + 1):
            y = start_y + i * cell_height
            draw.line([(start_x, y), (start_x + cols * cell_width, y)], fill='black', width=1)
        
        for j in range(cols + 1):
            x = start_x + j * cell_width
            draw.line([(x, start_y), (x, start_y + rows * cell_height)], fill='black', width=1)
        
        # 添加表头
        headers = ["产品", "Q1销量", "Q2销量", "Q3销量", "Q4销量"]
        try:
            font = ImageFont.load_default()
            for j, header in enumerate(headers):
                x = start_x + j * cell_width + 10
                y = start_y + 10
                draw.text((x, y), header, fill='black', font=font)
        except:
            pass
        
        # 添加标题
        try:
            font = ImageFont.load_default()
            draw.text((250, 50), "产品销量数据表", fill='black', font=font)
        except:
            pass
        
        img_path = test_dir / "data_table.png"
        img.save(img_path)
        images_created.append("data_table.png")
        print(f"   ✅ 创建数据表格: {img_path}")
        
    except Exception as e:
        print(f"   ⚠️ 创建数据表格失败: {str(e)}")
    
    # 系统架构图
    try:
        img = Image.new('RGB', (800, 600), color='white')
        draw = ImageDraw.Draw(img)
        
        # 绘制架构层次
        layers = [
            (100, 100, 700, 150, "前端层 (Frontend)"),
            (100, 200, 700, 250, "业务逻辑层 (Business Logic)"),
            (100, 300, 700, 350, "数据访问层 (Data Access)"),
            (100, 400, 700, 450, "数据库层 (Database)")
        ]
        
        colors = ['lightblue', 'lightgreen', 'lightyellow', 'lightcoral']
        
        for i, (x1, y1, x2, y2, text) in enumerate(layers):
            # 使用不同颜色填充（简化处理）
            draw.rectangle([x1, y1, x2, y2], outline='black', width=2)
            try:
                font = ImageFont.load_default()
                draw.text((x1+250, y1+20), text, fill='black', font=font)
            except:
                pass
        
        # 绘制连接线
        for i in range(len(layers) - 1):
            y1 = layers[i][3]  # 当前层底部
            y2 = layers[i+1][1]  # 下一层顶部
            x = 400  # 中心位置
            draw.line([(x, y1), (x, y2)], fill='black', width=2)
            # 箭头
            draw.polygon([(x-5, y2-10), (x, y2), (x+5, y2-10)], fill='black')
        
        # 添加标题
        try:
            font = ImageFont.load_default()
            draw.text((300, 50), "系统架构图", fill='black', font=font)
        except:
            pass
        
        img_path = test_dir / "system_architecture.png"
        img.save(img_path)
        images_created.append("system_architecture.png")
        print(f"   ✅ 创建架构图: {img_path}")
        
    except Exception as e:
        print(f"   ⚠️ 创建架构图失败: {str(e)}")
    
    print(f"✅ 创建了 {len(files_created)} 个文档文件和 {len(images_created)} 个图片文件")
    return test_dir, files_created, images_created

def demonstrate_image_cache():
    """演示图片缓存功能"""
    print("🚀 演示图片内容缓存功能")
    print("=" * 60)
    
    try:
        from complete_report_generator import CompleteReportGenerator
        
        # 创建测试数据
        test_dir, files_created, images_created = create_comprehensive_test_data()
        
        # 初始化生成器
        generator = CompleteReportGenerator(use_async=False)
        
        print(f"\n📖 第一次读取数据源（处理文档和图片）")
        start_time = time.time()
        
        content1 = generator.read_data_source(str(test_dir))
        
        first_time = time.time() - start_time
        print(f"⏱️ 第一次处理耗时: {first_time:.2f} 秒")
        
        # 检查缓存结构
        processed_dir = test_dir / "processed"
        if processed_dir.exists():
            print(f"\n📂 缓存结构分析:")
            
            # 检查主要缓存文件
            merged_file = processed_dir / "merged_content.txt"
            if merged_file.exists():
                print(f"   ✅ 合并内容文件: {merged_file.stat().st_size} 字节")
            
            info_file = processed_dir / "processing_info.json"
            if info_file.exists():
                print(f"   ✅ 处理信息文件: {info_file.stat().st_size} 字节")
                
                # 读取处理信息
                import json
                with open(info_file, 'r', encoding='utf-8') as f:
                    info = json.load(f)
                
                print(f"   📊 处理统计:")
                print(f"      文件数量: {info.get('total_files', 0)}")
                print(f"      内容长度: {info.get('total_characters', 0)} 字符")
                print(f"      缓存版本: {info.get('cache_version', '1.0')}")
                
                if info.get('image_index'):
                    print(f"      图片索引: 已创建")
            
            # 检查图片索引
            image_index_dir = processed_dir / "image_index"
            if image_index_dir.exists():
                print(f"   ✅ 图片索引目录: {image_index_dir}")
                
                index_file = image_index_dir / "image_index.json"
                if index_file.exists():
                    import json
                    with open(index_file, 'r', encoding='utf-8') as f:
                        img_index = json.load(f)
                    
                    images = img_index.get("images", {})
                    print(f"   📷 图片索引统计:")
                    print(f"      索引图片: {len(images)} 个")
                    print(f"      索引大小: {index_file.stat().st_size} 字节")
                    
                    # 显示图片分析结果
                    for img_path, img_data in images.items():
                        print(f"      • {img_path}:")
                        props = img_data.get("image_properties", {})
                        if props:
                            print(f"        尺寸: {props.get('width')}x{props.get('height')}")
                        
                        analysis = img_data.get("content_analysis", "")
                        if analysis:
                            print(f"        分析: {analysis}")
                        
                        ocr_text = img_data.get("ocr_text", "")
                        if ocr_text:
                            print(f"        文字: {ocr_text[:50]}...")
        
        print(f"\n📖 第二次读取数据源（使用缓存）")
        start_time = time.time()
        
        content2 = generator.read_data_source(str(test_dir))
        
        second_time = time.time() - start_time
        print(f"⏱️ 第二次处理耗时: {second_time:.2f} 秒")
        
        # 性能对比
        if second_time < first_time:
            speedup = first_time / second_time
            print(f"🚀 缓存加速效果: {speedup:.1f}x 倍")
        
        # 内容验证
        if content1 == content2:
            print(f"✅ 缓存内容一致性验证通过")
        else:
            print(f"⚠️ 缓存内容不一致")
        
        # 分析内容结构
        print(f"\n📋 内容结构分析:")
        print(f"   总长度: {len(content1)} 字符")
        
        # 统计各种内容类型
        file_sections = content1.count("=== 文件:")
        image_sections = content1.count("图片内容索引")
        
        print(f"   文件段落: {file_sections} 个")
        print(f"   图片索引: {image_sections} 个")
        
        if "图片内容索引" in content1:
            print(f"   ✅ 包含图片内容索引")
            
            # 提取图片索引部分
            idx_start = content1.find("=== 图片内容索引 ===")
            if idx_start != -1:
                idx_section = content1[idx_start:idx_start+1000]  # 取前1000字符
                print(f"\n📷 图片索引内容预览:")
                for line in idx_section.split('\n')[:10]:
                    if line.strip():
                        print(f"   {line}")
        
        # 清理测试数据
        print(f"\n🧹 清理测试数据...")
        if test_dir.exists():
            shutil.rmtree(test_dir)
            print(f"✅ 测试数据清理完成")
        
        return True
        
    except ImportError as e:
        print(f"❌ 导入失败: {str(e)}")
        return False
    except Exception as e:
        print(f"❌ 演示失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🖼️ AI报告生成器 - 图片内容缓存演示")
    print("=" * 60)
    
    success = demonstrate_image_cache()
    
    if success:
        print(f"\n🎉 图片内容缓存演示成功！")
        print(f"\n💡 图片缓存的优势:")
        print(f"   1. 🖼️ 智能图片内容分析和索引")
        print(f"   2. 💾 缓存图片分析结果，避免重复处理")
        print(f"   3. 🔍 支持OCR文字识别和缓存")
        print(f"   4. 📊 详细的图片属性信息")
        print(f"   5. 🚀 显著提升处理性能")
        print(f"   6. 🔄 与文档缓存无缝集成")
        
        print(f"\n🛠️ 管理工具:")
        print(f"   • python image_cache_manager.py list    # 查看图片缓存")
        print(f"   • python image_cache_manager.py details --index 1  # 查看详情")
        print(f"   • python image_cache_manager.py clean   # 清理缓存")
        print(f"   • python image_cache_manager.py stats   # 缓存统计")
    else:
        print(f"\n💡 如果演示失败，请检查:")
        print(f"   1. 确保Pillow库已安装: pip install Pillow")
        print(f"   2. 确保complete_report_generator.py文件存在")
        print(f"   3. 检查文件权限和磁盘空间")
    
    sys.exit(0 if success else 1)
