"""
测试Metaso搜索API功能
验证网页搜索和学术论文搜索功能
"""
import os
import sys
import time
from pathlib import Path

def test_metaso_webpage_search():
    """测试Metaso网页搜索"""
    print("🌐 测试Metaso网页搜索")
    print("=" * 50)
    
    try:
        import http.client
        import json
        
        # 使用提供的API Key
        api_key = os.getenv('METASO_API_KEY', 'mk-988A8E4DC50C53312E3D1A8729687F4C')
        
        conn = http.client.HTTPSConnection("metaso.cn")
        payload = json.dumps({
            "q": "地热发电产业进展",
            "scope": "webpage",
            "includeSummary": True,
            "size": "5"
        })
        headers = {
            'Authorization': f'Bearer {api_key}',
            'Accept': 'application/json',
            'Content-Type': 'application/json'
        }
        
        print(f"🔍 搜索查询: 地热发电产业进展")
        print(f"📊 搜索范围: 网页内容")
        
        conn.request("POST", "/api/v1/search", payload, headers)
        res = conn.getresponse()
        data = res.read()
        
        if res.status == 200:
            response_data = json.loads(data.decode("utf-8"))
            
            print(f"✅ 搜索成功 (HTTP {res.status})")
            
            results = response_data.get('data', {}).get('results', [])
            print(f"📋 找到 {len(results)} 个结果:")
            
            for i, result in enumerate(results, 1):
                print(f"\n{i}. 标题: {result.get('title', '')}")
                print(f"   URL: {result.get('url', '')}")
                print(f"   摘要: {result.get('summary', '')[:100]}...")
                print(f"   发布日期: {result.get('publishedDate', '未知')}")
                print(f"   评分: {result.get('score', 0)}")
            
            return True
        else:
            print(f"❌ 搜索失败: HTTP {res.status}")
            print(f"响应内容: {data.decode('utf-8')}")
            return False
            
    except Exception as e:
        print(f"❌ 网页搜索测试失败: {str(e)}")
        return False

def test_metaso_scholar_search():
    """测试Metaso学术论文搜索"""
    print("\n📚 测试Metaso学术论文搜索")
    print("=" * 50)
    
    try:
        import http.client
        import json
        
        # 使用提供的API Key
        api_key = os.getenv('METASO_API_KEY', 'mk-988A8E4DC50C53312E3D1A8729687F4C')
        
        conn = http.client.HTTPSConnection("metaso.cn")
        payload = json.dumps({
            "q": "地热发电产业进展",
            "scope": "scholar",
            "includeSummary": True,
            "size": "5"
        })
        headers = {
            'Authorization': f'Bearer {api_key}',
            'Accept': 'application/json',
            'Content-Type': 'application/json'
        }
        
        print(f"🔍 搜索查询: 地热发电产业进展")
        print(f"📊 搜索范围: 学术论文")
        
        conn.request("POST", "/api/v1/search", payload, headers)
        res = conn.getresponse()
        data = res.read()
        
        if res.status == 200:
            response_data = json.loads(data.decode("utf-8"))
            
            print(f"✅ 搜索成功 (HTTP {res.status})")
            
            results = response_data.get('data', {}).get('results', [])
            print(f"📋 找到 {len(results)} 个学术结果:")
            
            for i, result in enumerate(results, 1):
                print(f"\n{i}. 标题: {result.get('title', '')}")
                print(f"   URL: {result.get('url', '')}")
                print(f"   摘要: {result.get('summary', '')[:100]}...")
                print(f"   发布日期: {result.get('publishedDate', '未知')}")
                print(f"   评分: {result.get('score', 0)}")
                print(f"   作者: {result.get('authors', '未知')}")
            
            return True
        else:
            print(f"❌ 搜索失败: HTTP {res.status}")
            print(f"响应内容: {data.decode('utf-8')}")
            return False
            
    except Exception as e:
        print(f"❌ 学术搜索测试失败: {str(e)}")
        return False

def test_metaso_integration():
    """测试Metaso与报告生成器的集成"""
    print("\n🔧 测试Metaso集成功能")
    print("=" * 50)
    
    try:
        from complete_report_generator import CompleteReportGenerator
        
        generator = CompleteReportGenerator(use_async=False)
        search_manager = generator.SearchManager(generator)
        
        print(f"📊 搜索API配置状态:")
        for api_name, config in search_manager.search_apis.items():
            status = "✅ 已配置" if config.get('enabled', False) else "❌ 未配置"
            print(f"   {api_name}: {status}")
        
        # 测试Metaso搜索
        if 'metaso' in search_manager.search_apis and search_manager.search_apis['metaso']['enabled']:
            print(f"\n🔍 测试集成搜索功能...")
            
            test_queries = [
                "人工智能 2024 发展趋势",
                "量子计算 最新突破",
                "新能源汽车 市场分析"
            ]
            
            for query in test_queries:
                print(f"\n查询: {query}")
                
                try:
                    results = search_manager.multi_source_search(
                        query, ['metaso'], num_results=3
                    )
                    
                    print(f"   结果数量: {len(results)}")
                    for i, result in enumerate(results, 1):
                        print(f"   {i}. {result.get('title', '')[:50]}...")
                        print(f"      来源: {result.get('source', '')}")
                        print(f"      评分: {result.get('score', 0)}")
                
                except Exception as e:
                    print(f"   ❌ 搜索失败: {str(e)}")
            
            print(f"✅ Metaso集成测试通过")
            return True
        else:
            print(f"❌ Metaso API未配置")
            return False
            
    except Exception as e:
        print(f"❌ 集成测试失败: {str(e)}")
        return False

def test_search_enhancement_with_metaso():
    """测试使用Metaso的搜索增强功能"""
    print("\n🚀 测试搜索增强功能")
    print("=" * 50)
    
    try:
        from complete_report_generator import CompleteReportGenerator
        
        # 创建测试报告
        test_content = """# 人工智能技术发展报告

## 1. 技术概述
人工智能技术正在快速发展，包括机器学习、深度学习等领域。

## 2. 应用领域
AI在医疗、金融、自动驾驶等领域有广泛应用。

## 3. 发展前景
人工智能将继续推动各行业的数字化转型。

---
*本报告可能缺少最新的市场数据和技术发展信息。*
"""
        
        test_dir = Path("test_metaso_data")
        if test_dir.exists():
            import shutil
            shutil.rmtree(test_dir)
        test_dir.mkdir()
        
        test_report_path = test_dir / "ai_report.md"
        with open(test_report_path, 'w', encoding='utf-8') as f:
            f.write(test_content)
        
        print(f"📄 创建测试报告: {test_report_path}")
        
        # 初始化生成器
        generator = CompleteReportGenerator(use_async=False)
        
        # 分析内容缺口
        search_trigger = generator.SearchTrigger(generator)
        content_gaps = search_trigger.analyze_content_gaps(test_content, "人工智能技术发展")
        
        print(f"📊 发现 {len(content_gaps)} 个内容缺口:")
        for i, gap in enumerate(content_gaps, 1):
            print(f"   {i}. {gap['reason']} (优先级: {gap['priority']})")
        
        # 执行搜索增强（自动模式）
        print(f"\n🔍 执行搜索增强...")
        enhanced_path = generator.enhance_report_with_search(
            str(test_report_path),
            "人工智能技术发展",
            user_confirm=False  # 自动执行
        )
        
        if enhanced_path != str(test_report_path):
            print(f"✅ 搜索增强成功: {enhanced_path}")
            
            # 检查增强内容
            with open(enhanced_path, 'r', encoding='utf-8') as f:
                enhanced_content = f.read()
            
            if "最新信息补充" in enhanced_content:
                print(f"✅ 包含最新信息补充")
                supplement_sections = enhanced_content.count("###")
                print(f"📋 补充了 {supplement_sections} 个信息部分")
            
            return True
        else:
            print(f"⚠️ 未生成增强报告")
            return False
    
    except Exception as e:
        print(f"❌ 搜索增强测试失败: {str(e)}")
        return False
    finally:
        # 清理测试数据
        test_dir = Path("test_metaso_data")
        if test_dir.exists():
            import shutil
            shutil.rmtree(test_dir)

def main():
    """主测试函数"""
    print("🚀 Metaso搜索API功能测试")
    print("=" * 60)
    
    # 设置API Key
    os.environ['METASO_API_KEY'] = 'mk-988A8E4DC50C53312E3D1A8729687F4C'
    
    tests = [
        ("Metaso网页搜索", test_metaso_webpage_search),
        ("Metaso学术搜索", test_metaso_scholar_search),
        ("Metaso集成功能", test_metaso_integration),
        ("搜索增强功能", test_search_enhancement_with_metaso)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            print(f"\n{'='*60}")
            result = test_func()
            results.append((test_name, result))
            
            if result:
                print(f"✅ {test_name}: 测试通过")
            else:
                print(f"❌ {test_name}: 测试失败")
                
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {str(e)}")
            results.append((test_name, False))
    
    # 总结
    print(f"\n📊 测试总结:")
    print("=" * 60)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {test_name}: {status}")
    
    print(f"\n🎯 总体结果: {passed}/{total} 个测试通过")
    
    if passed == total:
        print(f"\n🎉 Metaso搜索功能测试全部通过！")
        print(f"\n💡 功能特点:")
        print(f"   1. 🌐 支持高质量的中文网页搜索")
        print(f"   2. 📚 支持学术论文搜索")
        print(f"   3. 🔧 完美集成到报告生成器")
        print(f"   4. 🚀 自动搜索增强功能")
        print(f"   5. 📊 智能内容质量评估")
    else:
        print(f"\n💡 如果测试失败，请检查:")
        print(f"   1. 网络连接是否正常")
        print(f"   2. API Key是否有效")
        print(f"   3. 是否安装了所需依赖")
    
    return passed == total

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\n❌ 用户中断测试")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 测试程序错误: {str(e)}")
        sys.exit(1)
