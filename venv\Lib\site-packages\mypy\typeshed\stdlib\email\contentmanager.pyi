from collections.abc import Callable
from email.message import Message
from typing import Any

class ContentManager:
    def get_content(self, msg: Message, *args: Any, **kw: Any) -> Any: ...
    def set_content(self, msg: Message, obj: Any, *args: Any, **kw: Any) -> Any: ...
    def add_get_handler(self, key: str, handler: Callable[..., Any]) -> None: ...
    def add_set_handler(self, typekey: type, handler: Callable[..., Any]) -> None: ...

raw_data_manager: ContentManager
