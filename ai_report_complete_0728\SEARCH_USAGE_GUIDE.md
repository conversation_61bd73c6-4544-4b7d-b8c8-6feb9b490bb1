# 联网搜索模块使用指南

## 🎯 概述

根据您的需求，我已经创建了一个完整的联网搜索模块系统，完全按照源代码中的设计工作流实现。系统提供了简单易用的接口，**输入内容即可开启搜索**。

## 📋 模块架构

### 🏗️ 三层架构设计

```
📱 搜索接口层 (search_interface.py)
├─ 简单易用的API接口
├─ 命令行交互界面
└─ 便捷搜索函数

🤖 智能搜索层 (smart_search_assistant.py)
├─ 搜索意图分析
├─ 搜索策略优化
├─ 结果质量评估
└─ 内容缺口识别

🔍 基础搜索层 (web_search_module.py)
├─ 多搜索引擎支持
├─ API管理和调用
├─ 结果合并和去重
└─ 速率限制控制
```

## 🚀 快速开始

### 1. 环境配置

在 `.env` 文件中配置API密钥：

```bash
# Metaso Search API (已提供)
METASO_API_KEY=mk-988A8E4DC50C53312E3D1A8729687F4C

# Google Custom Search API (可选)
GOOGLE_SEARCH_API_KEY=your_google_api_key
GOOGLE_SEARCH_CX=your_custom_search_engine_id

# Bing Search API (可选)
BING_SEARCH_API_KEY=your_bing_api_key
```

### 2. 基础使用

```python
# 导入搜索接口
from search_interface import search, smart_search

# 基础搜索 - 输入内容即开启搜索
results = search("人工智能最新发展")

# 智能搜索 - 自动分析搜索意图
smart_results = smart_search("AI技术趋势")
```

### 3. 命令行使用

```bash
# 启动命令行界面
python search_interface.py

# 然后输入搜索内容
🔍 请输入搜索内容: 人工智能
```

## 📖 详细使用方法

### 🔍 基础搜索功能

#### 1. 简单搜索

```python
from search_interface import search

# 最简单的使用方式
results = search("机器学习")

# 指定结果数量
results = search("深度学习", num_results=20)

# 指定搜索引擎
results = search("自然语言处理", engine="metaso")
```

#### 2. 多引擎搜索

```python
from core.web_search_module import WebSearchModule

web_search = WebSearchModule()

# 使用多个搜索引擎并合并结果
results = web_search.multi_search("计算机视觉", num_results_per_engine=5)
```

#### 3. 回退搜索

```python
# 带回退机制的搜索（如果首选引擎失败，自动尝试其他引擎）
results = web_search.search_with_fallback("区块链技术", preferred_engine="metaso")
```

### 🤖 智能搜索功能

#### 1. 自动意图识别

```python
from search_interface import smart_search

# 系统会自动识别搜索意图
latest_results = smart_search("人工智能最新发展")      # 识别为：latest_data
market_results = smart_search("AI市场规模分析")        # 识别为：market_analysis
tech_results = smart_search("机器学习技术创新")        # 识别为：technology_trends
```

#### 2. 指定搜索类型

```python
# 明确指定搜索类型
results = smart_search("电动汽车", search_type="market_analysis")

# 支持的搜索类型：
# - latest_data: 最新数据
# - market_analysis: 市场分析
# - technology_trends: 技术趋势
# - policy_regulations: 政策法规
# - competitive_analysis: 竞争分析
```

#### 3. 内容增强搜索

```python
from search_interface import enhance_content

# 基于现有内容识别缺口并搜索
existing_content = "人工智能是一门新兴技术..."
topic = "AI技术报告"

enhancement = enhance_content(existing_content, topic)

if enhancement['status'] == 'enhanced':
    gaps = enhancement['gaps']
    print(f"发现 {len(gaps)} 个内容缺口")
```

### 📦 批量搜索功能

```python
from search_interface import SearchInterface

search_interface = SearchInterface()

# 批量搜索多个查询
queries = ["人工智能", "机器学习", "深度学习", "自然语言处理"]
batch_results = search_interface.batch_search(queries)

# 每个结果包含详细的分析信息
for result in batch_results:
    analysis = result['analysis']
    print(f"查询: {result['query']}")
    print(f"高质量结果: {len(analysis['filtered_results'])} 个")
```

### 🎯 便捷搜索方法

```python
from search_interface import search_latest, search_market

# 搜索最新信息
latest_ai = search_latest("人工智能")

# 搜索市场信息
market_ev = search_market("电动汽车")

# 搜索技术信息
tech_5g = search_interface.search_tech("5G技术")

# 搜索政策信息
policy_ai = search_interface.search_policy("AI监管")
```

## 🔧 高级功能

### 1. 搜索结果分析

```python
# 智能搜索返回详细的分析结果
result = smart_search("量子计算发展")

analysis = result['analysis']
print(f"质量分数: {analysis['quality_score']}/100")
print(f"相关性分数: {analysis['relevance_score']}/100")
print(f"时效性分数: {analysis['freshness_score']}/100")
print(f"搜索建议: {result['recommendations']}")
```

### 2. 搜索历史管理

```python
from core.web_search_module import WebSearchModule

web_search = WebSearchModule()

# 获取搜索历史
history = web_search.get_search_history()

# 清空搜索历史
web_search.clear_search_history()
```

### 3. API状态监控

```python
from search_interface import test_search_apis

# 测试所有API状态
api_status = test_search_apis()

for engine, status in api_status.items():
    print(f"{engine}: {'✅ 正常' if status else '❌ 异常'}")
```

### 4. 系统状态查询

```python
search_interface = SearchInterface()

# 获取系统状态
status = search_interface.get_status()
print(f"API状态: {status['api_status']}")
print(f"搜索统计: {status['search_statistics']}")
```

## 🎨 搜索策略

### 1. 最新数据搜索

```python
# 自动添加时间限定词
smart_search("人工智能最新发展", "latest_data")
# 实际搜索: "人工智能最新发展 2024 2025 最新"
```

### 2. 市场分析搜索

```python
# 自动添加市场相关关键词
smart_search("电动汽车", "market_analysis")
# 实际搜索: "电动汽车 市场规模 行业分析 竞争格局"
```

### 3. 技术趋势搜索

```python
# 自动添加技术相关关键词
smart_search("5G技术", "technology_trends")
# 实际搜索: "5G技术 技术发展 创新 趋势 突破"
```

## 📊 结果格式

### 基础搜索结果

```python
[
    {
        'title': '搜索结果标题',
        'url': 'https://example.com',
        'snippet': '搜索结果摘要...',
        'source': 'metaso',  # 搜索引擎来源
        'date': '2024-01-01',
        'score': 0.95
    },
    # ... 更多结果
]
```

### 智能搜索结果

```python
{
    'query': '搜索查询',
    'search_type': 'latest_data',
    'duration': 2.5,
    'analysis': {
        'total_results': 15,
        'quality_score': 85.5,
        'relevance_score': 92.3,
        'freshness_score': 78.1,
        'filtered_results': [...],  # 高质量结果
        'summary': '找到 10 个高质量结果...'
    },
    'recommendations': [
        '搜索结果质量良好，可以直接使用'
    ],
    'timestamp': 1704067200
}
```

## 🛠️ 故障排除

### 1. API配置问题

```python
# 检查API配置
from core.web_search_module import WebSearchModule

web_search = WebSearchModule()
api_status = web_search.get_api_status()

for engine, config in api_status.items():
    print(f"{engine}:")
    print(f"  启用: {config['enabled']}")
    print(f"  有API密钥: {config['has_api_key']}")
```

### 2. 网络连接问题

```python
# 测试API连通性
api_results = web_search.test_all_apis()

working_apis = [name for name, status in api_results.items() if status]
print(f"可用API: {working_apis}")
```

### 3. 搜索无结果

```python
# 使用回退搜索
results = web_search.search_with_fallback("查询内容")

if not results:
    # 尝试更广泛的搜索词
    broader_results = search("更广泛的关键词")
```

## 💡 最佳实践

### 1. 搜索查询优化

```python
# ✅ 好的搜索查询
search("人工智能最新发展趋势 2024")
search("电动汽车市场规模分析")
search("5G技术创新应用")

# ❌ 避免的搜索查询
search("AI")  # 太简短
search("人工智能人工智能人工智能")  # 重复
search("@#$%")  # 特殊字符
```

### 2. 结果处理

```python
# 处理搜索结果
results = search("机器学习")

for result in results:
    # 检查结果完整性
    if result.get('title') and result.get('url') and result.get('snippet'):
        print(f"标题: {result['title']}")
        print(f"链接: {result['url']}")
        print(f"摘要: {result['snippet'][:100]}...")
```

### 3. 错误处理

```python
try:
    results = search("查询内容")
    if results:
        # 处理结果
        pass
    else:
        print("未找到相关结果")
except Exception as e:
    print(f"搜索失败: {str(e)}")
```

## 🔗 集成到报告生成

### 在报告生成中使用搜索

```python
from core.generator import CompleteReportGenerator
from search_interface import smart_search, enhance_content

# 在报告生成过程中使用搜索增强
generator = CompleteReportGenerator()

# 启用搜索增强
generator.report_config['enable_search_enhancement'] = True

# 生成报告时会自动调用搜索功能
output = generator.generate_report(
    topic="人工智能技术发展报告",
    data_sources=["data1", "data2"]
)
```

## 📈 总结

这个联网搜索模块系统为您提供了：

- ✅ **简单易用**：输入内容即可开启搜索
- ✅ **智能分析**：自动识别搜索意图和优化策略
- ✅ **多引擎支持**：Metaso、Google、Bing等多个搜索引擎
- ✅ **结果优化**：自动去重、排序和质量评估
- ✅ **错误处理**：完善的回退机制和错误恢复
- ✅ **灵活配置**：支持多种搜索策略和参数调整

现在您可以通过简单的函数调用或命令行界面，轻松使用联网搜索功能来增强您的报告内容！
