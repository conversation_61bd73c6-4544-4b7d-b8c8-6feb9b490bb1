"""
测试所有改进功能
验证进度条、缓存机制、废话清理等功能
"""
import os
import sys
import time
import shutil
from pathlib import Path

def test_progress_bars():
    """测试进度条功能"""
    print("🧪 测试进度条功能")
    print("=" * 60)
    
    try:
        from tqdm import tqdm
        import time
        
        # 测试基本进度条
        print("📊 测试基本进度条:")
        for i in tqdm(range(10), desc="基本测试", unit="项"):
            time.sleep(0.1)
        
        # 测试嵌套进度条
        print("\n📊 测试嵌套进度条:")
        main_pbar = tqdm(total=3, desc="主任务", unit="步骤")
        
        for i in range(3):
            main_pbar.set_description(f"步骤 {i+1}")
            sub_pbar = tqdm(total=5, desc=f"子任务{i+1}", unit="项", leave=False)
            
            for j in range(5):
                sub_pbar.set_description(f"处理项目 {j+1}")
                time.sleep(0.05)
                sub_pbar.update(1)
            
            sub_pbar.close()
            main_pbar.update(1)
        
        main_pbar.close()
        
        print("✅ 进度条功能测试通过")
        return True
        
    except ImportError:
        print("❌ tqdm库未安装，请运行: pip install tqdm")
        return False
    except Exception as e:
        print(f"❌ 进度条测试失败: {str(e)}")
        return False

def test_response_cleaning():
    """测试响应清理功能"""
    print("\n🧪 测试响应清理功能")
    print("=" * 60)
    
    try:
        from complete_report_generator import CompleteReportGenerator
        
        generator = CompleteReportGenerator(use_async=False)
        
        # 测试废话清理
        test_responses = [
            """好的，遵照您的要求，作为报告统筹模型，我已对您提供的章节内容进行了全面检查。

您提供的优化方案非常出色，已经准确地识别了核心问题。

以下是我基于您的优化方案的最终版本：

# 实际内容开始

这是真正的报告内容，应该被保留。

## 第一章 概述

这里是章节的实际内容。

---

### 最终优化版本（确认稿）

经我最终确认，无需进一步修改的优化后章节内容如下：

实际的章节内容在这里。""",

            """作为专业的报告统筹模型，请对以下章节进行深度审核。

根据统筹模型的安排，以下是生成的内容：

# 地热发电技术分析

地热发电是一种清洁的可再生能源技术。

## 技术原理

地热发电利用地下热能进行发电。""",

            """【去除重复表述】✓ 已实现
【增强独特性】✓ 已实现  
【保持完整性】✓ 已实现

---

## 市场分析

全球地热发电市场正在快速发展。"""
        ]
        
        print("🧹 测试废话清理:")
        for i, response in enumerate(test_responses, 1):
            print(f"\n测试样本 {i}:")
            print(f"原始长度: {len(response)} 字符")
            
            cleaned = generator._clean_model_response(response)
            print(f"清理后长度: {len(cleaned)} 字符")
            print(f"减少: {len(response) - len(cleaned)} 字符 ({((len(response) - len(cleaned)) / len(response) * 100):.1f}%)")
            
            # 显示清理后的内容预览
            preview = cleaned[:200] + "..." if len(cleaned) > 200 else cleaned
            print(f"清理后内容预览: {preview}")
        
        print("\n✅ 响应清理功能测试通过")
        return True
        
    except ImportError:
        print("❌ 无法导入CompleteReportGenerator")
        return False
    except Exception as e:
        print(f"❌ 响应清理测试失败: {str(e)}")
        return False

def test_cache_with_images():
    """测试包含图片的缓存功能"""
    print("\n🧪 测试图片缓存功能")
    print("=" * 60)
    
    try:
        from complete_report_generator import CompleteReportGenerator
        from PIL import Image, ImageDraw
        
        # 创建测试数据
        test_dir = Path("test_improvements_data")
        if test_dir.exists():
            shutil.rmtree(test_dir)
        test_dir.mkdir()
        
        # 创建测试文档
        with open(test_dir / "test_doc.txt", "w", encoding="utf-8") as f:
            f.write("这是一个测试文档，用于验证缓存功能。")
        
        # 创建测试图片
        img = Image.new('RGB', (300, 200), color='white')
        draw = ImageDraw.Draw(img)
        draw.rectangle([50, 50, 250, 150], outline='black', width=2)
        draw.text((100, 100), "Test Chart", fill='black')
        img.save(test_dir / "test_chart.png")
        
        generator = CompleteReportGenerator(use_async=False)
        
        print("📖 第一次读取（创建缓存）:")
        start_time = time.time()
        content1 = generator.read_data_source(str(test_dir))
        first_time = time.time() - start_time
        print(f"⏱️ 耗时: {first_time:.2f}秒")
        
        print("\n📖 第二次读取（使用缓存）:")
        start_time = time.time()
        content2 = generator.read_data_source(str(test_dir))
        second_time = time.time() - start_time
        print(f"⏱️ 耗时: {second_time:.2f}秒")
        
        # 验证结果
        if content1 == content2:
            print("✅ 缓存内容一致性验证通过")
        else:
            print("❌ 缓存内容不一致")
        
        if second_time < first_time:
            speedup = first_time / second_time if second_time > 0 else float('inf')
            print(f"🚀 缓存加速: {speedup:.1f}x")
        
        # 检查图片索引
        processed_dir = test_dir / "processed"
        image_index_dir = processed_dir / "image_index"
        
        if image_index_dir.exists():
            print("✅ 图片索引目录已创建")
            
            index_file = image_index_dir / "image_index.json"
            if index_file.exists():
                import json
                with open(index_file, 'r', encoding='utf-8') as f:
                    index_data = json.load(f)
                
                images = index_data.get("images", {})
                print(f"📷 图片索引: {len(images)} 个图片")
                
                for img_path, img_data in images.items():
                    analysis = img_data.get("content_analysis", "")
                    print(f"   {img_path}: {analysis}")
            else:
                print("❌ 图片索引文件不存在")
        else:
            print("❌ 图片索引目录不存在")
        
        # 清理测试数据
        if test_dir.exists():
            shutil.rmtree(test_dir)
        
        print("✅ 图片缓存功能测试通过")
        return True
        
    except ImportError as e:
        print(f"❌ 导入失败: {str(e)}")
        return False
    except Exception as e:
        print(f"❌ 图片缓存测试失败: {str(e)}")
        return False

def test_dependencies():
    """测试依赖库安装"""
    print("\n🧪 测试依赖库")
    print("=" * 60)
    
    required_libs = [
        ("tqdm", "进度条"),
        ("PIL", "图片处理"),
        ("pathlib", "路径处理"),
        ("json", "JSON处理"),
        ("re", "正则表达式")
    ]
    
    success_count = 0
    
    for lib_name, description in required_libs:
        try:
            __import__(lib_name)
            print(f"✅ {lib_name} ({description}): 已安装")
            success_count += 1
        except ImportError:
            print(f"❌ {lib_name} ({description}): 未安装")
    
    optional_libs = [
        ("PyPDF2", "PDF读取"),
        ("pdfplumber", "PDF读取增强"),
        ("pandas", "数据处理"),
        ("openpyxl", "Excel处理"),
        ("python-docx", "Word处理"),
        ("pytesseract", "OCR识别")
    ]
    
    optional_count = 0
    
    print(f"\n📦 可选依赖库:")
    for lib_name, description in optional_libs:
        try:
            __import__(lib_name.replace('-', '_'))
            print(f"✅ {lib_name} ({description}): 已安装")
            optional_count += 1
        except ImportError:
            print(f"⚠️ {lib_name} ({description}): 未安装")
    
    print(f"\n📊 依赖库统计:")
    print(f"   必需库: {success_count}/{len(required_libs)} 已安装")
    print(f"   可选库: {optional_count}/{len(optional_libs)} 已安装")
    
    return success_count == len(required_libs)

def main():
    """主测试函数"""
    print("🚀 AI报告生成器 - 改进功能测试")
    print("=" * 60)
    
    tests = [
        ("进度条功能", test_progress_bars),
        ("响应清理功能", test_response_cleaning),
        ("图片缓存功能", test_cache_with_images),
        ("依赖库检查", test_dependencies)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {str(e)}")
            results.append((test_name, False))
    
    # 总结
    print(f"\n📊 测试总结:")
    print("=" * 60)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {test_name}: {status}")
    
    print(f"\n🎯 总体结果: {passed}/{total} 个测试通过")
    
    if passed == total:
        print(f"\n🎉 所有改进功能测试通过！")
        print(f"\n💡 改进功能说明:")
        print(f"   1. ⏱️ 添加了详细的进度条显示")
        print(f"   2. 🧹 自动清理模型响应中的废话")
        print(f"   3. 🖼️ 支持图片内容智能索引和缓存")
        print(f"   4. 🚀 显著提升处理性能")
        
        print(f"\n🛠️ 使用建议:")
        print(f"   • 如果缺少可选依赖，运行: python install_extended_dependencies.py")
        print(f"   • 图片功能需要Pillow库: pip install Pillow")
        print(f"   • OCR功能需要pytesseract和Tesseract引擎")
    else:
        print(f"\n💡 如果测试失败，请:")
        print(f"   1. 检查依赖库安装")
        print(f"   2. 运行: python install_extended_dependencies.py")
        print(f"   3. 确保有足够的磁盘空间")
    
    return passed == total

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\n❌ 用户中断测试")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 测试程序错误: {str(e)}")
        sys.exit(1)
