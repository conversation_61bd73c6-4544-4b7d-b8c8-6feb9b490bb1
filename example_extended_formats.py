"""
扩展文件格式支持使用示例
演示如何使用新的文件格式读取功能
"""
import os
import sys
from pathlib import Path

def create_sample_data():
    """创建示例数据文件"""
    print("📁 创建示例数据文件...")
    
    # 创建数据目录
    data_dir = Path("sample_data")
    data_dir.mkdir(exist_ok=True)
    
    # 1. 创建JSON配置文件
    import json
    config_data = {
        "project": "AI报告生成器",
        "version": "2.0",
        "features": [
            "多格式文件读取",
            "智能内容分析",
            "自动报告生成"
        ],
        "settings": {
            "max_file_size": "100MB",
            "supported_languages": ["中文", "English"],
            "output_format": "markdown"
        }
    }
    
    with open(data_dir / "config.json", "w", encoding="utf-8") as f:
        json.dump(config_data, f, ensure_ascii=False, indent=2)
    
    # 2. 创建YAML配置文件
    yaml_content = """# 项目配置文件
project:
  name: AI报告生成器
  description: 支持多种文件格式的智能报告生成工具
  
features:
  file_formats:
    - PDF文件读取
    - Office文档支持
    - 代码文件解析
    - 配置文件读取
  
  ai_capabilities:
    - 内容理解
    - 结构分析
    - 智能总结
    
settings:
  performance:
    max_concurrent: 8
    timeout: 300
  quality:
    min_content_length: 100
    max_iterations: 3"""
    
    with open(data_dir / "settings.yaml", "w", encoding="utf-8") as f:
        f.write(yaml_content)
    
    # 3. 创建CSV数据文件
    csv_content = """文件格式,支持状态,读取方法,备注
PDF,.pdf,多种方法,支持OCR
Word,.docx,python-docx,完整支持
Excel,.xlsx,pandas,支持多工作表
PowerPoint,.pptx,python-pptx,提取文本
JSON,.json,内置json,结构化数据
XML,.xml,ElementTree,层次数据
YAML,.yaml,PyYAML,配置文件
CSV,.csv,pandas,表格数据
Markdown,.md,文本读取,格式化文本
Python,.py,文本读取,代码文件"""
    
    with open(data_dir / "file_support.csv", "w", encoding="utf-8") as f:
        f.write(csv_content)
    
    # 4. 创建Markdown文档
    md_content = """# AI报告生成器功能说明

## 概述
AI报告生成器现在支持读取多种文件格式，大大扩展了数据源的范围。

## 支持的文件格式

### 文档格式
- **PDF文件**: 支持文本提取和OCR识别
- **Word文档**: 支持.docx和.doc格式
- **PowerPoint**: 支持.pptx和.ppt格式
- **Excel表格**: 支持.xlsx和.xls格式

### 数据格式
- **JSON**: 结构化数据，完整解析
- **XML**: 层次化数据，递归提取
- **YAML**: 配置文件，格式化输出
- **CSV/TSV**: 表格数据，pandas处理

### 代码文件
- **Python**: .py文件
- **JavaScript**: .js文件
- **HTML/CSS**: 网页文件
- **SQL**: 数据库脚本
- **其他**: Java, C++, PHP, Go等

### 配置文件
- **INI**: 传统配置格式
- **TOML**: 现代配置格式
- **Properties**: Java配置文件

## 使用方法

```python
from complete_report_generator import CompleteReportGenerator

# 初始化生成器
generator = CompleteReportGenerator()

# 读取数据源（自动识别文件格式）
content = generator.read_data_source("your_data_directory")

# 生成报告
generator.generate_complete_report(
    topic="您的报告主题",
    data_source="your_data_directory"
)
```

## 优势特点

1. **自动格式识别**: 根据文件扩展名自动选择合适的读取方法
2. **编码兼容**: 支持UTF-8、GBK等多种编码
3. **错误处理**: 单个文件读取失败不影响整体处理
4. **内容整合**: 多个文件内容自动合并和标记
5. **扩展性强**: 易于添加新的文件格式支持

## 注意事项

- 某些格式需要安装额外的Python库
- 大文件可能需要较长的处理时间
- OCR功能需要安装Tesseract引擎
"""
    
    with open(data_dir / "README.md", "w", encoding="utf-8") as f:
        f.write(md_content)
    
    # 5. 创建Python代码示例
    py_content = '''"""
示例Python代码文件
演示代码文件的读取功能
"""

class FileProcessor:
    """文件处理器类"""
    
    def __init__(self, supported_formats):
        self.supported_formats = supported_formats
        self.processed_count = 0
    
    def process_file(self, file_path):
        """处理单个文件"""
        try:
            # 检查文件格式
            if self._is_supported(file_path):
                content = self._read_file(file_path)
                self.processed_count += 1
                return content
            else:
                raise ValueError(f"不支持的文件格式: {file_path}")
        except Exception as e:
            print(f"处理文件失败: {e}")
            return None
    
    def _is_supported(self, file_path):
        """检查是否支持该文件格式"""
        extension = file_path.suffix.lower()
        return extension in self.supported_formats
    
    def _read_file(self, file_path):
        """读取文件内容"""
        with open(file_path, 'r', encoding='utf-8') as f:
            return f.read()

# 使用示例
if __name__ == "__main__":
    processor = FileProcessor(['.txt', '.md', '.py', '.json'])
    result = processor.process_file("example.py")
    print(f"处理结果: {result}")
'''
    
    with open(data_dir / "example_code.py", "w", encoding="utf-8") as f:
        f.write(py_content)
    
    print(f"✅ 示例数据文件创建完成，位于: {data_dir.absolute()}")
    return data_dir

def demonstrate_reading():
    """演示文件读取功能"""
    print("\n🔍 演示扩展文件格式读取功能")
    print("=" * 60)
    
    try:
        from complete_report_generator import CompleteReportGenerator
        
        # 创建示例数据
        data_dir = create_sample_data()
        
        # 初始化生成器
        generator = CompleteReportGenerator(use_async=False)
        
        # 读取数据源
        print(f"\n📖 读取数据源: {data_dir}")
        content = generator.read_data_source(str(data_dir))
        
        if content:
            print(f"✅ 读取成功!")
            print(f"📊 总内容长度: {len(content)} 字符")
            
            # 统计文件数量
            file_count = content.count("=== 文件:")
            print(f"📄 读取文件数量: {file_count}")
            
            # 显示内容摘要
            print(f"\n📝 内容摘要:")
            lines = content.split('\n')[:20]  # 显示前20行
            for line in lines:
                if line.strip():
                    print(f"   {line}")
            
            if len(content.split('\n')) > 20:
                print(f"   ... (还有 {len(content.split('\n')) - 20} 行)")
            
        else:
            print("❌ 读取失败或内容为空")
        
        # 清理示例文件
        print(f"\n🧹 清理示例文件...")
        import shutil
        if data_dir.exists():
            shutil.rmtree(data_dir)
            print("✅ 清理完成")
        
        return content is not None and len(content) > 0
        
    except ImportError as e:
        print(f"❌ 导入失败: {str(e)}")
        print("💡 请先运行 python install_extended_dependencies.py 安装依赖")
        return False
    except Exception as e:
        print(f"❌ 演示失败: {str(e)}")
        return False

if __name__ == "__main__":
    print("🚀 AI报告生成器 - 扩展文件格式支持演示")
    print("=" * 60)
    
    success = demonstrate_reading()
    
    if success:
        print(f"\n🎉 演示成功！扩展文件格式支持功能正常工作。")
        print(f"\n💡 使用提示:")
        print(f"   1. 将各种格式的文件放在数据目录中")
        print(f"   2. 调用 generator.read_data_source(目录路径)")
        print(f"   3. 系统会自动识别并读取所有支持的文件格式")
        print(f"   4. 生成报告时会整合所有文件的内容")
    else:
        print(f"\n💡 如果演示失败，请:")
        print(f"   1. 运行: python install_extended_dependencies.py")
        print(f"   2. 确保所有依赖库安装成功")
        print(f"   3. 重新运行此演示脚本")
    
    sys.exit(0 if success else 1)
