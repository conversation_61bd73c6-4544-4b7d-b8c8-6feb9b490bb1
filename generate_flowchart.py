import requests
import base64

# 读取PlantUML文件内容
with open('report_generator_flow.puml', 'r', encoding='utf-8') as f:
    plantuml_content = f.read()

# 将PlantUML内容编码为Base64
plantuml_bytes = plantuml_content.encode('utf-8')
encoded_content = base64.b64encode(plantuml_bytes).decode('utf-8')

# 构建PlantUML服务器URL
plantuml_url = f"https://www.plantuml.com/plantuml/png/{encoded_content}"

# 发送请求获取PNG图像
response = requests.get(plantuml_url)

# 保存PNG图像
if response.status_code == 200:
    with open('report_generator_flow.png', 'wb') as f:
        f.write(response.content)
    print("Flowchart generated successfully as report_generator_flow.png")
else:
    print(f"Failed to generate flowchart. Status code: {response.status_code}")
