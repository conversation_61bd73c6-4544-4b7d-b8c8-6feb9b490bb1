@startuml
start

partition "程序初始化" {
  :创建基础目录和模板;
  :获取用户输入配置;
  note right: 包括主题、框架文件路径\n数据源、标题数量等配置
}

partition "Checkpoint检查" {
  :检查可用的checkpoint;
  if ("存在可用checkpoint?") then (是)
    :选择恢复checkpoint;
    :从checkpoint恢复报告生成;
    stop
  else (否)
    :继续正常流程;
  endif
}

partition "报告生成器初始化" {
  :验证和准备数据源;
  if ("使用默认配置?") then (是)
    :创建示例数据;
  else (否)
    :使用用户提供的数据;
  endif
  :创建报告生成器实例;
  note right: 支持同步/异步模式\n配置Token限制等参数
}

partition "报告生成主流程" {
  :开始生成报告;
  
  if ("需要从checkpoint恢复?") then (是)
    :从checkpoint恢复流程;
  else (否)
    :全新生成流程;
  endif
  
  partition "第一阶段: 框架生成" {
    :读取框架文件;
    :生成一级标题框架;
    :生成完整子结构;
    note right: 统筹模型(gemini-2.5-pro)\n生成完整的标题结构
  }
  
  partition "第二阶段: 内容生成" {
    :执行模型生成具体内容;
    note right: 执行模型(gemini-2.5-flash)\n按框架生成各章节内容
  }
  
  partition "第三阶段: 迭代优化" {
    repeat
      :保存当前版本;
      
      partition "章节审核优化" {
        repeat
          :统筹模型审核章节;
          :统筹模型优化章节;
        repeat while ("完成所有章节?")
      }
      
      partition "整体文档优化" {
        :统筹模型审核整体文档;
        :统筹模型优化整体文档;
      }
      
      :保存优化后版本;
    repeat while ("完成3轮迭代?")
  }
  
  partition "第四阶段: 字数控制" {
    :控制最终报告字数;
    note right: 根据目标字数压缩内容
  }
  
  partition "第五阶段: 输出生成" {
    :生成Word文档;
    :生成Markdown文档;
    :嵌入图片(可选);
    :搜索增强(可选);
  }
}

:输出最终报告文件;
stop

partition "Checkpoint管理" {
  :保存checkpoint;
  note right: 在关键节点保存\n用于断点续传
  :清理旧checkpoint;
}

@enduml
