#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Gemini API管理器模块
完全按照原代码实现，支持同步和异步调用，API轮换机制
"""

import time
import threading
import asyncio
import math
from typing import List, Tuple, Any, Dict, Optional
import google.generativeai as genai

try:
    from ..core.config import API_KEYS, MODEL_NAMES, MAX_RETRIES, REQUEST_TIMEOUT
except ImportError:
    from core.config import API_KEYS, MODEL_NAMES, MAX_RETRIES, REQUEST_TIMEOUT


class GeminiAPIManager:
    """API轮换管理器"""
    
    def __init__(self, api_keys: List[str], model_names: List[str]):
        self.api_configs = []
        
        for i, key in enumerate(api_keys): 
            if key and "YOUR_GEMINI_API_KEY" not in key and len(key) > 10:
                self.api_configs.append({
                    "name": f"API Key {i+1}",
                    "key": key,
                    "models": model_names,
                    "current_model_index": 0
                })
        
        if not self.api_configs:
            raise ValueError("FATAL: No valid Google API keys are configured.")
        
        self.total_keys = len(self.api_configs)
        self.current_api_index = 0
        self.lock = threading.Lock()
        self.max_rotations = 10000  # 大幅增加轮换限制
        self.total_rotations_completed = 0
        self.usage_counts = {i: 0 for i in range(self.total_keys)}
        self.consecutive_cleanup_counts = {i: 0 for i in range(self.total_keys)}
        self.api_call_counts = {i: 0 for i in range(self.total_keys)}
        
        print(f"Gemini API Manager initialized with {self.total_keys} active keys.")
        print(f"Each key will rotate through {len(model_names)} models: {model_names}")
        
        if self.total_keys > 0:
            self._log_current_key_status()
    
    def _log_current_key_status(self):
        """记录当前密钥状态"""
        print(f"\n--- 所有API密钥状态 (已完成轮换: {self.total_rotations_completed}/{self.max_rotations}) ---")
        for i in range(self.total_keys):
            config = self.api_configs[i]
            status = "🔴 当前" if i == self.current_api_index else "⚪️ 待用"
            current_model = config['models'][config['current_model_index']]
            print(f" {status} {config['name']}: 模型='{current_model}', "
                  f"成功处理={self.usage_counts[i]}, "
                  f"API总调用={self.api_call_counts[i]}")
        print("-----------------------------------------------------------\n")
    
    def record_successful_processing(self, key_index: int):
        """记录成功处理"""
        with self.lock:
            self.usage_counts[key_index] += 1
            self.consecutive_cleanup_counts[key_index] = 0
    
    def generate_content_with_model(self, prompt: str, model_name: str) -> Tuple[Any, int]:
        """使用指定模型生成内容"""
        with self.lock:
            if self.total_rotations_completed >= self.max_rotations:
                raise Exception(f"已完成 {self.max_rotations} 轮完整的API密钥轮换，程序终止。")
            
            if self.total_keys == 0:
                raise Exception("No configured API keys to use for generation.")
        
        max_attempts = self.total_keys + 1
        
        for _ in range(max_attempts):
            with self.lock:
                current_api_index = self.current_api_index
                api_config = self.api_configs[current_api_index]
                api_key = api_config["key"]
                api_name = api_config["name"]
                self.api_call_counts[current_api_index] += 1
            
            # 从prompt中提取任务信息
            try:
                task_purpose = self._extract_task_purpose(prompt)
            except Exception as e:
                task_purpose = f"任务信息提取失败: {str(e)[:50]}"
            print(f"\n[API调用] 使用: {api_name} | 模型: {model_name} | 调用数: {self.api_call_counts[current_api_index]}")
            print(f"[任务目的] {task_purpose}")

            # 显示prompt摘要（前100字符）
            prompt_summary = prompt.replace('\n', ' ')[:100] + "..." if len(prompt) > 100 else prompt.replace('\n', ' ')
            print(f"[任务内容] {prompt_summary}")
            
            try:
                genai.configure(api_key=api_key)
                
                generation_config = genai.types.GenerationConfig(
                    temperature=0.0,
                    max_output_tokens=1000000
                )
                
                model = genai.GenerativeModel(model_name, generation_config=generation_config)
                response = model.generate_content([prompt])
                
                return response, current_api_index
                
            except Exception as e:
                error_msg = str(e).lower()
                print(f"!!!!!! ERROR with {api_name} using model {model_name}: {error_msg} !!!!!!")
                
                if "quota" in error_msg or "limit" in error_msg or "permission" in error_msg or "resource_exhausted" in error_msg:
                    print(f"  -> 检测到配额/权限问题，切换到下一个API密钥...")
                    with self.lock:
                        self.current_api_index = (self.current_api_index + 1) % self.total_keys
                    time.sleep(1)
                    continue
                else:
                    print(f"  -> 非配额问题，切换到下一个API密钥。")
                    with self.lock:
                        self.current_api_index = (self.current_api_index + 1) % self.total_keys
                    time.sleep(2)
                    continue
        
        raise Exception(f"所有API密钥都失败了，已尝试 {max_attempts} 次")

    def _extract_task_purpose(self, prompt: str, context: dict = None) -> str:
        """从prompt中提取详细的任务目的"""
        prompt_lower = prompt.lower()

        if "框架" in prompt and "json" in prompt_lower:
            return "🎯 统筹模型生成报告框架结构"
        elif "审核" in prompt and "章节" in prompt:
            try:
                title = self._extract_title_from_prompt(prompt)
            except:
                title = "未知章节"
            return f"🔍 统筹模型审核章节: {title}"
        elif "优化" in prompt and "章节" in prompt:
            try:
                title = self._extract_title_from_prompt(prompt)
            except:
                title = "未知章节"
            return f"✨ 统筹模型优化章节: {title}"
        elif "生成" in prompt and ("内容" in prompt or "详细" in prompt):
            try:
                title = self._extract_title_from_prompt(prompt)
                level = self._extract_level_from_prompt(prompt)
            except:
                title = "未知节点"
                level = "未知级别"
            return f"⚡ 执行模型生成第{level}级节点: {title}"
        else:
            return "🤖 AI模型执行任务"

    def _extract_title_from_prompt(self, prompt: str) -> str:
        """从prompt中提取标题"""
        import re
        title_patterns = [
            r'"([^"]+)"',
            r'「([^」]+)」',
            r'《([^》]+)》',
            r'【([^】]+)】'
        ]

        for pattern in title_patterns:
            match = re.search(pattern, prompt)
            if match:
                return match.group(1)

        if "章节标题：" in prompt:
            start = prompt.find("章节标题：") + 5
            end = prompt.find("\n", start)
            if end == -1:
                end = start + 50
            return prompt[start:end].strip()

        return "未知标题"

    def _extract_level_from_prompt(self, prompt: str) -> str:
        """从prompt中提取级别"""
        import re
        level_match = re.search(r'第(\d+)级', prompt)
        if level_match:
            return level_match.group(1)
        return "未知级别"


class AsyncGeminiAPIManager:
    """异步API管理器 - 完全按照原代码实现"""

    def __init__(self, api_keys: List[str], model_names: List[str]):
        self.api_configs = []

        for i, key in enumerate(api_keys):
            if key and "YOUR_GEMINI_API_KEY" not in key and len(key) > 10:
                self.api_configs.append({
                    "name": f"API Key {i+1}",
                    "key": key,
                    "models": model_names,
                    "current_model_index": 0,
                    "semaphore": asyncio.Semaphore(1)
                })

        if not self.api_configs:
            raise ValueError("FATAL: No valid Google API keys are configured.")

        self.total_keys = len(self.api_configs)
        self.current_api_index = 0
        self.index_lock = threading.Lock()
        self.max_rotations = 10000
        self.total_rotations_completed = 0
        self.usage_counts = {i: 0 for i in range(self.total_keys)}
        self.consecutive_cleanup_counts = {i: 0 for i in range(self.total_keys)}
        self.api_call_counts = {i: 0 for i in range(self.total_keys)}
        self.success_counts = {i: 0 for i in range(self.total_keys)}

        for i, config in enumerate(self.api_configs):
            config.update({
                "error_count": 0,
                "last_error_time": 0,
                "is_available": True
            })

        print(f"Async Gemini API Manager initialized with {self.total_keys} active keys.")
        print(f"Each key supports max 1 concurrent request, total max concurrent: {self.total_keys}")

        if self.total_keys > 0:
            self._log_current_key_status()

    def _log_current_key_status(self):
        """记录当前密钥状态"""
        print(f"\n--- 所有API密钥状态 (已完成轮换: {self.total_rotations_completed}/{self.max_rotations}) ---")
        for i in range(self.total_keys):
            config = self.api_configs[i]
            status = "🔴 当前" if i == self.current_api_index else "⚪️ 待用"
            current_model = config['models'][config['current_model_index']]
            print(f" {status} {config['name']}: 模型='{current_model}', "
                  f"成功处理={self.usage_counts[i]}, "
                  f"API总调用={self.api_call_counts[i]}")
        print("-----------------------------------------------------------\n")

    async def generate_content_with_model_async(self, prompt: str, model_name: str) -> Tuple[Any, int]:
        """异步生成内容"""
        max_retries = 2
        retry_delay = 1

        for retry in range(max_retries):
            api_config = self._get_available_api_config()

            if api_config is None:
                if retry < max_retries - 1:
                    await asyncio.sleep(retry_delay)
                    retry_delay *= 2
                    continue
                else:
                    if self.api_configs:
                        api_config = {
                            "api_index": 0,
                            "api_name": self.api_configs[0]["name"],
                            "api_key": self.api_configs[0]["key"],
                            "model_name": model_name,
                            "semaphore": self.api_configs[0]["semaphore"]
                        }
                    else:
                        raise Exception("没有可用的API密钥")

            api_index = api_config["api_index"]
            semaphore = api_config["semaphore"]

            try:
                await asyncio.wait_for(semaphore.acquire(), timeout=30)
                try:
                    with self.index_lock:
                        self.api_call_counts[api_index] += 1

                    task_purpose = "🤖 AI模型执行任务"
                    print(f"\n[异步API调用] 使用: {api_config['api_name']} | 模型: {model_name}")
                    print(f"[任务目的] {task_purpose}")

                    loop = asyncio.get_event_loop()
                    response = await asyncio.wait_for(
                        loop.run_in_executor(
                            None,
                            self._sync_api_call,
                            api_config["api_key"],
                            model_name,
                            prompt
                        ),
                        timeout=120
                    )

                    with self.index_lock:
                        self.usage_counts[api_index] += 1
                        self.success_counts[api_index] += 1

                    return response, api_index

                finally:
                    semaphore.release()

            except Exception as e:
                if retry < max_retries - 1:
                    await asyncio.sleep(retry_delay)
                    retry_delay *= 2
                    continue
                else:
                    raise e

        raise Exception(f"API调用失败，已重试 {max_retries} 次")

    def _get_available_api_config(self) -> Optional[Dict[str, Any]]:
        """获取可用的API配置"""
        with self.index_lock:
            if self.total_keys == 0:
                return None

            api_config = self.api_configs[self.current_api_index]
            model_name = api_config["models"][api_config["current_model_index"]]
            
            result = {
                "api_index": self.current_api_index,
                "api_name": api_config["name"],
                "api_key": api_config["key"],
                "model_name": model_name,
                "semaphore": api_config["semaphore"]
            }

            self.current_api_index = (self.current_api_index + 1) % self.total_keys
            return result

    def _sync_api_call(self, api_key: str, model_name: str, prompt: str):
        """同步API调用"""
        genai.configure(api_key=api_key)
        generation_config = genai.types.GenerationConfig(
            temperature=0.0,
            max_output_tokens=8192
        )
        model = genai.GenerativeModel(model_name, generation_config=generation_config)
        response = model.generate_content([prompt])
        return response

    def generate_content_with_model(self, prompt: str, model_name: str) -> Tuple[Any, int]:
        """同步接口兼容"""
        try:
            loop = asyncio.get_running_loop()
            import concurrent.futures

            def run_in_thread():
                new_loop = asyncio.new_event_loop()
                asyncio.set_event_loop(new_loop)
                try:
                    return new_loop.run_until_complete(
                        self.generate_content_with_model_async(prompt, model_name)
                    )
                finally:
                    new_loop.close()

            with concurrent.futures.ThreadPoolExecutor() as executor:
                future = executor.submit(run_in_thread)
                return future.result()

        except RuntimeError:
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            try:
                return loop.run_until_complete(self.generate_content_with_model_async(prompt, model_name))
            finally:
                loop.close()

    async def record_successful_processing(self, key_index: int):
        """记录成功处理"""
        pass

    # ==================== 原代码中的复杂功能补充 ====================

    def _extract_task_purpose(self, prompt: str, context: dict = None) -> str:
        """从prompt中提取详细的任务目的（完全按原代码）"""
        prompt_lower = prompt.lower()

        if "框架" in prompt and "json" in prompt_lower:
            return "🎯 统筹模型生成报告框架结构"
        elif "审核" in prompt and "章节" in prompt:
            # 提取章节标题
            try:
                title = self._extract_title_from_prompt(prompt)
            except:
                title = "未知章节"
            return f"🔍 统筹模型审核章节: {title}"
        elif "优化" in prompt and "章节" in prompt:
            try:
                title = self._extract_title_from_prompt(prompt)
            except:
                title = "未知章节"
            return f"✨ 统筹模型优化章节: {title}"
        elif "审核" in prompt and "整体" in prompt:
            return "📄 统筹模型审核整体文档质量"
        elif "优化" in prompt and "整体" in prompt:
            return "🔧 统筹模型优化整体文档结构"
        elif "ocr" in prompt_lower or "图片" in prompt or "pdf" in prompt_lower:
            return "🖼️ Gemini OCR处理PDF图片内容"
        elif "生成" in prompt and ("内容" in prompt or "详细" in prompt):
            # 提取节点标题和级别
            try:
                title = self._extract_title_from_prompt(prompt)
                level = self._extract_level_from_prompt(prompt)
            except:
                title = "未知节点"
                level = "未知级别"
            return f"⚡ 执行模型生成第{level}级节点: {title}"
        elif "第" in prompt and "级" in prompt:
            level_match = prompt.find("第") + 1
            if level_match < len(prompt):
                level_char = prompt[level_match]
                try:
                    title = self._extract_title_from_prompt(prompt)
                except:
                    title = "未知标题"
                return f"📝 执行模型生成第{level_char}级标题: {title}"
        else:
            return "🤖 AI模型执行任务"

    def _extract_title_from_prompt(self, prompt: str) -> str:
        """从prompt中提取标题（完全按原代码）"""
        # 查找引号中的标题
        import re

        # 匹配 "标题" 或 「标题」
        title_patterns = [
            r'"([^"]+)"',
            r'「([^」]+)」',
            r'《([^》]+)》',
            r'【([^】]+)】'
        ]

        for pattern in title_patterns:
            match = re.search(pattern, prompt)
            if match:
                return match.group(1)

        # 如果没有找到引号，尝试查找"章节标题："后的内容
        if "章节标题：" in prompt:
            start = prompt.find("章节标题：") + 5
            end = prompt.find("\n", start)
            if end == -1:
                end = start + 50
            return prompt[start:end].strip()

        return "未知标题"

    def _extract_level_from_prompt(self, prompt: str) -> str:
        """从prompt中提取级别（完全按原代码）"""
        import re

        # 查找"第X级"
        level_match = re.search(r'第(\d+)级', prompt)
        if level_match:
            return level_match.group(1)

        return "未知级别"

    def _get_available_api_config(self) -> Optional[Dict[str, Any]]:
        """获取可用的API配置（优化版，完全按原代码）"""
        with self.index_lock:
            if self.total_rotations_completed >= self.max_rotations:
                raise Exception(f"已完成 {self.max_rotations} 轮完整的API密钥轮换，程序终止。")

            if self.total_keys == 0:
                raise Exception("No configured API keys to use for generation.")

            attempts = 0
            while attempts < self.total_keys:
                api_config = self.api_configs[self.current_api_index]

                # 检查API是否可用
                if (api_config.get("is_available", True) and
                    not api_config["semaphore"].locked() and
                    api_config.get("error_count", 0) < 5):

                    # 找到可用的API
                    model_name = api_config["models"][api_config["current_model_index"]]
                    result = {
                        "api_index": self.current_api_index,
                        "api_name": api_config["name"],
                        "api_key": api_config["key"],
                        "model_name": model_name,
                        "semaphore": api_config["semaphore"]
                    }

                    # 移动到下一个API（负载均衡）
                    self.current_api_index = (self.current_api_index + 1) % self.total_keys
                    return result

                # 尝试下一个API
                self.current_api_index = (self.current_api_index + 1) % self.total_keys
                attempts += 1

            # 如果所有API都不可用，重置错误状态
            self._reset_error_states()
            return None

    def _reset_error_states(self):
        """重置API错误状态（完全按原代码）"""
        import time
        current_time = time.time()
        with self.index_lock:
            for config in self.api_configs:
                # 如果距离上次错误超过5分钟，重置状态
                if current_time - config.get("last_error_time", 0) > 300:
                    config["error_count"] = 0
                    config["is_available"] = True

    def _mark_api_error(self, api_index: int, error_msg: str):
        """标记API错误（完全按原代码）"""
        import time
        with self.index_lock:
            if api_index < len(self.api_configs):
                config = self.api_configs[api_index]
                config["error_count"] = config.get("error_count", 0) + 1
                config["last_error_time"] = time.time()

                # 如果错误次数过多，暂时禁用
                if config["error_count"] >= 5:
                    config["is_available"] = False
                    print(f"⚠️ API {config['name']} 暂时禁用（错误次数过多）")
