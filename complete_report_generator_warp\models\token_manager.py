"""
Token管理器模块 - 严格按照原始代码的TokenManager实现
处理文本的token计算、分割和管理
"""

import tiktoken
from typing import List, Dict, Any, Tuple


class TokenManager:
    """Token管理器 - 与原始代码完全相同的实现"""
    
    def __init__(self):
        """初始化Token管理器"""
        self.encoding = tiktoken.get_encoding("cl100k_base")
    
    def count_tokens(self, text: str) -> int:
        """计算文本的token数量 - 与原始代码完全相同"""
        if not text:
            return 0
        return len(self.encoding.encode(text))
    
    def split_text_by_tokens(self, text: str, max_tokens: int) -> List[str]:
        """
        按token数量分割文本 - 与原始代码完全相同
        
        Args:
            text: 要分割的文本
            max_tokens: 每个片段的最大token数
            
        Returns:
            分割后的文本片段列表
        """
        if not text:
            return []
        
        tokens = self.encoding.encode(text)
        if len(tokens) <= max_tokens:
            return [text]
        
        chunks = []
        for i in range(0, len(tokens), max_tokens):
            chunk_tokens = tokens[i:i + max_tokens]
            chunk_text = self.encoding.decode(chunk_tokens)
            chunks.append(chunk_text)
        
        return chunks
    
    def truncate_to_tokens(self, text: str, max_tokens: int) -> str:
        """
        截断文本到指定的token数量 - 与原始代码完全相同
        
        Args:
            text: 要截断的文本
            max_tokens: 最大token数量
            
        Returns:
            截断后的文本
        """
        if not text:
            return ""
        
        tokens = self.encoding.encode(text)
        if len(tokens) <= max_tokens:
            return text
        
        truncated_tokens = tokens[:max_tokens]
        return self.encoding.decode(truncated_tokens)
    
    def estimate_tokens_from_words(self, word_count: int) -> int:
        """
        根据字数估算token数量 - 与原始代码完全相同
        中文大约1.5个字符对应1个token
        
        Args:
            word_count: 字数
            
        Returns:
            估算的token数量
        """
        return int(word_count / 1.5)
    
    def estimate_words_from_tokens(self, token_count: int) -> int:
        """
        根据token数量估算字数 - 与原始代码完全相同
        中文大约1.5个字符对应1个token
        
        Args:
            token_count: token数量
            
        Returns:
            估算的字数
        """
        return int(token_count * 1.5)
