#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
AI报告生成器 - 完整版本 v0728
模块化重构版本

主要功能：
- 智能报告生成
- 异步并行处理
- 搜索增强
- 图片嵌入
- 内容优化
"""

from .core.generator import CompleteReportGenerator
from .core.config import ReportConfig
from .api.gemini_manager import GeminiAPIManager, AsyncGeminiAPIManager
from .utils.token_manager import TokenManager
from .search.search_manager import SearchManager
from .search.search_trigger import SearchTrigger
from .content.content_processor import ContentProcessor
from .image.image_processor import ImageProcessor

__version__ = "1.0.0"
__author__ = "AI Report Generator Team"

__all__ = [
    "CompleteReportGenerator",
    "ReportConfig", 
    "GeminiAPIManager",
    "AsyncGeminiAPIManager",
    "TokenManager",
    "SearchManager",
    "SearchTrigger",
    "ContentProcessor",
    "ImageProcessor"
]
