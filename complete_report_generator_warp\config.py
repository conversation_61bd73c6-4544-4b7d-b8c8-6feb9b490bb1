"""
配置文件 - 包含所有系统常量和API配置
严格按照原始代码的配置内容
"""

import os
from typing import List, Dict, Any

# API配置 - 完全按照原始代码
OPENAI_BASE_URL = "https://api.openai.com/v1"
DEEPSEEK_BASE_URL = "https://api.deepseek.com"
GEMINI_BASE_URL = "https://generativelanguage.googleapis.com/v1beta/models"

# 获取API密钥 - 与原始代码完全相同
DEEPSEEK_API_KEY = os.getenv("DEEPSEEK_API_KEY", "")
GEMINI_API_KEY = os.getenv("GEMINI_API_KEY", "")
METASO_API_KEY = os.getenv("METASO_API_KEY", "")
GOOGLE_API_KEY = os.getenv("GOOGLE_API_KEY", "")
GOOGLE_CSE_ID = os.getenv("GOOGLE_CSE_ID", "")
BING_SUBSCRIPTION_KEY = os.getenv("BING_SUBSCRIPTION_KEY", "")

# 模型配置 - 完全按照原始代码
ORCHESTRATOR_MODEL = "deepseek-chat"
EXECUTOR_MODEL = "gemini-1.5-flash-002"

# Token限制 - 与原始代码相同
MAX_TOKENS_EXECUTOR = 20000
MAX_TOKENS_ORCHESTRATOR = 8000

# 重试配置 - 与原始代码相同
MAX_RETRIES = 5
RETRY_DELAY = 2

# 并发配置 - 与原始代码相同
MAX_CONCURRENT_TASKS = 3

# 搜索配置 - 与原始代码相同
SEARCH_ENGINES = ["metaso", "google", "bing"]
MAX_SEARCH_RESULTS = 10

# 输出配置 - 与原始代码相同
OUTPUT_DIR = "output"
CACHE_DIR = "cache"
CHECKPOINT_DIR = "checkpoints"

# 文件类型支持 - 与原始代码相同
SUPPORTED_FILE_TYPES = [
    '.txt', '.md', '.pdf', '.docx', '.xlsx', '.csv', 
    '.pptx', '.json', '.xml', '.html', '.py', '.java',
    '.cpp', '.js', '.ts', '.go', '.rs', '.sql'
]

# PDF处理配置 - 与原始代码相同
PDF_IMAGE_DPI = 150
PDF_IMAGE_FORMAT = 'PNG'
MAX_PDF_PAGES_PER_BATCH = 50

# 图片配置 - 与原始代码相同
IMAGE_EXTENSIONS = ['.png', '.jpg', '.jpeg', '.gif', '.bmp', '.svg', '.webp']
MAX_IMAGE_SIZE_MB = 10
IMAGE_COMPRESSION_QUALITY = 85

# 报告生成配置 - 与原始代码相同
DEFAULT_PRIMARY_SECTIONS = 8
DEFAULT_MAX_DEPTH = 6
DEFAULT_TARGET_WORDS = 50000
DEFAULT_MAX_TOKENS = 250000

# 优化配置 - 与原始代码相同
OPTIMIZATION_ITERATIONS = 3
OPTIMIZATION_TYPES = [
    "structure",
    "consistency", 
    "redundancy",
    "coherence",
    "data_consistency"
]

# 审核评分阈值 - 与原始代码相同
AUDIT_SCORE_THRESHOLD = 7.5
SECTION_SCORE_THRESHOLD = 7.0

# 日志配置 - 与原始代码相同
LOG_FORMAT = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
LOG_LEVEL = 'INFO'

# 缓存配置 - 与原始代码相同
CACHE_EXPIRY_DAYS = 7
ENABLE_CACHE = True

# 检查点配置 - 与原始代码相同
CHECKPOINT_INTERVAL = 5  # 每5个节点保存一次检查点
ENABLE_CHECKPOINTS = True

# Word文档样式配置 - 与原始代码相同
WORD_STYLES = {
    'title': {'font_size': 22, 'bold': True, 'color': '000000'},
    'heading_1': {'font_size': 18, 'bold': True, 'color': '1F4788'},
    'heading_2': {'font_size': 16, 'bold': True, 'color': '2F5496'},
    'heading_3': {'font_size': 14, 'bold': True, 'color': '3F6AAA'},
    'heading_4': {'font_size': 12, 'bold': True, 'color': '4F7FBE'},
    'heading_5': {'font_size': 11, 'bold': True, 'color': '5F95D2'},
    'heading_6': {'font_size': 10, 'bold': True, 'color': '6FAAE6'},
    'normal': {'font_size': 10.5, 'color': '000000'}
}

# 字体配置 - 与原始代码相同
DEFAULT_FONT = '微软雅黑'
DEFAULT_FONT_EN = 'Calibri'

# API费率限制配置（请求/分钟）- 与原始代码相同
API_RATE_LIMITS = {
    'deepseek': 60,
    'gemini': 60,
    'openai': 60,
    'metaso': 30,
    'google': 100,
    'bing': 30
}

# Gemini API配置 - 与原始代码相同
GEMINI_MODELS = [
    "gemini-1.5-flash-002",
    "gemini-1.5-flash-latest", 
    "gemini-1.5-pro-latest",
    "gemini-1.5-pro-002"
]

# 提示词模板路径 - 与原始代码相同
PROMPT_TEMPLATES_DIR = "templates/prompts"

# 数据源默认路径 - 与原始代码相同
DEFAULT_DATA_SOURCES = [
    "market_overview", "technology_trends", "competitive_landscape",
    "regulatory_environment", "investment_analysis", "future_outlook",
    "challenges", "recommendations", "risk_analysis", "case_studies",
    "market_segmentation", "supply_chain", "innovation_trends",
    "policy_impact", "financial_analysis", "strategic_planning",
    "sustainability", "global_perspective", "emerging_technologies", "conclusions"
]

# 章节标题模板 - 与原始代码相同
SECTION_TEMPLATES = [
    ("市场概览与现状分析", "市场规模与增长趋势", "全球市场规模", "市场总量分析", "历史数据回顾"),
    ("技术发展趋势", "核心技术分析", "关键技术突破", "技术创新点", "技术原理解析"),
    ("竞争格局分析", "市场参与者分析", "主要厂商概况", "领先企业分析", "企业战略定位"),
    ("政策环境分析", "政策法规体系", "国际政策环境", "主要国家政策", "政策内容解读"),
    ("投资与融资分析", "投资规模与结构", "投资总量分析", "投资规模变化", "投资增长趋势"),
    ("未来发展展望", "发展趋势预测", "市场发展预测", "市场规模预测", "增长驱动因素"),
    ("风险挑战分析", "市场风险分析", "需求波动风险", "市场不确定性", "风险因素识别"),
    ("发展建议与策略", "政策建议", "政府层面建议", "政策支持建议", "具体政策措施"),
    ("风险评估与管控", "风险识别体系", "主要风险因素", "风险评估方法", "风险管控策略"),
    ("案例研究分析", "典型案例分析", "成功案例研究", "失败案例分析", "经验教训总结"),
    ("市场细分研究", "细分市场分析", "目标市场定位", "市场需求特征", "竞争优势分析"),
    ("供应链分析", "供应链结构", "关键供应商分析", "供应链风险", "供应链优化"),
    ("创新趋势分析", "技术创新方向", "产品创新趋势", "商业模式创新", "创新生态系统"),
    ("政策影响评估", "政策环境变化", "政策影响分析", "政策风险评估", "政策建议"),
    ("财务分析", "财务状况分析", "盈利能力分析", "财务风险评估", "投资回报分析"),
    ("战略规划建议", "发展战略制定", "战略实施路径", "资源配置建议", "战略风险管控"),
    ("可持续发展", "环境影响评估", "社会责任分析", "可持续发展策略", "ESG评估"),
    ("全球化视角", "国际市场分析", "全球化趋势", "国际合作机会", "全球化挑战"),
    ("新兴技术影响", "前沿技术分析", "技术融合趋势", "技术应用前景", "技术风险评估"),
    ("结论与展望", "主要结论总结", "发展前景展望", "关键建议汇总", "未来研究方向")
]
