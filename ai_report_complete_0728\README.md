# AI报告生成器 - 模块化版本 v0728

## 📋 项目简介

这是一个完全模块化重构的AI报告生成器，能够自动生成专业的产业研究报告。系统采用模块化设计，代码结构清晰，易于维护和扩展。

## 🏗️ 项目结构

```
ai_report_complete_0728/
├── __init__.py                 # 主包初始化
├── main.py                     # 程序入口
├── README.md                   # 项目文档
│
├── core/                       # 核心模块
│   ├── __init__.py
│   ├── config.py              # 配置管理
│   └── generator.py           # 核心生成器
│
├── api/                        # API管理模块
│   ├── __init__.py
│   └── gemini_manager.py      # Gemini API管理器
│
├── utils/                      # 工具模块
│   ├── __init__.py
│   ├── token_manager.py       # Token管理
│   ├── file_reader.py         # 文件读取器
│   └── checkpoint_manager.py  # Checkpoint管理
│
├── search/                     # 搜索模块
│   ├── __init__.py
│   ├── search_trigger.py      # 搜索触发器
│   ├── search_manager.py      # 搜索管理器
│   ├── content_validator.py   # 内容验证器
│   └── content_integrator.py  # 内容整合器
│
├── content/                    # 内容处理模块
│   ├── __init__.py
│   ├── content_processor.py   # 内容处理器
│   └── content_cleaner.py     # 内容清理器
│
└── image/                      # 图片处理模块
    ├── __init__.py
    └── image_processor.py     # 图片处理器
```

## 🚀 快速开始

### 1. 环境准备

确保您的Python环境已安装必要的依赖：

```bash
pip install google-generativeai
pip install python-docx
pip install requests
pip install aiohttp
pip install tqdm
```

### 2. API密钥配置

在 `core/config.py` 中配置您的Gemini API密钥：

```python
API_KEYS = [
    "您的Gemini API密钥1",
    "您的Gemini API密钥2",
    # ... 更多密钥
]
```

### 3. 运行程序

```bash
cd ai_report_complete_0728
python main.py
```

### 4. 按提示输入配置

程序会引导您输入以下配置：
- 报告主题
- 框架文件路径（可选）
- 数据源路径
- 目标字数
- 最大层级深度
- 运行模式（异步/同步）
- 是否启用搜索增强
- 是否启用图片嵌入

## 🔧 核心功能

### 1. 智能报告生成
- **统筹模型**：使用 gemini-2.5-pro 负责框架设计和内容审核
- **执行模型**：使用 gemini-2.5-flash 负责具体内容生成
- **分工明确**：确保报告结构合理，内容专业

### 2. 异步并行处理
- **并发API调用**：支持多个API密钥同时工作
- **智能负载均衡**：自动分配API请求
- **大幅提升速度**：相比同步模式提升5-10倍效率

### 3. 内容清理优化
- **废话过滤**：自动清理AI生成的思考过程
- **版本对比清理**：移除优化前后的对比内容
- **精炼输出**：确保最终报告简洁专业

### 4. 搜索增强
- **多源搜索**：支持Metaso、Google、Bing搜索API
- **内容缺口分析**：智能识别需要补充的信息
- **质量验证**：确保搜索结果的相关性和质量

### 5. 字数精确控制
- **智能压缩**：保持核心内容的同时控制字数
- **层级分配**：根据章节重要性分配字数
- **质量保证**：压缩过程中保持专业水准

## 📊 模块说明

### Core 模块
- **generator.py**：核心生成器，协调整个生成流程
- **config.py**：配置管理，包含API密钥、模型配置等

### API 模块
- **gemini_manager.py**：Gemini API管理，支持同步和异步调用

### Utils 模块
- **token_manager.py**：Token管理，处理大文档分批
- **file_reader.py**：文件读取器，支持多种格式
- **checkpoint_manager.py**：断点续传管理

### Search 模块
- **search_trigger.py**：分析内容缺口，触发搜索需求
- **search_manager.py**：管理多个搜索API
- **content_validator.py**：验证搜索结果质量
- **content_integrator.py**：整合搜索结果到报告

### Content 模块
- **content_processor.py**：内容处理和优化
- **content_cleaner.py**：清理AI生成的废话内容

### Image 模块
- **image_processor.py**：图片处理和嵌入

## ⚙️ 配置选项

### 基础配置
```python
config = ReportConfig(
    target_words=50000,      # 目标字数
    max_depth=6,             # 最大层级深度
    use_async=True,          # 是否使用异步模式
    enable_search=True,      # 是否启用搜索增强
    enable_images=True       # 是否启用图片嵌入
)
```

### 环境变量配置
```bash
export METASO_API_KEY="your_metaso_key"
export GOOGLE_SEARCH_API_KEY="your_google_key"
export GOOGLE_SEARCH_CX="your_google_cx"
export BING_SEARCH_API_KEY="your_bing_key"
```

## 🔍 使用示例

### 基础使用
```python
from core.generator import CompleteReportGenerator

# 创建生成器
generator = CompleteReportGenerator(use_async=True)

# 生成报告
output_path = generator.generate_report(
    topic="人工智能产业发展研究",
    data_sources=["data/"],
    framework_file_path="templates/framework.md"
)

print(f"报告已生成: {output_path}")
```

### 高级配置
```python
from core.generator import CompleteReportGenerator
from core.config import ReportConfig

# 自定义配置
config = ReportConfig(
    target_words=30000,
    max_depth=5,
    use_async=True,
    enable_search=True,
    enable_images=False
)

# 创建生成器
generator = CompleteReportGenerator(use_async=True)
generator.report_config = config.to_dict()

# 生成报告
output_path = generator.generate_report(
    topic="区块链技术应用研究",
    data_sources=["data/blockchain/", "data/reports/"]
)
```

## 🛠️ 扩展开发

### 添加新的搜索API
1. 在 `search/search_manager.py` 中添加新的搜索方法
2. 在 `search_apis` 配置中注册新API
3. 在 `multi_source_search` 中添加调用逻辑

### 添加新的文件格式支持
1. 在 `utils/file_reader.py` 中添加新的读取方法
2. 在支持的扩展名列表中添加新格式
3. 实现相应的解析逻辑

### 自定义内容清理规则
1. 在 `content/content_cleaner.py` 中修改清理模式
2. 添加新的废话识别规则
3. 调整清理强度和精度

## 📈 性能优化

### 异步模式优势
- **并发处理**：同时调用多个API
- **速度提升**：5-10倍性能提升
- **资源利用**：充分利用API配额

### 内存优化
- **分批处理**：大文档自动分批
- **流式处理**：减少内存占用
- **垃圾回收**：及时释放资源

### API优化
- **智能轮换**：自动切换API密钥
- **错误重试**：自动处理API错误
- **负载均衡**：平衡API使用

## 🐛 故障排除

### 常见问题
1. **API密钥错误**：检查 `core/config.py` 中的密钥配置
2. **依赖缺失**：运行 `pip install -r requirements.txt`
3. **文件权限**：确保有读写权限
4. **网络问题**：检查网络连接和防火墙设置

### 调试模式
在代码中添加调试信息：
```python
import logging
logging.basicConfig(level=logging.DEBUG)
```

## 📝 更新日志

### v0728 (2025-07-28)
- ✅ 完全模块化重构
- ✅ 修复字数控制问题
- ✅ 修复联网搜索功能
- ✅ 优化内容清理算法
- ✅ 改进代码结构和可维护性

## 📄 许可证

本项目采用 MIT 许可证。详见 LICENSE 文件。

## 🤝 贡献

欢迎提交 Issue 和 Pull Request 来改进这个项目！
