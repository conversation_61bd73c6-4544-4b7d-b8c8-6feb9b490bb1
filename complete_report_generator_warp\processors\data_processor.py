"""
数据处理模块 - 负责处理和解析不同类型的数据文件
遵循原始代码的实现和逻辑
"""

import os
import json
from pathlib import Path
from typing import Dict, List, Any
import PyPDF2
import pandas as pd
import docx


class DataProcessor:
    """数据处理器类，处理PDF、Excel、Word等格式文件"""
    
    def extract_text_from_pdf(self, pdf_path: str) -> str:
        """提取PDF文本内容 - 与原始代码相同"""
        try:
            text = ""
            with open(pdf_path, "rb") as file:
                reader = PyPDF2.PdfReader(file)
                for page in reader.pages:
                    text += page.extract_text()
            return text.strip()
        except Exception as e:
            print(f"❌ 提取PDF文本失败: {e}")
            return ""

    def read_excel_file(self, file_path: str) -> pd.DataFrame:
        """读取Excel文件 - 与原始代码相同"""
        try:
            return pd.read_excel(file_path)
        except Exception as e:
            print(f"❌ 读取Excel文件失败: {e}")
            return pd.DataFrame()

    def read_word_file(self, file_path: str) -> str:
        """读取Word文件 - 与原始代码相同"""
        try:
            doc = docx.Document(file_path)
            text = [p.text for p in doc.paragraphs]
            return '\n'.join(text).strip()
        except Exception as e:
            print(f"❌ 读取Word文件失败: {e}")
            return ""
    
    def read_text_file(self, file_path: str) -> str:
        """读取文本文件 - 与原始代码相同"""
        try:
            with open(file_path, 'r', encoding='utf-8') as file:
                return file.read().strip()
        except Exception as e:
            print(f"❌ 读取文本文件失败: {e}")
            return ""

    def read_csv_file(self, file_path: str) -> pd.DataFrame:
        """读取CSV文件 - 与原始代码相同"""
        try:
            return pd.read_csv(file_path)
        except Exception as e:
            print(f"❌ 读取CSV文件失败: {e}")
            return pd.DataFrame()
