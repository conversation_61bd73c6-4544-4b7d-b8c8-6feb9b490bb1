#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Checkpoint管理器模块
支持断点续传功能
"""

import os
import json
import time
from pathlib import Path
from typing import Dict, Any, List, Optional
from datetime import datetime


class CheckpointManager:
    """Checkpoint管理器"""
    
    def __init__(self, checkpoints_dir: str = "checkpoints"):
        self.checkpoints_dir = Path(checkpoints_dir)
        self.checkpoints_dir.mkdir(exist_ok=True)
    
    def save_checkpoint(self, 
                       checkpoint_id: str,
                       step: str,
                       data: Dict[str, Any],
                       metadata: Dict[str, Any] = None) -> str:
        """保存checkpoint"""
        checkpoint_data = {
            "checkpoint_id": checkpoint_id,
            "step": step,
            "timestamp": datetime.now().isoformat(),
            "data": data,
            "metadata": metadata or {}
        }
        
        checkpoint_file = self.checkpoints_dir / f"{checkpoint_id}.json"
        
        try:
            with open(checkpoint_file, 'w', encoding='utf-8') as f:
                json.dump(checkpoint_data, f, ensure_ascii=False, indent=2)
            
            print(f"✅ Checkpoint已保存: {checkpoint_id} (步骤: {step})")
            return str(checkpoint_file)
            
        except Exception as e:
            print(f"❌ Checkpoint保存失败: {str(e)}")
            return ""
    
    def load_checkpoint(self, checkpoint_id: str) -> Optional[Dict[str, Any]]:
        """加载checkpoint"""
        checkpoint_file = self.checkpoints_dir / f"{checkpoint_id}.json"
        
        if not checkpoint_file.exists():
            print(f"❌ Checkpoint不存在: {checkpoint_id}")
            return None
        
        try:
            with open(checkpoint_file, 'r', encoding='utf-8') as f:
                checkpoint_data = json.load(f)
            
            print(f"✅ Checkpoint已加载: {checkpoint_id}")
            return checkpoint_data
            
        except Exception as e:
            print(f"❌ Checkpoint加载失败: {str(e)}")
            return None
    
    def list_checkpoints(self) -> List[Dict[str, Any]]:
        """列出所有checkpoint"""
        checkpoints = []
        
        for checkpoint_file in self.checkpoints_dir.glob("*.json"):
            try:
                with open(checkpoint_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                checkpoints.append({
                    "checkpoint_id": data.get("checkpoint_id", ""),
                    "step": data.get("step", ""),
                    "timestamp": data.get("timestamp", ""),
                    "file_path": str(checkpoint_file)
                })
                
            except Exception as e:
                print(f"⚠️ 读取checkpoint失败 {checkpoint_file}: {str(e)}")
        
        # 按时间排序
        checkpoints.sort(key=lambda x: x["timestamp"], reverse=True)
        return checkpoints
    
    def delete_checkpoint(self, checkpoint_id: str) -> bool:
        """删除checkpoint"""
        checkpoint_file = self.checkpoints_dir / f"{checkpoint_id}.json"
        
        if not checkpoint_file.exists():
            print(f"❌ Checkpoint不存在: {checkpoint_id}")
            return False
        
        try:
            checkpoint_file.unlink()
            print(f"✅ Checkpoint已删除: {checkpoint_id}")
            return True
            
        except Exception as e:
            print(f"❌ Checkpoint删除失败: {str(e)}")
            return False
    
    def cleanup_old_checkpoints(self, keep_count: int = 10) -> int:
        """清理旧的checkpoint"""
        checkpoints = self.list_checkpoints()
        
        if len(checkpoints) <= keep_count:
            return 0
        
        deleted_count = 0
        checkpoints_to_delete = checkpoints[keep_count:]
        
        for checkpoint in checkpoints_to_delete:
            if self.delete_checkpoint(checkpoint["checkpoint_id"]):
                deleted_count += 1
        
        print(f"✅ 已清理 {deleted_count} 个旧checkpoint")
        return deleted_count
    
    def generate_checkpoint_id(self, prefix: str = "checkpoint") -> str:
        """生成checkpoint ID"""
        timestamp = int(time.time())
        return f"{prefix}_{timestamp}"
