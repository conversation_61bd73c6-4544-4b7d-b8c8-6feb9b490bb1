"""
LangGraph工作流编排模块
管理整个报告生成流程
"""
from typing import List
import yaml
import os

from .config import Config
from .logger import ProcessLogger, setup_logger, APICallLogger
from .state import ReportState
from .llm_client import LLMClient
from .context_manager import ContextManager
from .workflow_nodes import WorkflowNodes
from .word_generator import WordGenerator


class ReportGenerationWorkflow:
    """
    报告生成工作流类
    集成各个模块，执行完整的报告生成流程
    """

    def __init__(
        self, 
        config_path: str
    ):
        # 加载配置
        self.config = Config.from_yaml(config_path)
        
        # 设置日志
        self.logger = setup_logger(self.config.logging)
        self.process_logger = ProcessLogger(self.logger)
        self.api_logger = APICallLogger(self.logger, self.config.logging)
        
        # 初始化各模块
        self.llm_client = LLMClient(self.config, self.api_logger)
        self.context_manager = ContextManager(self.config, self.process_logger)
        self.workflow_nodes = WorkflowNodes(self.config, self.llm_client, self.context_manager, self.process_logger)
        self.word_generator = WordGenerator(self.config, self.process_logger)
    
    def execute(self, topic: str, framework_file: str, data_sources: List[str]) -> str:
        """
        执行完整的报告生成流程
        """
        # 创建初始报告状态
        state = ReportState(
            topic=topic,
            framework_file=framework_file,
            data_sources=data_sources
        )
        
        try:
            # 执行工作流节点
            state = self.workflow_nodes.setup_and_prepare_sources(state)
            state = self.workflow_nodes.design_framework(state)
            
            while state.should_continue_iteration():
                state = self.workflow_nodes.generate_content_top_down(state)
                state = self.workflow_nodes.review_and_refine_bottom_up(state)
            
            state = self.workflow_nodes.final_review_and_structured_output(state)
            
            # 生成Word文档
            output_file = self.word_generator.generate_word_document(
                state,
                output_path=os.path.join(self.config.output.output_dir, f"{topic}.docx")
            )
            
            self.logger.info(
                "报告生成成功",
                topic=topic,
                output_file=output_file
            )
            
            return output_file
            
        except Exception as e:
            self.process_logger.log_node_error(
                "report_generation_workflow",
                error=str(e)
            )
            raise

