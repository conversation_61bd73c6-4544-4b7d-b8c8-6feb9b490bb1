"""
图片缓存管理工具
专门管理AI报告生成器的图片内容缓存和索引
"""
import os
import sys
import json
import shutil
from pathlib import Path
from datetime import datetime
import argparse

class ImageCacheManager:
    """图片缓存管理器"""
    
    def __init__(self):
        self.image_caches = []
        self.total_images = 0
        self.total_cache_size = 0
    
    def scan_image_caches(self, root_dir="."):
        """扫描图片缓存"""
        root_path = Path(root_dir)
        self.image_caches = []
        self.total_images = 0
        self.total_cache_size = 0
        
        print(f"🔍 扫描图片缓存: {root_path.absolute()}")
        
        for image_index_dir in root_path.rglob("image_index"):
            if image_index_dir.is_dir() and image_index_dir.parent.name == "processed":
                cache_info = self._analyze_image_cache(image_index_dir)
                if cache_info:
                    self.image_caches.append(cache_info)
                    self.total_images += cache_info["image_count"]
                    self.total_cache_size += cache_info["cache_size"]
        
        print(f"✅ 发现 {len(self.image_caches)} 个图片缓存")
        print(f"🖼️ 总图片数量: {self.total_images}")
        print(f"📊 总缓存大小: {self._format_size(self.total_cache_size)}")
        
        return self.image_caches
    
    def _analyze_image_cache(self, image_index_dir):
        """分析图片缓存目录"""
        try:
            cache_info = {
                "path": image_index_dir,
                "data_source": image_index_dir.parent.parent,
                "cache_size": 0,
                "image_count": 0,
                "created_time": None,
                "images": {},
                "cache_files": []
            }
            
            # 计算缓存大小
            for file_path in image_index_dir.rglob("*"):
                if file_path.is_file():
                    cache_info["cache_size"] += file_path.stat().st_size
                    cache_info["cache_files"].append(file_path.name)
            
            # 读取图片索引
            index_file = image_index_dir / "image_index.json"
            if index_file.exists():
                with open(index_file, 'r', encoding='utf-8') as f:
                    index_data = json.load(f)
                
                cache_info["created_time"] = index_data.get("created_time")
                cache_info["image_count"] = len(index_data.get("images", {}))
                cache_info["images"] = index_data.get("images", {})
                cache_info["index_version"] = index_data.get("index_version", "1.0")
            
            return cache_info
            
        except Exception as e:
            print(f"⚠️ 分析图片缓存失败 {image_index_dir}: {str(e)}")
            return None
    
    def list_image_caches(self):
        """列出图片缓存"""
        if not self.image_caches:
            print("❌ 未发现任何图片缓存")
            return
        
        print(f"\n📋 图片缓存列表:")
        print("=" * 80)
        
        for i, cache_info in enumerate(self.image_caches, 1):
            print(f"\n{i}. {cache_info['data_source'].name}")
            print(f"   📁 路径: {cache_info['path']}")
            print(f"   🖼️ 图片: {cache_info['image_count']} 个")
            print(f"   📊 大小: {self._format_size(cache_info['cache_size'])}")
            
            if cache_info['created_time']:
                try:
                    created = datetime.fromisoformat(cache_info['created_time'])
                    print(f"   🕒 创建: {created.strftime('%Y-%m-%d %H:%M:%S')}")
                except:
                    print(f"   🕒 创建: {cache_info['created_time']}")
            
            # 显示图片信息摘要
            images = cache_info['images']
            if images:
                print(f"   📷 图片详情:")
                for img_path, img_data in list(images.items())[:3]:  # 只显示前3个
                    props = img_data.get("image_properties", {})
                    size_info = f"{props.get('width', 0)}x{props.get('height', 0)}" if props else "未知"
                    analysis = img_data.get("content_analysis", "")[:50]
                    print(f"      • {img_path} ({size_info}) - {analysis}...")
                
                if len(images) > 3:
                    print(f"      ... 还有 {len(images) - 3} 个图片")
    
    def show_image_details(self, cache_index):
        """显示图片缓存详情"""
        if not self.image_caches or cache_index < 1 or cache_index > len(self.image_caches):
            print(f"❌ 无效的缓存索引: {cache_index}")
            return
        
        cache_info = self.image_caches[cache_index - 1]
        
        print(f"\n📷 图片缓存详情: {cache_info['data_source'].name}")
        print("=" * 60)
        print(f"缓存路径: {cache_info['path']}")
        print(f"图片数量: {cache_info['image_count']}")
        print(f"缓存大小: {self._format_size(cache_info['cache_size'])}")
        print(f"索引版本: {cache_info.get('index_version', '1.0')}")
        
        if cache_info['created_time']:
            try:
                created = datetime.fromisoformat(cache_info['created_time'])
                print(f"创建时间: {created.strftime('%Y-%m-%d %H:%M:%S')}")
            except:
                print(f"创建时间: {cache_info['created_time']}")
        
        print(f"\n🖼️ 图片列表:")
        images = cache_info['images']
        
        for i, (img_path, img_data) in enumerate(images.items(), 1):
            print(f"\n{i}. {img_path}")
            
            # 图片属性
            props = img_data.get("image_properties", {})
            if props:
                print(f"   尺寸: {props.get('width')}x{props.get('height')}")
                print(f"   格式: {props.get('format', '未知')}")
                print(f"   模式: {props.get('mode', '未知')}")
                print(f"   宽高比: {props.get('aspect_ratio', 0)}")
            
            # 内容分析
            analysis = img_data.get("content_analysis", "")
            if analysis:
                print(f"   分析: {analysis}")
            
            # OCR文字
            ocr_text = img_data.get("ocr_text", "")
            if ocr_text:
                print(f"   文字: {ocr_text}")
            
            # 分析方法
            method = img_data.get("analysis_method", "未知")
            print(f"   方法: {method}")
            
            # 分析时间
            analyzed_time = img_data.get("analyzed_time", "")
            if analyzed_time:
                try:
                    analyzed = datetime.fromisoformat(analyzed_time)
                    print(f"   时间: {analyzed.strftime('%Y-%m-%d %H:%M:%S')}")
                except:
                    print(f"   时间: {analyzed_time}")
    
    def clean_image_cache(self, cache_index=None, confirm=True):
        """清理图片缓存"""
        if not self.image_caches:
            print("❌ 未发现任何图片缓存")
            return False
        
        if cache_index is not None:
            # 清理指定缓存
            if 1 <= cache_index <= len(self.image_caches):
                cache_info = self.image_caches[cache_index - 1]
                return self._clean_single_image_cache(cache_info, confirm)
            else:
                print(f"❌ 无效的缓存索引: {cache_index}")
                return False
        else:
            # 清理所有图片缓存
            return self._clean_all_image_caches(confirm)
    
    def _clean_single_image_cache(self, cache_info, confirm=True):
        """清理单个图片缓存"""
        cache_path = cache_info['path']
        cache_size = self._format_size(cache_info['cache_size'])
        
        print(f"\n🗑️ 准备清理图片缓存:")
        print(f"   📁 路径: {cache_path}")
        print(f"   🖼️ 图片: {cache_info['image_count']} 个")
        print(f"   📊 大小: {cache_size}")
        
        if confirm:
            response = input(f"\n❓ 确认清理此图片缓存? (y/N): ").strip().lower()
            if response != 'y':
                print("❌ 取消清理")
                return False
        
        try:
            shutil.rmtree(cache_path)
            print(f"✅ 图片缓存清理完成: {cache_path}")
            return True
        except Exception as e:
            print(f"❌ 清理失败: {str(e)}")
            return False
    
    def _clean_all_image_caches(self, confirm=True):
        """清理所有图片缓存"""
        total_size = self._format_size(self.total_cache_size)
        
        print(f"\n🗑️ 准备清理所有图片缓存:")
        print(f"   📁 缓存数: {len(self.image_caches)} 个")
        print(f"   🖼️ 总图片: {self.total_images} 个")
        print(f"   📊 总大小: {total_size}")
        
        if confirm:
            response = input(f"\n❓ 确认清理所有图片缓存? (y/N): ").strip().lower()
            if response != 'y':
                print("❌ 取消清理")
                return False
        
        success_count = 0
        for cache_info in self.image_caches:
            try:
                shutil.rmtree(cache_info['path'])
                success_count += 1
                print(f"✅ 已清理: {cache_info['path']}")
            except Exception as e:
                print(f"❌ 清理失败 {cache_info['path']}: {str(e)}")
        
        print(f"\n📊 清理结果: {success_count}/{len(self.image_caches)} 个图片缓存清理成功")
        return success_count == len(self.image_caches)
    
    def image_cache_stats(self):
        """显示图片缓存统计"""
        if not self.image_caches:
            print("❌ 未发现任何图片缓存")
            return
        
        print(f"\n📊 图片缓存统计:")
        print("=" * 50)
        print(f"图片缓存数量: {len(self.image_caches)}")
        print(f"总图片数量: {self.total_images}")
        print(f"总缓存大小: {self._format_size(self.total_cache_size)}")
        print(f"平均图片数量: {self.total_images / len(self.image_caches):.1f}")
        print(f"平均缓存大小: {self._format_size(self.total_cache_size / len(self.image_caches))}")
        
        # 分析图片类型
        format_stats = {}
        analysis_methods = {}
        
        for cache_info in self.image_caches:
            for img_path, img_data in cache_info['images'].items():
                # 统计格式
                props = img_data.get("image_properties", {})
                img_format = props.get("format", "未知")
                format_stats[img_format] = format_stats.get(img_format, 0) + 1
                
                # 统计分析方法
                method = img_data.get("analysis_method", "未知")
                analysis_methods[method] = analysis_methods.get(method, 0) + 1
        
        if format_stats:
            print(f"\n📷 图片格式分布:")
            for fmt, count in sorted(format_stats.items(), key=lambda x: x[1], reverse=True):
                print(f"   {fmt}: {count} 个")
        
        if analysis_methods:
            print(f"\n🔍 分析方法分布:")
            for method, count in sorted(analysis_methods.items(), key=lambda x: x[1], reverse=True):
                print(f"   {method}: {count} 个")
    
    def _format_size(self, size_bytes):
        """格式化文件大小"""
        if size_bytes < 1024:
            return f"{size_bytes} B"
        elif size_bytes < 1024 * 1024:
            return f"{size_bytes / 1024:.1f} KB"
        elif size_bytes < 1024 * 1024 * 1024:
            return f"{size_bytes / (1024 * 1024):.1f} MB"
        else:
            return f"{size_bytes / (1024 * 1024 * 1024):.1f} GB"

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="AI报告生成器图片缓存管理工具")
    parser.add_argument("command", choices=["scan", "list", "details", "clean", "stats"], 
                       help="操作命令")
    parser.add_argument("--dir", default=".", help="扫描目录 (默认: 当前目录)")
    parser.add_argument("--index", type=int, help="缓存索引")
    parser.add_argument("--force", action="store_true", help="强制执行，不询问确认")
    
    args = parser.parse_args()
    
    print("🖼️ AI报告生成器 - 图片缓存管理工具")
    print("=" * 60)
    
    manager = ImageCacheManager()
    
    if args.command == "scan":
        manager.scan_image_caches(args.dir)
    
    elif args.command == "list":
        manager.scan_image_caches(args.dir)
        manager.list_image_caches()
    
    elif args.command == "details":
        manager.scan_image_caches(args.dir)
        if args.index:
            manager.show_image_details(args.index)
        else:
            print("❌ 请指定缓存索引: --index <number>")
    
    elif args.command == "clean":
        manager.scan_image_caches(args.dir)
        if manager.image_caches:
            manager.clean_image_cache(args.index, not args.force)
        else:
            print("❌ 未发现任何图片缓存")
    
    elif args.command == "stats":
        manager.scan_image_caches(args.dir)
        manager.image_cache_stats()

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n❌ 用户中断操作")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 程序错误: {str(e)}")
        sys.exit(1)
