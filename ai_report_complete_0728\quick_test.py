#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
快速测试脚本
"""

import sys
from pathlib import Path

# 添加当前目录到Python路径
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

def main():
    print("🚀 开始快速测试...")
    
    try:
        # 测试核心模块导入
        print("1. 测试核心模块...")
        from core.config import ReportConfig, API_KEYS, MODEL_NAMES
        print(f"   ✅ 配置模块导入成功，API密钥数量: {len(API_KEYS)}")
        
        from core.generator import CompleteReportGenerator
        print("   ✅ 生成器模块导入成功")
        
        # 测试创建生成器实例
        print("2. 测试创建生成器实例...")
        generator = CompleteReportGenerator(use_async=False)
        print("   ✅ 同步生成器创建成功")
        
        # 测试配置
        print("3. 测试配置...")
        config = ReportConfig(target_words=30000, max_depth=5)
        print(f"   ✅ 配置创建成功: {config.target_words} 字")
        
        # 测试Token管理器
        print("4. 测试Token管理器...")
        from utils.token_manager import TokenManager
        token_manager = TokenManager()
        test_text = "这是一个测试文本。" * 10
        token_info = token_manager.get_token_info(test_text)
        print(f"   ✅ Token管理器测试成功: {token_info['estimated_tokens']} tokens")
        
        # 测试内容清理器
        print("5. 测试内容清理器...")
        from content.content_cleaner import ContentCleaner
        cleaner = ContentCleaner()
        dirty_text = "好的，我来生成内容。\n\n这是有用的内容。\n\n思考过程：我在思考..."
        clean_text = cleaner.clean_content_thoroughly(dirty_text)
        print(f"   ✅ 内容清理器测试成功，清理率: {(1-len(clean_text)/len(dirty_text))*100:.1f}%")
        
        print("\n🎉 所有测试通过！模块化系统工作正常！")
        return True
        
    except Exception as e:
        print(f"\n❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
