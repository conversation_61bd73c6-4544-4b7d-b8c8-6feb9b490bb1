#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
配置管理模块
"""

import os
from typing import Dict, List, Any
from pathlib import Path

# API密钥配置 - 从原代码复制的真实密钥
API_KEYS = [
    "AIzaSyAuvPJmOQzYGi-zfmxvMAEUIRTaWelwXwQ",
    "AIzaSyCz4ND6v_5_eGtlgok53Monj6gvTh-0XGE",
    "AIzaSyDDJ7DGAXY2RElU1QCXlOQpCWRk9mhgwY8",
    "AIzaSyBTec7MOadr0yOt-omEudzD0PxANwG67qc",
    "AIzaSyCe_XVHffYL1GpoHc7Z7cguoPpLKlHI6YY",
    "AIzaSyDJD1E55O771LcM7JA5_rla_2DcYz4fNIs",
    "AIzaSyDHNm93ybs8DRoO7XUMgqQt0ZqD8_BcTzY",
    "AIzaSyAAfACI-vjIZe78e7pfusUn56xJPY6kcKU",
    "AIzaSyDKW-0DSIGNnjacCfSGADC7OmoDvAaReac",
    "AIzaSyApBdUyH_XTZWffyZrreQq0DskEjdKTzpg"
]

# 模型配置
MODEL_NAMES = ["gemini-2.5-pro", "gemini-2.5-flash"]

# 全局配置常量
MAX_RETRIES = 3
REQUEST_TIMEOUT = 60
MAX_CONSECUTIVE_CLEANUP_COUNT = 100

class ReportConfig:
    """报告配置类"""
    
    def __init__(self, 
                 target_words: int = 50000,
                 max_depth: int = 6,
                 use_async: bool = True,
                 max_tokens: int = 250000,
                 enable_search: bool = True,
                 enable_images: bool = True,
                 output_formats: List[str] = None):
        """
        初始化报告配置
        
        Args:
            target_words: 目标字数
            max_depth: 最大层级深度
            use_async: 是否使用异步模式
            max_tokens: 最大token数
            enable_search: 是否启用搜索增强
            enable_images: 是否启用图片嵌入
            output_formats: 输出格式列表 ['docx', 'md']
        """
        self.target_words = target_words
        self.max_depth = max_depth
        self.use_async = use_async
        self.max_tokens = max_tokens
        self.enable_search = enable_search
        self.enable_images = enable_images
        self.output_formats = output_formats or ['docx', 'md']
        
        # 目录配置
        self.base_dir = Path.cwd()
        self.data_dir = self.base_dir / "data"
        self.templates_dir = self.base_dir / "templates"
        self.output_dir = self.base_dir / "output"
        self.logs_dir = self.base_dir / "logs"
        self.checkpoints_dir = self.base_dir / "checkpoints"
        
        # 创建必要目录
        self._create_directories()
    
    def _create_directories(self):
        """创建必要的目录"""
        for directory in [self.data_dir, self.templates_dir, self.output_dir, 
                         self.logs_dir, self.checkpoints_dir]:
            directory.mkdir(exist_ok=True)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "target_words": self.target_words,
            "max_depth": self.max_depth,
            "use_async": self.use_async,
            "max_tokens": self.max_tokens,
            "enable_search": self.enable_search,
            "enable_images": self.enable_images,
            "output_formats": self.output_formats
        }
    
    @classmethod
    def from_dict(cls, config_dict: Dict[str, Any]) -> 'ReportConfig':
        """从字典创建配置对象"""
        return cls(**config_dict)
    
    def get_word_count_by_level(self, level: int) -> str:
        """根据层级获取建议字数"""
        # 基础字数配置
        base_word_counts = {
            1: "8000-12000",
            2: "4000-6000", 
            3: "2000-3000",
            4: "1000-1500",
            5: "500-800",
            6: "300-500"
        }
        
        # 动态调整
        if level <= self.max_depth:
            return base_word_counts.get(level, "500-800")
        else:
            return "300-500"

def get_environment_config() -> Dict[str, str]:
    """获取环境变量配置"""
    return {
        "METASO_API_KEY": os.getenv('METASO_API_KEY', 'mk-988A8E4DC50C53312E3D1A8729687F4C'),
        "GOOGLE_SEARCH_API_KEY": os.getenv('GOOGLE_SEARCH_API_KEY', ''),
        "GOOGLE_SEARCH_CX": os.getenv('GOOGLE_SEARCH_CX', ''),
        "BING_SEARCH_API_KEY": os.getenv('BING_SEARCH_API_KEY', '')
    }
