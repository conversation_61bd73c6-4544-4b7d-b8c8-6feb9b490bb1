"""
用户界面模块 - 处理用户输入和报告生成的配置
严格按照原始代码的实现
"""

import os
from typing import Any, Dict


def get_user_inputs() -> Dict[str, Any]:
    """
    获取用户输入的报告生成配置 - 与原始代码相同
    
    Returns:
        配置字典
    """
    print("🔧 配置报告生成参数")
    print("=" * 50)
    
    # 1. 获取主题输入路径
    print("\n📝 1. 主题配置")
    topic_input_method = input("选择主题输入方式 (1: 直接输入, 2: 从文件读取) [默认: 1]: ").strip() or "1"
    
    if topic_input_method == "2":
        topic_file_path = input("请输入主题文件路径 [默认: inputs/topic.txt]: ").strip() or "inputs/topic.txt"
        try:
            with open(topic_file_path, 'r', encoding='utf-8') as f:
                topic = f.read().strip()
            print(f"✅ 从文件读取主题: {topic}")
        except FileNotFoundError:
            print(f"⚠️ 文件不存在: {topic_file_path}，使用默认主题")
            topic = "固态电池产业研究报告"
    else:
        topic = input("请输入报告主题 [默认: 固态电池产业研究报告]: ").strip() or "固态电池产业研究报告"
    
    # 2. 获取一级标题数量配置
    print("\n🔢 2. 标题层级配置")
    while True:
        try:
            primary_sections = input("请输入一级标题数量 [默认: 8]: ").strip()
            if not primary_sections:
                primary_sections = 8
            else:
                primary_sections = int(primary_sections)
            if primary_sections < 1 or primary_sections > 20:
                print("❌ 一级标题数量应在1-20之间")
                continue
            break
        except ValueError:
            print("❌ 请输入有效的数字")
    
    # 返回配置
    return {
        "topic": topic,
        "primary_sections": primary_sections
    }

