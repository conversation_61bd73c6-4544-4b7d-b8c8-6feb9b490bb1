from _typeshed import Incomplete, StrPath
from lib2to3 import fixer_base
from typing import ClassVar, Literal

from ..pytree import Node

class FixExitfunc(fixer_base.BaseFix):
    BM_compatible: ClassVar[Literal[True]]
    PATTERN: ClassVar[str]
    def __init__(self, *args) -> None: ...
    sys_import: Incomplete | None
    def start_tree(self, tree: Node, filename: StrPath) -> None: ...
    def transform(self, node, results) -> None: ...
