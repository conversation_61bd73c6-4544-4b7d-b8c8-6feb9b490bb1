"""
完整的多轮迭代报告生成器
集成所有组件实现3轮迭代优化流程
"""
import time
import json
from typing import Dict, Any, List, Optional
from datetime import datetime
from pathlib import Path

from .state import ReportState, IterationResult
from .config import Config
from .logger import ProcessLogger, setup_logger, APICallLogger
from .llm_client import LLMClient
from .context_manager import ContextManager
from .iteration_controller import IterationController, IterationStageCoordinator
from .hierarchical_generator import HierarchicalContentGenerator, ChapterDataSourceIntegrator
from .quality_checker import QualityChecker
from .quality_improvement import QualityImprovementEngine, QualityScoreCalculator
from .enhanced_models import CoordinatorModel, ExecutorModel, ModelCommunicationProtocol
from .word_generator import WordGenerator


class IterativeReportGenerator:
    """
    多轮迭代报告生成器
    基于现有AIReportGeneratorWithChapterSources创建多轮迭代版本
    """
    
    def __init__(self, config_path: str = "config.yaml"):
        """
        初始化多轮迭代报告生成器
        
        Args:
            config_path: 配置文件路径
        """
        # 加载配置
        self.config = Config.from_yaml(config_path)
        self.config.ensure_directories()
        
        # 初始化日志系统
        self.logger = setup_logger(self.config.logging.dict())
        self.process_logger = ProcessLogger(self.logger)
        self.api_logger = APICallLogger(self.logger, self.config.logging.dict())
        
        # 初始化核心组件
        self.llm_client = LLMClient(self.config, self.api_logger)
        self.context_manager = ContextManager(self.config, self.process_logger)
        
        # 初始化迭代控制组件
        self.iteration_controller = IterationController(self.config, self.process_logger)
        self.stage_coordinator = IterationStageCoordinator(self.config, self.process_logger)
        
        # 初始化内容生成组件
        self.hierarchical_generator = HierarchicalContentGenerator(
            self.config, self.llm_client, self.context_manager, self.process_logger
        )
        self.chapter_integrator = ChapterDataSourceIntegrator(self.config, self.process_logger)
        
        # 初始化质量控制组件
        self.quality_checker = QualityChecker(self.config, self.llm_client, self.process_logger)
        self.quality_improvement = QualityImprovementEngine(
            self.config, self.llm_client, self.process_logger
        )
        self.quality_calculator = QualityScoreCalculator(self.config, self.process_logger)
        
        # 初始化增强模型
        self.coordinator_model = CoordinatorModel(self.config, self.llm_client, self.process_logger)
        self.executor_model = ExecutorModel(self.config, self.llm_client, self.process_logger)
        self.communication_protocol = ModelCommunicationProtocol(self.config, self.process_logger)
        
        # 初始化文档生成器
        self.word_generator = WordGenerator(self.config, self.process_logger)
        
        # 集成章节数据源
        self.hierarchical_generator = self.chapter_integrator.integrate_chapter_data_sources(
            self.hierarchical_generator, ReportState()  # 临时状态用于集成
        )
    
    def generate_iterative_report(
        self,
        topic: str,
        data_sources: List[str],
        framework_file: Optional[str] = None,
        checkpoint_path: Optional[str] = None
    ) -> str:
        """
        生成多轮迭代优化的报告
        
        Args:
            topic: 报告主题
            data_sources: 8个数据源文件夹路径的列表
            framework_file: 参考框架文件路径（可选）
            checkpoint_path: 检查点文件路径，用于恢复之前的生成任务（可选）
            
        Returns:
            生成的Word文档路径
        """
        self.process_logger.logger.info("开始多轮迭代报告生成")
        start_time = time.time()
        
        try:
            # 验证输入参数
            self._validate_inputs(topic, data_sources)
            
            # 创建或恢复状态
            if checkpoint_path and Path(checkpoint_path).exists():
                state = self._restore_from_checkpoint(checkpoint_path)
                self.process_logger.logger.info(f"从检查点恢复: {checkpoint_path}")
            else:
                state = ReportState(
                    topic=topic,
                    framework_file=framework_file or "",
                    data_sources=data_sources,
                    max_iterations=3
                )
            
            # 执行完整的迭代流程
            state = self._execute_complete_iterative_workflow(state)
            
            # 生成最终文档
            output_path = self._generate_final_document(state)
            
            duration = time.time() - start_time
            self.process_logger.logger.info(
                f"多轮迭代报告生成完成，耗时: {duration:.2f}秒，"
                f"输出文件: {output_path}"
            )
            
            return output_path
            
        except Exception as e:
            self.process_logger.logger.error(f"多轮迭代报告生成失败: {str(e)}")
            raise
    
    def _execute_complete_iterative_workflow(self, state: ReportState) -> ReportState:
        """
        执行完整的迭代工作流
        
        Args:
            state: 报告状态
            
        Returns:
            更新后的报告状态
        """
        self.process_logger.logger.info("开始执行完整迭代工作流")
        
        try:
            # 阶段1：设置和准备数据源
            state = self._setup_and_prepare_sources(state)
            
            # 阶段2：设计报告框架
            state = self._design_framework(state)
            
            # 阶段3：执行多轮迭代优化
            state = self._execute_iterative_optimization(state)
            
            # 阶段4：最终审核和结构化输出
            state = self._final_review_and_output(state)
            
            self.process_logger.logger.info("完整迭代工作流执行完成")
            
        except Exception as e:
            self.process_logger.logger.error(f"迭代工作流执行失败: {str(e)}")
            # 保存错误检查点
            self._save_error_checkpoint(state, "workflow_execution")
            raise
        
        return state
    
    def _setup_and_prepare_sources(self, state: ReportState) -> ReportState:
        """设置和准备数据源"""
        self.process_logger.log_node_start("setup_and_prepare_sources")
        
        # 验证数据源数量
        if len(state.data_sources) != self.config.report.num_top_level_sections:
            error_msg = f"数据源数量不匹配：期望{self.config.report.num_top_level_sections}个，实际{len(state.data_sources)}个"
            state.add_error(error_msg, "setup_and_prepare_sources")
            raise ValueError(error_msg)
        
        # 验证每个路径是否存在
        for idx, source_path in enumerate(state.data_sources):
            if not Path(source_path).exists():
                state.add_warning(f"数据源路径不存在: {source_path}")
            elif not Path(source_path).is_dir():
                error_msg = f"路径不是目录: {source_path}"
                state.add_error(error_msg, "setup_and_prepare_sources")
                raise ValueError(error_msg)
        
        state.update_phase("sources_validated")
        self.process_logger.log_node_complete("setup_and_prepare_sources", 0)
        
        return state
    
    def _design_framework(self, state: ReportState) -> ReportState:
        """设计报告框架"""
        self.process_logger.log_node_start("design_framework")
        
        try:
            # 读取框架模板（如果提供）
            framework_content = ""
            if state.framework_file and Path(state.framework_file).exists():
                with open(state.framework_file, 'r', encoding='utf-8') as f:
                    framework_content = f.read()
            
            # 构建提示词
            prompt = f"""
你是一位专业的产业研究分析师。请为主题"{state.topic}"设计一份详细的研究报告框架。

要求：
1. 必须包含恰好8个一级标题
2. 每个一级标题下必须完整扩展到六级子标题（例如：1.1.1.1.1.1）
3. 标题层级必须连贯，不能跳级
4. 每个标题都应该有明确的title字段

{f"请参考以下现有框架的风格和结构：{framework_content}" if framework_content else ""}

请以JSON格式返回，结构如下：
{{
    "sections": [
        {{
            "title": "一级标题1",
            "level": 1,
            "children": [
                {{
                    "title": "二级标题1.1",
                    "level": 2,
                    "children": [...]
                }}
            ]
        }}
    ]
}}

确保返回的是有效的JSON格式。
"""
            
            # 调用LLM生成框架
            response = self.llm_client.call_llm(
                prompt=prompt,
                model_type="orchestrator",
                response_format="json"
            )
            
            # 验证响应结构
            if not self._validate_framework_structure(response):
                raise ValueError("生成的框架结构不符合要求")
            
            state.report_structure = response
            state.update_phase("framework_designed")
            
            self.process_logger.log_node_complete("design_framework", 0)
            
        except Exception as e:
            self.process_logger.log_node_error("design_framework", str(e))
            state.add_error(str(e), "design_framework")
            raise
        
        return state
    
    def _execute_iterative_optimization(self, state: ReportState) -> ReportState:
        """执行多轮迭代优化"""
        self.process_logger.logger.info("开始多轮迭代优化")
        
        # 初始化迭代结果
        state.iteration_result = IterationResult()
        state.iteration_result.start_time = datetime.now()
        
        try:
            # 执行迭代循环
            while state.should_continue_iteration():
                iteration_number = state.current_iteration + 1
                self.process_logger.logger.info(f"开始第 {iteration_number} 轮迭代")
                
                # 开始新的迭代步骤
                iteration_step = state.start_new_iteration_step()
                
                # 使用通信协议协调迭代周期
                cycle_result = self.communication_protocol.coordinate_iteration_cycle(
                    self.coordinator_model,
                    self.executor_model,
                    state,
                    iteration_number
                )
                
                # 执行分层内容生成
                state = self.hierarchical_generator.generate_hierarchical_content(state)
                
                # 执行质量检查
                state = self.quality_checker.check_report_quality(state)
                
                # 应用质量改进
                improvements_count = self.quality_improvement.apply_quality_improvements(state)
                iteration_step.improvements_applied = improvements_count
                
                # 完成迭代步骤
                state.complete_current_iteration_step()
                state.increment_iteration()
                
                # 检查收敛性
                if self.iteration_controller.calculate_convergence(state):
                    self.process_logger.logger.info(f"在第 {state.current_iteration} 轮迭代后达到收敛")
                    break
                
                # 保存迭代检查点
                self._save_iteration_checkpoint(state, iteration_number)
                
                self.process_logger.logger.info(f"第 {state.current_iteration} 轮迭代完成")
            
            # 标记迭代完成
            state.iteration_result.mark_completed()
            state.iteration_result.final_quality_score = state.get_current_quality_score()
            
            self.process_logger.logger.info(
                f"多轮迭代优化完成，总共 {state.current_iteration} 轮，"
                f"最终质量分数: {state.iteration_result.final_quality_score:.3f}"
            )
            
        except Exception as e:
            self.process_logger.logger.error(f"迭代优化过程中发生错误: {str(e)}")
            state.add_error(f"迭代优化错误: {str(e)}", "iterative_optimization")
            self._save_error_checkpoint(state, "iterative_optimization")
            raise
        
        return state
    
    def _final_review_and_output(self, state: ReportState) -> ReportState:
        """最终审核和结构化输出"""
        self.process_logger.log_node_start("final_review_and_output")
        
        try:
            # 构建完整的报告内容用于最终审核
            report_json = json.dumps(state.report_structure, ensure_ascii=False, indent=2)
            
            prompt = f"""
你是一位资深的报告编辑。请对以下报告进行最终审核和优化。

任务要求：
1. 确保内容的连贯性和逻辑性
2. 消除不同章节间的重复内容
3. 统一专业术语和表述风格
4. 保持所有引用标记[来源: xxx]不变
5. 必须保持与输入完全相同的JSON结构，只修改content字段的值

当前报告内容：
{report_json}

请返回优化后的完整JSON结构。
"""
            
            # 调用LLM进行最终审核
            response = self.llm_client.call_llm(
                prompt=prompt,
                model_type="orchestrator",
                response_format="json"
            )
            
            state.report_structure = response
            state.update_phase("final_review_completed")
            
            # 提取所有引用来源
            self._extract_all_citations(state)
            
            self.process_logger.log_node_complete("final_review_and_output", 0)
            
        except Exception as e:
            self.process_logger.log_node_error("final_review_and_output", str(e))
            state.add_error(str(e), "final_review_and_output")
            self._save_error_checkpoint(state, "final_review_and_output")
            raise
        
        return state
    
    def _generate_final_document(self, state: ReportState) -> str:
        """生成最终Word文档"""
        output_file = f"{state.topic}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.docx"
        output_path = Path(self.config.paths.output_dir) / output_file
        
        document_path = self.word_generator.generate_word_document(
            state, 
            str(output_path)
        )
        
        state.last_checkpoint = document_path
        state.update_phase("completed")
        
        return document_path
    
    def _validate_inputs(self, topic: str, data_sources: List[str]):
        """验证输入参数"""
        if not topic or not topic.strip():
            raise ValueError("报告主题不能为空")
        
        if len(data_sources) != self.config.report.num_top_level_sections:
            raise ValueError(
                f"数据源数量必须为 {self.config.report.num_top_level_sections} 个，"
                f"当前提供了 {len(data_sources)} 个"
            )
        
        # 验证路径格式
        for idx, source in enumerate(data_sources):
            if not source or not source.strip():
                raise ValueError(f"数据源路径 {idx + 1} 不能为空")
    
    def _validate_framework_structure(self, framework: Dict[str, Any]) -> bool:
        """验证框架结构是否符合要求"""
        if "sections" not in framework:
            return False
        
        sections = framework["sections"]
        if len(sections) != self.config.report.num_top_level_sections:
            return False
        
        # 简单验证：检查是否有标题和层级
        for section in sections:
            if "title" not in section or "level" not in section:
                return False
        
        return True
    
    def _extract_all_citations(self, state: ReportState):
        """提取所有引用来源"""
        import re
        citation_pattern = re.compile(self.config.report.citation_pattern)
        
        def extract_from_section(section: Dict[str, Any]):
            content = section.get("content", "")
            citations = citation_pattern.findall(content)
            
            section_key = f"level_{section.get('level', 0)}_{section.get('title', '')}"
            if citations:
                state.source_citations[section_key] = citations
            
            # 递归处理子节点
            if "children" in section:
                for child in section["children"]:
                    extract_from_section(child)
        
        sections = state.report_structure.get("sections", [])
        for section in sections:
            extract_from_section(section)
    
    def _save_iteration_checkpoint(self, state: ReportState, iteration_number: int):
        """保存迭代检查点"""
        if self.config.persistence.enabled:
            try:
                checkpoint_data = state.to_checkpoint_dict()
                checkpoint_file = Path(self.config.persistence.checkpoint_dir) / f"iteration_checkpoint_{iteration_number}.json"
                checkpoint_file.parent.mkdir(parents=True, exist_ok=True)
                
                with open(checkpoint_file, 'w', encoding='utf-8') as f:
                    json.dump(checkpoint_data, f, ensure_ascii=False, indent=2)
                
                state.last_checkpoint = str(checkpoint_file)
                self.process_logger.log_state_checkpoint(str(checkpoint_file))
                
            except Exception as e:
                self.process_logger.logger.error(f"保存迭代检查点失败: {str(e)}")
    
    def _save_error_checkpoint(self, state: ReportState, error_context: str):
        """保存错误检查点"""
        if self.config.persistence.enabled:
            try:
                checkpoint_data = state.to_checkpoint_dict()
                timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                checkpoint_file = Path(self.config.persistence.checkpoint_dir) / f"error_checkpoint_{timestamp}_{error_context}.json"
                checkpoint_file.parent.mkdir(parents=True, exist_ok=True)
                
                with open(checkpoint_file, 'w', encoding='utf-8') as f:
                    json.dump(checkpoint_data, f, ensure_ascii=False, indent=2)
                
                state.last_checkpoint = str(checkpoint_file)
                self.process_logger.log_state_checkpoint(f"Error checkpoint saved: {checkpoint_file}")
                
            except Exception as e:
                self.process_logger.logger.error(f"保存错误检查点失败: {str(e)}")
    
    def _restore_from_checkpoint(self, checkpoint_path: str) -> ReportState:
        """从检查点恢复状态"""
        with open(checkpoint_path, 'r', encoding='utf-8') as f:
            checkpoint_data = json.load(f)
        
        return ReportState.from_checkpoint_dict(checkpoint_data)
