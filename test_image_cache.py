"""
测试图片内容缓存机制
验证图片内容的智能索引和缓存功能
"""
import os
import sys
import time
import shutil
from pathlib import Path
from PIL import Image, ImageDraw, ImageFont

def create_test_images():
    """创建测试图片"""
    print("🖼️ 创建测试图片...")
    
    test_dir = Path("test_image_cache_data")
    if test_dir.exists():
        shutil.rmtree(test_dir)
    test_dir.mkdir()
    
    images_created = []
    
    # 1. 创建包含文字的图片
    try:
        # 创建一个简单的文字图片
        img = Image.new('RGB', (400, 200), color='white')
        draw = ImageDraw.Draw(img)
        
        # 尝试使用默认字体
        try:
            font = ImageFont.load_default()
        except:
            font = None
        
        text = "测试图片\nTest Image\n数据分析报告"
        draw.text((50, 50), text, fill='black', font=font)
        
        img_path = test_dir / "text_image.png"
        img.save(img_path)
        images_created.append("text_image.png")
        print(f"   ✅ 创建文字图片: {img_path}")
        
    except Exception as e:
        print(f"   ⚠️ 创建文字图片失败: {str(e)}")
    
    # 2. 创建图表样式的图片
    try:
        img = Image.new('RGB', (600, 400), color='white')
        draw = ImageDraw.Draw(img)
        
        # 绘制简单的柱状图
        draw.rectangle([100, 100, 150, 300], fill='blue')
        draw.rectangle([200, 150, 250, 300], fill='red')
        draw.rectangle([300, 80, 350, 300], fill='green')
        draw.rectangle([400, 120, 450, 300], fill='orange')
        
        # 添加标题
        try:
            font = ImageFont.load_default()
            draw.text((200, 50), "Sales Chart 2024", fill='black', font=font)
        except:
            pass
        
        img_path = test_dir / "chart_image.png"
        img.save(img_path)
        images_created.append("chart_image.png")
        print(f"   ✅ 创建图表图片: {img_path}")
        
    except Exception as e:
        print(f"   ⚠️ 创建图表图片失败: {str(e)}")
    
    # 3. 创建表格样式的图片
    try:
        img = Image.new('RGB', (500, 300), color='white')
        draw = ImageDraw.Draw(img)
        
        # 绘制表格线
        for i in range(6):
            y = 50 + i * 40
            draw.line([(50, y), (450, y)], fill='black', width=1)
        
        for i in range(5):
            x = 50 + i * 100
            draw.line([(x, 50), (x, 250)], fill='black', width=1)
        
        # 添加表格标题
        try:
            font = ImageFont.load_default()
            draw.text((200, 20), "Data Table", fill='black', font=font)
        except:
            pass
        
        img_path = test_dir / "table_image.png"
        img.save(img_path)
        images_created.append("table_image.png")
        print(f"   ✅ 创建表格图片: {img_path}")
        
    except Exception as e:
        print(f"   ⚠️ 创建表格图片失败: {str(e)}")
    
    # 4. 创建流程图样式的图片
    try:
        img = Image.new('RGB', (600, 300), color='white')
        draw = ImageDraw.Draw(img)
        
        # 绘制流程框
        boxes = [(50, 100, 150, 150), (250, 100, 350, 150), (450, 100, 550, 150)]
        for box in boxes:
            draw.rectangle(box, outline='black', width=2)
        
        # 绘制箭头
        draw.line([(150, 125), (250, 125)], fill='black', width=2)
        draw.line([(350, 125), (450, 125)], fill='black', width=2)
        
        # 添加箭头头部
        draw.polygon([(240, 120), (250, 125), (240, 130)], fill='black')
        draw.polygon([(440, 120), (450, 125), (440, 130)], fill='black')
        
        img_path = test_dir / "flow_diagram.png"
        img.save(img_path)
        images_created.append("flow_diagram.png")
        print(f"   ✅ 创建流程图片: {img_path}")
        
    except Exception as e:
        print(f"   ⚠️ 创建流程图片失败: {str(e)}")
    
    print(f"✅ 创建了 {len(images_created)} 个测试图片: {', '.join(images_created)}")
    return test_dir, images_created

def test_image_cache_functionality():
    """测试图片缓存功能"""
    print("🧪 测试图片内容缓存机制")
    print("=" * 60)
    
    try:
        from complete_report_generator import CompleteReportGenerator
        
        # 创建测试图片
        test_dir, images_created = create_test_images()
        
        # 初始化生成器
        generator = CompleteReportGenerator(use_async=False)
        
        print(f"\n🔄 第一次读取（应该分析图片并创建缓存）")
        start_time = time.time()
        
        content1 = generator.read_data_source(str(test_dir))
        
        first_read_time = time.time() - start_time
        print(f"⏱️ 第一次读取耗时: {first_read_time:.2f} 秒")
        
        # 检查是否创建了图片缓存
        processed_dir = test_dir / "processed"
        image_index_dir = processed_dir / "image_index"
        
        if image_index_dir.exists():
            print(f"✅ 图片索引目录已创建: {image_index_dir}")
            
            # 检查图片索引文件
            index_file = image_index_dir / "image_index.json"
            if index_file.exists():
                print(f"✅ 图片索引文件存在: {index_file.stat().st_size} 字节")
                
                # 读取索引内容
                import json
                with open(index_file, 'r', encoding='utf-8') as f:
                    index_data = json.load(f)
                
                images = index_data.get("images", {})
                print(f"📊 图片索引信息: {len(images)} 个图片")
                
                for img_path, img_data in images.items():
                    print(f"   📷 {img_path}:")
                    props = img_data.get("image_properties", {})
                    if props:
                        print(f"      尺寸: {props.get('width')}x{props.get('height')}")
                    
                    analysis = img_data.get("content_analysis", "")
                    if analysis:
                        print(f"      分析: {analysis}")
                    
                    ocr_text = img_data.get("ocr_text", "")
                    if ocr_text:
                        print(f"      文字: {ocr_text[:50]}...")
            else:
                print(f"❌ 图片索引文件不存在")
        else:
            print(f"❌ 图片索引目录未创建")
        
        print(f"\n🔄 第二次读取（应该使用图片缓存）")
        start_time = time.time()
        
        content2 = generator.read_data_source(str(test_dir))
        
        second_read_time = time.time() - start_time
        print(f"⏱️ 第二次读取耗时: {second_read_time:.2f} 秒")
        
        # 比较两次读取的结果
        if content1 == content2:
            print(f"✅ 两次读取内容一致")
        else:
            print(f"❌ 两次读取内容不一致")
            print(f"   第一次长度: {len(content1)}")
            print(f"   第二次长度: {len(content2)}")
        
        # 检查内容中是否包含图片信息
        if "图片内容索引" in content1:
            print(f"✅ 内容包含图片索引信息")
            
            # 统计图片相关信息
            image_count = content1.count("图片文件:")
            print(f"📊 检测到 {image_count} 个图片文件信息")
        else:
            print(f"⚠️ 内容未包含图片索引信息")
        
        # 性能比较
        if second_read_time < first_read_time:
            speedup = first_read_time / second_read_time
            print(f"🚀 图片缓存加速: {speedup:.1f}x 倍")
        else:
            print(f"⚠️ 图片缓存未能提升性能")
        
        # 显示内容摘要
        print(f"\n📝 内容摘要（包含图片信息）:")
        if content1:
            lines = content1.split('\n')
            for i, line in enumerate(lines[:20]):
                if line.strip():
                    print(f"   {line}")
            if len(lines) > 20:
                print(f"   ... (还有 {len(lines) - 20} 行)")
        
        # 清理测试数据
        print(f"\n🧹 清理测试数据...")
        if test_dir.exists():
            shutil.rmtree(test_dir)
            print(f"✅ 测试数据清理完成")
        
        # 总结
        print(f"\n📊 图片缓存测试总结:")
        print(f"   ✅ 图片索引创建: {'正常' if image_index_dir.exists() else '异常'}")
        print(f"   ✅ 内容一致性: {'正常' if content1 == content2 else '异常'}")
        print(f"   ✅ 性能提升: {'是' if second_read_time < first_read_time else '否'}")
        print(f"   ✅ 图片信息包含: {'是' if '图片内容索引' in content1 else '否'}")
        
        return True
        
    except ImportError as e:
        print(f"❌ 导入失败: {str(e)}")
        return False
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_image_analysis_methods():
    """测试图片分析方法"""
    print(f"\n🔍 测试图片分析方法")
    print("=" * 60)
    
    try:
        from complete_report_generator import CompleteReportGenerator
        
        # 创建测试图片
        test_dir, images_created = create_test_images()
        generator = CompleteReportGenerator(use_async=False)
        
        print(f"📷 测试各种图片分析方法:")
        
        for img_name in images_created:
            img_path = test_dir / img_name
            print(f"\n   测试图片: {img_name}")
            
            # 测试轻量级分析
            analysis = generator._analyze_image_content_lightweight(str(img_path))
            print(f"   轻量级分析: {analysis}")
            
            # 测试OCR提取
            ocr_text = generator._extract_image_text_lightweight(img_path)
            if ocr_text:
                print(f"   OCR文字: {ocr_text[:100]}...")
            else:
                print(f"   OCR文字: 无文字内容")
        
        # 清理
        if test_dir.exists():
            shutil.rmtree(test_dir)
        
        return True
        
    except Exception as e:
        print(f"❌ 图片分析测试失败: {str(e)}")
        return False

if __name__ == "__main__":
    print("🚀 AI报告生成器 - 图片内容缓存测试")
    print("=" * 60)
    
    # 测试图片缓存功能
    cache_test_passed = test_image_cache_functionality()
    
    # 测试图片分析方法
    analysis_test_passed = test_image_analysis_methods()
    
    if cache_test_passed and analysis_test_passed:
        print(f"\n🎉 图片内容缓存机制测试通过！")
        print(f"\n💡 图片缓存的优势:")
        print(f"   1. 避免重复分析相同图片")
        print(f"   2. 缓存OCR识别结果")
        print(f"   3. 智能图片内容索引")
        print(f"   4. 显著提升处理速度")
        print(f"   5. 支持多种图片格式")
    else:
        print(f"\n💡 如果测试失败，请检查:")
        print(f"   1. 确保Pillow库已安装: pip install Pillow")
        print(f"   2. 确保pytesseract库已安装: pip install pytesseract")
        print(f"   3. 检查Tesseract OCR引擎安装")
        print(f"   4. 确保有足够的磁盘空间")
    
    sys.exit(0 if (cache_test_passed and analysis_test_passed) else 1)
