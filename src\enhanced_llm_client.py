"""
增强的LLM客户端
严格按照统筹模型(gemini-2.5-pro)和执行模型(gemini-2.5-flash)分工
"""
import time
from typing import Dict, Any, List, Optional, Union
from datetime import datetime

from .api_rotation_manager import get_api_manager
from .config import Config
from .logger import APICallLogger


class EnhancedLLMClient:
    """
    增强的LLM客户端
    严格按照用户要求使用指定模型
    """
    
    def __init__(self, config: Config, api_logger: APICallLogger):
        self.config = config
        self.api_logger = api_logger
        self.api_manager = get_api_manager()
        
        # 模型分工定义
        self.ORCHESTRATOR_MODEL = "gemini-2.5-pro"    # 统筹模型
        self.EXECUTOR_MODEL = "gemini-2.5-flash"      # 执行模型
        
        self.api_logger.logger.info("增强LLM客户端初始化完成")
        self.api_logger.logger.info(f"统筹模型: {self.ORCHESTRATOR_MODEL}")
        self.api_logger.logger.info(f"执行模型: {self.EXECUTOR_MODEL}")
    
    def call_orchestrator_model(
        self, 
        prompt: str, 
        context: Optional[Dict[str, Any]] = None,
        response_format: str = "text"
    ) -> Union[str, Dict[str, Any]]:
        """
        调用统筹模型 (gemini-2.5-pro)
        用于：框架设计、任务分配、质量检查、整体协调
        
        Args:
            prompt: 提示词
            context: 上下文信息（可选）
            response_format: 响应格式 ("text" 或 "json")
            
        Returns:
            模型响应
        """
        self.api_logger.logger.info(f"🎯 调用统筹模型: {self.ORCHESTRATOR_MODEL}")
        
        # 构建完整提示词
        full_prompt = self._build_full_prompt(prompt, context, "orchestrator")
        
        try:
            # 强制使用统筹模型
            response, key_index = self.api_manager.generate_content_with_model(
                full_prompt, 
                self.ORCHESTRATOR_MODEL
            )
            
            # 处理响应
            content = self._extract_content(response)
            
            # 检查内容质量
            if self.api_manager.check_content_needs_cleanup(content):
                self.api_logger.logger.warning("统筹模型响应质量不佳，记录清理事件")
                self.api_manager.record_cleanup(key_index)
                # 重试一次
                response, key_index = self.api_manager.generate_content_with_model(
                    full_prompt, 
                    self.ORCHESTRATOR_MODEL
                )
                content = self._extract_content(response)
            else:
                self.api_manager.record_successful_processing(key_index)
            
            # 记录API调用
            self._log_api_call("orchestrator", self.ORCHESTRATOR_MODEL, full_prompt, content)
            
            # 格式化响应
            return self._format_response(content, response_format)
            
        except Exception as e:
            self.api_logger.logger.error(f"统筹模型调用失败: {str(e)}")
            raise
    
    def call_executor_model(
        self, 
        prompt: str, 
        context: Optional[Dict[str, Any]] = None,
        response_format: str = "text"
    ) -> Union[str, Dict[str, Any]]:
        """
        调用执行模型 (gemini-2.5-flash)
        用于：具体内容生成、节点任务执行
        
        Args:
            prompt: 提示词
            context: 上下文信息（可选）
            response_format: 响应格式 ("text" 或 "json")
            
        Returns:
            模型响应
        """
        self.api_logger.logger.info(f"⚡ 调用执行模型: {self.EXECUTOR_MODEL}")
        
        # 构建完整提示词
        full_prompt = self._build_full_prompt(prompt, context, "executor")
        
        try:
            # 强制使用执行模型
            response, key_index = self.api_manager.generate_content_with_model(
                full_prompt, 
                self.EXECUTOR_MODEL
            )
            
            # 处理响应
            content = self._extract_content(response)
            
            # 检查内容质量
            if self.api_manager.check_content_needs_cleanup(content):
                self.api_logger.logger.warning("执行模型响应质量不佳，记录清理事件")
                self.api_manager.record_cleanup(key_index)
                # 重试一次
                response, key_index = self.api_manager.generate_content_with_model(
                    full_prompt, 
                    self.EXECUTOR_MODEL
                )
                content = self._extract_content(response)
            else:
                self.api_manager.record_successful_processing(key_index)
            
            # 记录API调用
            self._log_api_call("executor", self.EXECUTOR_MODEL, full_prompt, content)
            
            # 格式化响应
            return self._format_response(content, response_format)
            
        except Exception as e:
            self.api_logger.logger.error(f"执行模型调用失败: {str(e)}")
            raise
    
    def _build_full_prompt(
        self, 
        prompt: str, 
        context: Optional[Dict[str, Any]], 
        model_type: str
    ) -> str:
        """构建完整提示词"""
        full_prompt_parts = []
        
        # 添加角色定义
        if model_type == "orchestrator":
            full_prompt_parts.append("""
你是一个专业的报告统筹模型，负责：
1. 设计报告框架结构
2. 分配和协调任务
3. 进行质量检查和评估
4. 提供整体指导和建议

请以专业、严谨的态度完成任务。
""")
        else:  # executor
            full_prompt_parts.append("""
你是一个专业的内容执行模型，负责：
1. 根据统筹模型的指导生成具体内容
2. 执行分配的节点任务
3. 确保内容质量和格式规范
4. 遵循既定的框架和要求

请严格按照指导要求生成高质量内容。
""")
        
        # 添加上下文信息
        if context and "parts" in context:
            full_prompt_parts.append("\n=== 上下文信息 ===")
            for part in context["parts"]:
                if "text" in part:
                    full_prompt_parts.append(part["text"])
        
        # 添加主要提示词
        full_prompt_parts.append(f"\n=== 任务要求 ===\n{prompt}")
        
        return "\n".join(full_prompt_parts)
    
    def _extract_content(self, response) -> str:
        """提取响应内容"""
        try:
            if hasattr(response, 'text'):
                return response.text
            elif hasattr(response, 'candidates') and response.candidates:
                candidate = response.candidates[0]
                if hasattr(candidate, 'content') and hasattr(candidate.content, 'parts'):
                    parts = candidate.content.parts
                    if parts and hasattr(parts[0], 'text'):
                        return parts[0].text
            
            # 如果以上都失败，尝试转换为字符串
            return str(response)
            
        except Exception as e:
            self.api_logger.logger.error(f"提取响应内容失败: {str(e)}")
            return ""
    
    def _format_response(self, content: str, response_format: str) -> Union[str, Dict[str, Any]]:
        """格式化响应"""
        if response_format == "json":
            try:
                import json
                # 尝试提取JSON部分
                content = content.strip()
                
                # 查找JSON代码块
                if "```json" in content:
                    start = content.find("```json") + 7
                    end = content.find("```", start)
                    if end != -1:
                        json_str = content[start:end].strip()
                        return json.loads(json_str)
                
                # 查找花括号包围的JSON
                if content.startswith("{") and content.endswith("}"):
                    return json.loads(content)
                
                # 尝试直接解析
                return json.loads(content)
                
            except json.JSONDecodeError as e:
                self.api_logger.logger.warning(f"JSON解析失败: {str(e)}")
                # 返回包装的文本响应
                return {"content": content, "format_error": str(e)}
        
        return content
    
    def _log_api_call(self, model_type: str, model_name: str, prompt: str, response: str):
        """记录API调用"""
        self.api_logger.log_api_call(
            model_name=model_name,
            prompt_tokens=len(prompt) // 4,  # 粗略估算
            completion_tokens=len(response) // 4,  # 粗略估算
            total_tokens=len(prompt + response) // 4,
            response_time=0.0,  # 实际响应时间由API管理器处理
            success=True,
            metadata={
                "model_type": model_type,
                "timestamp": datetime.now().isoformat()
            }
        )
    
    def call_llm(
        self, 
        prompt: str, 
        context: Optional[Dict[str, Any]] = None,
        model_type: str = "executor",
        response_format: str = "text"
    ) -> Union[str, Dict[str, Any]]:
        """
        通用LLM调用接口（为了兼容现有代码）
        
        Args:
            prompt: 提示词
            context: 上下文信息
            model_type: 模型类型 ("orchestrator" 或 "executor")
            response_format: 响应格式
            
        Returns:
            模型响应
        """
        if model_type == "orchestrator":
            return self.call_orchestrator_model(prompt, context, response_format)
        else:
            return self.call_executor_model(prompt, context, response_format)
    
    def get_api_status(self) -> Dict[str, Any]:
        """获取API状态"""
        return self.api_manager.get_status()


class FrameworkReader:
    """
    框架文件读取器
    读取指定路径的markdown框架文件
    """
    
    def __init__(self, logger):
        self.logger = logger
    
    def read_framework_file(self, framework_path: str) -> str:
        """
        读取框架文件
        
        Args:
            framework_path: 框架文件路径
            
        Returns:
            框架文件内容
        """
        try:
            from pathlib import Path
            
            framework_file = Path(framework_path)
            
            if not framework_file.exists():
                self.logger.warning(f"框架文件不存在: {framework_path}")
                return ""
            
            if not framework_file.suffix.lower() in ['.md', '.markdown']:
                self.logger.warning(f"框架文件不是markdown格式: {framework_path}")
            
            with open(framework_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            self.logger.info(f"成功读取框架文件: {framework_path}")
            self.logger.info(f"框架文件大小: {len(content)} 字符")
            
            return content
            
        except Exception as e:
            self.logger.error(f"读取框架文件失败: {str(e)}")
            return ""
    
    def validate_framework_content(self, content: str) -> bool:
        """
        验证框架内容是否有效
        
        Args:
            content: 框架内容
            
        Returns:
            是否有效
        """
        if not content or len(content.strip()) < 100:
            return False
        
        # 检查是否包含标题结构
        import re
        headers = re.findall(r'^#+\s+.+$', content, re.MULTILINE)
        
        if len(headers) < 3:  # 至少应该有3个标题
            return False
        
        return True
