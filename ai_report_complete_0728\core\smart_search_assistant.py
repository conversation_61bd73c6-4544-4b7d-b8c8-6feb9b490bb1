#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
智能搜索助手
基于联网搜索模块，提供智能化的搜索功能
包括搜索策略优化、结果分析、内容增强等
"""

import re
import time
from typing import List, Dict, Any, Optional
from .web_search_module import WebSearchModule


class SmartSearchAssistant:
    """
    智能搜索助手
    
    功能特点：
    1. 智能查询优化
    2. 搜索结果分析和过滤
    3. 内容缺口识别
    4. 搜索策略自动调整
    5. 结果质量评估
    """

    def __init__(self):
        """初始化智能搜索助手"""
        self.web_search = WebSearchModule()
        self.search_strategies = {
            'latest_data': self._search_latest_data,
            'market_analysis': self._search_market_analysis,
            'technology_trends': self._search_technology_trends,
            'policy_regulations': self._search_policy_regulations,
            'competitive_analysis': self._search_competitive_analysis
        }
        
        print("🤖 智能搜索助手已初始化")

    # ==================== 主要搜索接口 ====================

    def intelligent_search(self, content: str, search_type: str = "auto") -> Dict[str, Any]:
        """
        智能搜索（主要入口）
        
        Args:
            content: 搜索内容或主题
            search_type: 搜索类型 ("auto", "latest_data", "market_analysis", etc.)
            
        Returns:
            包含搜索结果和分析的字典
        """
        print(f"🤖 智能搜索: {content}")
        
        if not content.strip():
            return self._empty_result("搜索内容不能为空")

        start_time = time.time()
        
        # 分析搜索意图
        if search_type == "auto":
            search_type = self._analyze_search_intent(content)
        
        print(f"📊 识别搜索类型: {search_type}")
        
        # 优化搜索查询
        optimized_queries = self._optimize_search_queries(content, search_type)
        
        # 执行搜索策略
        if search_type in self.search_strategies:
            search_results = self.search_strategies[search_type](optimized_queries)
        else:
            search_results = self._default_search(optimized_queries)
        
        # 分析和过滤结果
        analyzed_results = self._analyze_search_results(search_results, content)
        
        # 生成搜索报告
        search_report = self._generate_search_report(
            content, search_type, analyzed_results, time.time() - start_time
        )
        
        return search_report

    def enhance_content_with_search(self, content: str, topic: str) -> Dict[str, Any]:
        """
        基于内容缺口进行搜索增强
        
        Args:
            content: 现有内容
            topic: 主题
            
        Returns:
            增强建议和搜索结果
        """
        print(f"🔍 内容增强搜索: {topic}")
        
        # 分析内容缺口
        content_gaps = self._analyze_content_gaps(content, topic)
        
        if not content_gaps:
            return {
                'status': 'no_gaps',
                'message': '内容已较为完整，无需搜索增强',
                'gaps': [],
                'search_results': []
            }
        
        # 针对每个缺口进行搜索
        enhancement_results = []
        
        for gap in content_gaps:
            print(f"   🔍 搜索缺口: {gap['type']}")
            
            search_result = self.intelligent_search(gap['query'], gap['type'])
            enhancement_results.append({
                'gap': gap,
                'search_result': search_result
            })
        
        return {
            'status': 'enhanced',
            'message': f'发现 {len(content_gaps)} 个内容缺口，已完成搜索增强',
            'gaps': content_gaps,
            'enhancement_results': enhancement_results
        }

    def batch_search(self, queries: List[str], search_type: str = "auto") -> List[Dict[str, Any]]:
        """
        批量搜索
        
        Args:
            queries: 搜索查询列表
            search_type: 搜索类型
            
        Returns:
            搜索结果列表
        """
        print(f"📦 批量搜索: {len(queries)} 个查询")
        
        results = []
        for i, query in enumerate(queries, 1):
            print(f"   🔍 搜索 {i}/{len(queries)}: {query}")
            result = self.intelligent_search(query, search_type)
            results.append(result)
            
            # 避免频率限制
            if i < len(queries):
                time.sleep(1)
        
        return results

    # ==================== 搜索策略 ====================

    def _search_latest_data(self, queries: List[str]) -> List[Dict[str, Any]]:
        """搜索最新数据"""
        all_results = []
        
        for query in queries:
            # 添加时间限定词
            time_query = f"{query} 2024 2025 最新"
            results = self.web_search.search(time_query, num_results=8)
            all_results.extend(results)
        
        return all_results

    def _search_market_analysis(self, queries: List[str]) -> List[Dict[str, Any]]:
        """搜索市场分析"""
        all_results = []
        
        for query in queries:
            # 市场相关关键词
            market_query = f"{query} 市场规模 行业分析 竞争格局"
            results = self.web_search.search(market_query, num_results=8)
            all_results.extend(results)
        
        return all_results

    def _search_technology_trends(self, queries: List[str]) -> List[Dict[str, Any]]:
        """搜索技术趋势"""
        all_results = []
        
        for query in queries:
            # 技术相关关键词
            tech_query = f"{query} 技术发展 创新 趋势 突破"
            results = self.web_search.search(tech_query, num_results=8)
            all_results.extend(results)
        
        return all_results

    def _search_policy_regulations(self, queries: List[str]) -> List[Dict[str, Any]]:
        """搜索政策法规"""
        all_results = []
        
        for query in queries:
            # 政策相关关键词
            policy_query = f"{query} 政策 法规 监管 标准"
            results = self.web_search.search(policy_query, num_results=8)
            all_results.extend(results)
        
        return all_results

    def _search_competitive_analysis(self, queries: List[str]) -> List[Dict[str, Any]]:
        """搜索竞争分析"""
        all_results = []
        
        for query in queries:
            # 竞争相关关键词
            comp_query = f"{query} 竞争对手 市场份额 对比分析"
            results = self.web_search.search(comp_query, num_results=8)
            all_results.extend(results)
        
        return all_results

    def _default_search(self, queries: List[str]) -> List[Dict[str, Any]]:
        """默认搜索策略"""
        all_results = []
        
        for query in queries:
            results = self.web_search.search(query, num_results=10)
            all_results.extend(results)
        
        return all_results

    # ==================== 分析方法 ====================

    def _analyze_search_intent(self, content: str) -> str:
        """分析搜索意图"""
        content_lower = content.lower()
        
        # 时效性关键词
        if any(word in content_lower for word in ['最新', '2024', '2025', '近期', '当前']):
            return 'latest_data'
        
        # 市场分析关键词
        if any(word in content_lower for word in ['市场', '规模', '份额', '行业', '竞争']):
            return 'market_analysis'
        
        # 技术趋势关键词
        if any(word in content_lower for word in ['技术', '创新', '发展', '趋势', '突破']):
            return 'technology_trends'
        
        # 政策法规关键词
        if any(word in content_lower for word in ['政策', '法规', '监管', '标准', '规范']):
            return 'policy_regulations'
        
        # 竞争分析关键词
        if any(word in content_lower for word in ['竞争', '对手', '对比', '比较']):
            return 'competitive_analysis'
        
        return 'latest_data'  # 默认搜索最新数据

    def _optimize_search_queries(self, content: str, search_type: str) -> List[str]:
        """优化搜索查询"""
        queries = []
        
        # 基础查询
        queries.append(content.strip())
        
        # 根据搜索类型添加优化查询
        if search_type == 'latest_data':
            queries.append(f"{content} 最新发展")
            queries.append(f"{content} 2024 趋势")
        elif search_type == 'market_analysis':
            queries.append(f"{content} 市场分析")
            queries.append(f"{content} 行业报告")
        elif search_type == 'technology_trends':
            queries.append(f"{content} 技术创新")
            queries.append(f"{content} 发展趋势")
        elif search_type == 'policy_regulations':
            queries.append(f"{content} 政策法规")
            queries.append(f"{content} 监管政策")
        elif search_type == 'competitive_analysis':
            queries.append(f"{content} 竞争格局")
            queries.append(f"{content} 市场竞争")
        
        return queries[:3]  # 限制查询数量

    def _analyze_content_gaps(self, content: str, topic: str) -> List[Dict[str, Any]]:
        """分析内容缺口"""
        gaps = []
        content_lower = content.lower()
        
        # 时效性检查
        if not any(word in content_lower for word in ['2024', '2025', '最新', '近期']):
            gaps.append({
                'type': 'latest_data',
                'query': f'{topic} 最新数据 2024 2025',
                'priority': 'high',
                'reason': '内容可能缺乏最新的数据和发展动态'
            })
        
        # 市场数据检查
        if not any(word in content_lower for word in ['市场', '规模', '份额', '竞争']):
            gaps.append({
                'type': 'market_analysis',
                'query': f'{topic} 市场规模 竞争格局 行业分析',
                'priority': 'medium',
                'reason': '缺少详细的市场分析和竞争格局信息'
            })
        
        # 技术发展检查
        if not any(word in content_lower for word in ['技术', '创新', '发展', '趋势']):
            gaps.append({
                'type': 'technology_trends',
                'query': f'{topic} 技术发展 创新 突破',
                'priority': 'medium',
                'reason': '需要补充最新的技术发展和创新信息'
            })
        
        # 政策法规检查
        if not any(word in content_lower for word in ['政策', '法规', '监管', '标准']):
            gaps.append({
                'type': 'policy_regulations',
                'query': f'{topic} 政策法规 监管 标准',
                'priority': 'low',
                'reason': '可以补充相关的政策法规信息'
            })
        
        return gaps

    def _analyze_search_results(self, results: List[Dict[str, Any]], query: str) -> Dict[str, Any]:
        """分析搜索结果"""
        if not results:
            return {
                'total_results': 0,
                'quality_score': 0,
                'relevance_score': 0,
                'freshness_score': 0,
                'filtered_results': [],
                'summary': '未找到相关结果'
            }
        
        # 计算质量分数
        quality_score = self._calculate_quality_score(results)
        
        # 计算相关性分数
        relevance_score = self._calculate_relevance_score(results, query)
        
        # 计算时效性分数
        freshness_score = self._calculate_freshness_score(results)
        
        # 过滤高质量结果
        filtered_results = self._filter_high_quality_results(results)
        
        # 生成摘要
        summary = self._generate_results_summary(filtered_results)
        
        return {
            'total_results': len(results),
            'quality_score': quality_score,
            'relevance_score': relevance_score,
            'freshness_score': freshness_score,
            'filtered_results': filtered_results,
            'summary': summary
        }

    def _calculate_quality_score(self, results: List[Dict[str, Any]]) -> float:
        """计算结果质量分数"""
        if not results:
            return 0.0
        
        quality_indicators = 0
        total_results = len(results)
        
        for result in results:
            # 标题完整性
            if result.get('title') and len(result['title']) > 10:
                quality_indicators += 1
            
            # 摘要完整性
            if result.get('snippet') and len(result['snippet']) > 50:
                quality_indicators += 1
            
            # URL有效性
            if result.get('url') and result['url'].startswith('http'):
                quality_indicators += 1
        
        return (quality_indicators / (total_results * 3)) * 100

    def _calculate_relevance_score(self, results: List[Dict[str, Any]], query: str) -> float:
        """计算相关性分数"""
        if not results or not query:
            return 0.0
        
        query_words = set(query.lower().split())
        relevance_scores = []
        
        for result in results:
            title = result.get('title', '').lower()
            snippet = result.get('snippet', '').lower()
            
            # 计算关键词匹配度
            title_words = set(title.split())
            snippet_words = set(snippet.split())
            
            title_match = len(query_words & title_words) / len(query_words) if query_words else 0
            snippet_match = len(query_words & snippet_words) / len(query_words) if query_words else 0
            
            relevance_scores.append((title_match * 0.7 + snippet_match * 0.3) * 100)
        
        return sum(relevance_scores) / len(relevance_scores) if relevance_scores else 0

    def _calculate_freshness_score(self, results: List[Dict[str, Any]]) -> float:
        """计算时效性分数"""
        # 简化实现：基于来源和内容判断
        fresh_indicators = 0
        total_results = len(results)
        
        for result in results:
            title = result.get('title', '').lower()
            snippet = result.get('snippet', '').lower()
            
            # 检查时间相关词汇
            if any(word in title + snippet for word in ['2024', '2025', '最新', '近期']):
                fresh_indicators += 1
        
        return (fresh_indicators / total_results * 100) if total_results > 0 else 0

    def _filter_high_quality_results(self, results: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """过滤高质量结果"""
        filtered = []
        
        for result in results:
            # 基本质量检查
            if (result.get('title') and len(result['title']) > 10 and
                result.get('snippet') and len(result['snippet']) > 30 and
                result.get('url') and result['url'].startswith('http')):
                filtered.append(result)
        
        # 按来源权重排序
        filtered.sort(key=lambda x: {
            'metaso': 3,
            'google': 2,
            'bing': 1
        }.get(x.get('source', ''), 0), reverse=True)
        
        return filtered[:10]  # 返回前10个高质量结果

    def _generate_results_summary(self, results: List[Dict[str, Any]]) -> str:
        """生成结果摘要"""
        if not results:
            return "未找到相关的高质量结果"
        
        sources = set(result.get('source', '') for result in results)
        
        summary = f"找到 {len(results)} 个高质量结果，"
        summary += f"来源包括: {', '.join(sources)}。"
        
        if len(results) >= 5:
            summary += "结果丰富，涵盖多个角度。"
        elif len(results) >= 3:
            summary += "结果适中，信息较为全面。"
        else:
            summary += "结果有限，建议调整搜索策略。"
        
        return summary

    def _generate_search_report(self, query: str, search_type: str, analysis: Dict[str, Any], duration: float) -> Dict[str, Any]:
        """生成搜索报告"""
        return {
            'query': query,
            'search_type': search_type,
            'duration': round(duration, 2),
            'analysis': analysis,
            'recommendations': self._generate_recommendations(analysis),
            'timestamp': time.time()
        }

    def _generate_recommendations(self, analysis: Dict[str, Any]) -> List[str]:
        """生成搜索建议"""
        recommendations = []
        
        if analysis['quality_score'] < 50:
            recommendations.append("建议调整搜索关键词以获得更高质量的结果")
        
        if analysis['relevance_score'] < 60:
            recommendations.append("建议使用更具体的搜索词汇提高相关性")
        
        if analysis['freshness_score'] < 30:
            recommendations.append("建议添加时间限定词获取更新的信息")
        
        if analysis['total_results'] < 5:
            recommendations.append("建议使用更广泛的搜索词汇获取更多结果")
        
        if not recommendations:
            recommendations.append("搜索结果质量良好，可以直接使用")
        
        return recommendations

    def _empty_result(self, message: str) -> Dict[str, Any]:
        """返回空结果"""
        return {
            'query': '',
            'search_type': 'none',
            'duration': 0,
            'analysis': {
                'total_results': 0,
                'quality_score': 0,
                'relevance_score': 0,
                'freshness_score': 0,
                'filtered_results': [],
                'summary': message
            },
            'recommendations': [message],
            'timestamp': time.time()
        }

    # ==================== 便捷方法 ====================

    def search_latest_info(self, topic: str) -> Dict[str, Any]:
        """搜索最新信息"""
        return self.intelligent_search(topic, "latest_data")

    def search_market_info(self, topic: str) -> Dict[str, Any]:
        """搜索市场信息"""
        return self.intelligent_search(topic, "market_analysis")

    def search_tech_trends(self, topic: str) -> Dict[str, Any]:
        """搜索技术趋势"""
        return self.intelligent_search(topic, "technology_trends")

    def get_search_statistics(self) -> Dict[str, Any]:
        """获取搜索统计"""
        history = self.web_search.get_search_history()
        
        return {
            'total_searches': len(history),
            'api_status': self.web_search.get_api_status(),
            'recent_searches': history[-10:] if history else []
        }
