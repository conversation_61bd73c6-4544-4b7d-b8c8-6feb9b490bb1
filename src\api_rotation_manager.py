#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
API轮换管理器
实现多API密钥和模型的智能轮换机制
"""

import threading
import time
import json
import os
import logging
from typing import List, Dict, Any, Optional, Tuple
from datetime import datetime
import google.generativeai as genai

logger = logging.getLogger(__name__)

# API密钥列表 - 用户提供的完整密钥列表
API_KEYS = [
    "AIzaSyAuvPJmOQzYGi-zfmxvMAEUIRTaWelwXwQ",
    "AIzaSyCz4ND6v_5_eGtlgok53Monj6gvTh-0XGE", 
    "AIzaSyDDJ7DGAXY2RElU1QCXlOQpCWRk9mhgwY8",
    "AIzaSyBTec7MOadr0yOt-omEudzD0PxANwG67qc",
    "AIzaSyCe_XVHffYL1GpoHc7Z7cguoPpLKlHI6YY",
    "AIzaSyDJD1E55O771LcM7JA5_rla_2DcYz4fNIs",
    "AIzaSyDHNm93ybs8DRoO7XUMgqQt0ZqD8_BcTzY",
    "AIzaSyAAfACI-vjIZe78e7pfusUn56xJPY6kcKU",
    "AIzaSyDKW-0DSIGNnjacCfSGADC7OmoDvAaReac",
    "AIzaSyApBdUyH_XTZWffyZrreQq0DskEjdKTzpg",
]

# 定义要轮换使用的模型名称 - 用户指定的模型组合
MODEL_NAMES = ['gemini-2.5-pro', 'gemini-2.5-flash']

# 连续多少次响应需要清理(格式不佳)或信息不足时，就切换到下一个模型/密钥
MAX_CONSECUTIVE_CLEANUP_COUNT = 10

# 新增：用于断点续传的临时文件名
CHECKPOINT_FILE = 'ai_report_v2/checkpoint_V2.json'


class GeminiAPIManager:
    """
    Manages Gemini API keys and models with a two-tiered rotation system,
    usage counts, and automatic rotation, strictly following the user's logic.
    Includes a feature to stop after a set number of full rotations.
    """
    
    def __init__(self, api_keys: List[str], model_names: List[str]):
        self.api_configs = []
        
        for i, key in enumerate(api_keys):
            # 确保密钥非空且不是占位符
            if key and "YOUR_GEMINI_API_KEY" not in key and len(key) > 10:
                self.api_configs.append({
                    "name": f"API Key {i+1}",
                    "key": key,
                    "models": model_names,
                    "current_model_index": 0
                })
        
        if not self.api_configs:
            raise ValueError("FATAL: No valid Google API keys are configured. Please replace placeholders in the API_KEYS list.")
        
        self.total_keys = len(self.api_configs)
        self.current_api_index = 0
        self.lock = threading.Lock()
        
        # 新增：轮换上限功能
        self.max_rotations = 100
        self.total_rotations_completed = 0
        self.usage_counts = {i: 0 for i in range(self.total_keys)}
        self.consecutive_cleanup_counts = {i: 0 for i in range(self.total_keys)}
        self.api_call_counts = {i: 0 for i in range(self.total_keys)}
        
        print(f"Gemini API Manager initialized with {self.total_keys} active keys.")
        print(f"Each key will rotate through {len(model_names)} models: {model_names}")
        print(f"连续清理/信息不足切换阈值: {MAX_CONSECUTIVE_CLEANUP_COUNT} 次")
        print(f"程序将在完成 {self.max_rotations} 轮完整的API密钥轮换后自动停止。")
        
        if self.total_keys > 0:
            self._log_current_key_status()
    
    def _log_current_key_status(self):
        """记录当前密钥状态"""
        print(f"\n--- 所有API密钥状态 (已完成轮换: {self.total_rotations_completed}/{self.max_rotations}) ---")
        for i in range(self.total_keys):
            config = self.api_configs[i]
            status = "🔴 当前" if i == self.current_api_index else "⚪️ 待用"
            current_model = config['models'][config['current_model_index']]
            print(f" {status} {config['name']}: 模型='{current_model}', "
                  f"成功处理={self.usage_counts[i]}, "
                  f"API总调用={self.api_call_counts[i]}, "
                  f"连续清理计数={self.consecutive_cleanup_counts[i]}/{MAX_CONSECUTIVE_CLEANUP_COUNT}")
        print("-----------------------------------------------------------\n")
    
    def _get_current_config(self) -> Dict[str, Any]:
        """获取当前配置"""
        with self.lock:
            api_config = self.api_configs[self.current_api_index]
            model_name = api_config["models"][api_config["current_model_index"]]
            return {
                "api_index": self.current_api_index,
                "api_name": api_config["name"],
                "api_key": api_config["key"],
                "model_name": model_name
            }
    
    def _switch_to_next(self, reason: str = "未知原因"):
        """切换到下一个API密钥或模型"""
        # This method must be called within a lock
        print(f"!!!!!! 切换事件触发 !!!!!!")
        print(f"         切换原因: {reason}")
        
        current_api_config = self.api_configs[self.current_api_index]
        
        # Tier 1: Try to switch to the next model within the current API key
        if current_api_config["current_model_index"] + 1 < len(current_api_config["models"]):
            current_api_config["current_model_index"] += 1
            print(f"         内部切换: {current_api_config['name']} -> 新模型: '{current_api_config['models'][current_api_config['current_model_index']]}'")
        else:
            # Tier 2: If no more models, switch to the next API key
            print(f"         {current_api_config['name']} 的所有模型已尝试，切换到下一个API Key...")
            self.consecutive_cleanup_counts[self.current_api_index] = 0  # Reset cleanup count for the outgoing key
            current_api_config["current_model_index"] = 0  # Reset model index for the outgoing key
            
            # 新增：检查是否完成了一整轮轮换
            if self.current_api_index == self.total_keys - 1:
                self.total_rotations_completed += 1
                print(f"$$$$$ 完成一轮完整的API密钥轮换。总完成轮数: {self.total_rotations_completed}/{self.max_rotations} $$$$$")
            
            self.current_api_index = (self.current_api_index + 1) % self.total_keys
            print(f"         已切换到新的API密钥: {self.api_configs[self.current_api_index]['name']}")
        
        self._log_current_key_status()
    
    def _check_and_switch_if_needed(self, key_index: int):
        """检查是否需要切换"""
        # This method must be called within a lock
        consecutive_cleanups = self.consecutive_cleanup_counts[key_index]
        if consecutive_cleanups >= MAX_CONSECUTIVE_CLEANUP_COUNT:
            self._switch_to_next(f"API Key {key_index + 1} 连续清理/信息不足达到阈值 ({consecutive_cleanups})")
    
    def record_successful_processing(self, key_index: int):
        """记录成功处理"""
        with self.lock:
            self.usage_counts[key_index] += 1
            self.consecutive_cleanup_counts[key_index] = 0
    
    def record_cleanup(self, key_index: int):
        """记录清理事件"""
        with self.lock:
            self.consecutive_cleanup_counts[key_index] += 1
            print(f"  INFO: {self.api_configs[key_index]['name']} 连续清理/信息不足+1 (当前: {self.consecutive_cleanup_counts[key_index]})")
            self._check_and_switch_if_needed(key_index)
    
    def check_content_needs_cleanup(self, content: str) -> bool:
        """检查内容是否需要清理"""
        if not content or not isinstance(content, str):
            return True
        
        # 检查内容是否包含错误信息
        error_indicators = [
            "内容生成失败",
            "任务执行失败", 
            "API调用失败",
            "无法生成内容",
            "I'm sorry",
            "I apologize",
            "As an AI",
            "As a language model"
        ]
        
        if any(indicator in content for indicator in error_indicators):
            return True
        
        # 检查内容是否过短
        if len(content.strip()) < 200:
            return True
        
        return False
    
    def generate_content(self, prompt: str) -> Tuple[Any, int]:
        """生成内容"""
        with self.lock:
            if self.total_rotations_completed >= self.max_rotations:
                raise Exception(f"已完成 {self.max_rotations} 轮完整的API密钥轮换，程序终止。")
            
            if self.total_keys == 0:
                raise Exception("No configured API keys to use for generation.")
        
        max_attempts = self.total_keys * len(self.api_configs[0]['models']) + 1
        
        for attempt in range(max_attempts):
            config = self._get_current_config()
            key_index = config['api_index']
            
            with self.lock:
                self.api_call_counts[key_index] += 1
            
            print(f"\n[API调用] 使用: {config['api_name']} | 模型: {config['model_name']} | 调用数: {self.api_call_counts[key_index]}")
            
            try:
                genai.configure(api_key=config["api_key"])
                
                # 优化: 明确设置一个慷慨的输出token上限，防止响应被截断
                generation_config = genai.types.GenerationConfig(
                    temperature=0.0,
                    max_output_tokens=1000000
                )
                
                model = genai.GenerativeModel(config["model_name"], generation_config=generation_config)
                response = model.generate_content([prompt])
                
                return response, key_index
                
            except Exception as e:
                error_msg = str(e).lower()
                print(f"!!!!!! ERROR with {config['api_name']} using model {config['model_name']}: {error_msg} !!!!!!")
                
                if "quota" in error_msg or "limit" in error_msg or "permission" in error_msg or "resource_exhausted" in error_msg:
                    print(f"  -> 检测到配额/权限问题，立即切换...")
                    with self.lock:
                        self._switch_to_next(f"配额/权限问题 on {config['model_name']}")
                    time.sleep(1)
                    continue
                else:
                    print(f"  -> 非配额问题，处理失败。")
                    with self.lock:
                        self._switch_to_next(f"未知错误 on {config['model_name']}: {e}")
                    time.sleep(1)
                    continue
        
        raise Exception("All API keys and models have failed. Cannot proceed.")
    
    def generate_content_with_model(self, prompt: str, model_name: str) -> Tuple[Any, int]:
        """使用指定模型生成内容"""
        with self.lock:
            if self.total_rotations_completed >= self.max_rotations:
                raise Exception(f"已完成 {self.max_rotations} 轮完整的API密钥轮换，程序终止。")
            
            if self.total_keys == 0:
                raise Exception("No configured API keys to use for generation.")
        
        # 尝试所有API密钥，但只使用指定的模型
        max_attempts = self.total_keys + 1
        
        for attempt in range(max_attempts):
            # 获取当前API密钥，但强制使用指定模型
            with self.lock:
                current_api_index = self.current_api_index
                api_config = self.api_configs[current_api_index]
                api_key = api_config["key"]
                api_name = api_config["name"]
                self.api_call_counts[current_api_index] += 1
            
            print(f"\n[API调用] 使用: {api_name} | 强制模型: {model_name} | 调用数: {self.api_call_counts[current_api_index]}")
            
            try:
                genai.configure(api_key=api_key)
                
                # 优化: 明确设置一个慷慨的输出token上限，防止响应被截断
                generation_config = genai.types.GenerationConfig(
                    temperature=0.0,
                    max_output_tokens=1000000
                )
                
                model = genai.GenerativeModel(model_name, generation_config=generation_config)
                response = model.generate_content([prompt])
                
                return response, current_api_index
                
            except Exception as e:
                error_msg = str(e).lower()
                print(f"!!!!!! ERROR with {api_name} using model {model_name}: {error_msg} !!!!!!")
                
                if "quota" in error_msg or "limit" in error_msg or "permission" in error_msg or "resource_exhausted" in error_msg:
                    print(f"  -> 检测到配额/权限问题，切换到下一个API密钥...")
                    with self.lock:
                        self.current_api_index = (self.current_api_index + 1) % self.total_keys
                        if self.current_api_index == 0:
                            self.total_rotations_completed += 1
                    time.sleep(1)
                    continue
                else:
                    print(f"  -> 非配额问题，切换到下一个API密钥。")
                    with self.lock:
                        self.current_api_index = (self.current_api_index + 1) % self.total_keys
                        if self.current_api_index == 0:
                            self.total_rotations_completed += 1
                    time.sleep(1)
                    continue
        
        raise Exception(f"All API keys have failed for model {model_name}. Cannot proceed.")


# 全局API管理器实例
_api_manager: Optional[GeminiAPIManager] = None


def get_api_manager() -> GeminiAPIManager:
    """获取API管理器实例"""
    global _api_manager
    if _api_manager is None:
        _api_manager = GeminiAPIManager(API_KEYS, MODEL_NAMES)
    return _api_manager


def initialize_api_manager_with_checkpoint() -> GeminiAPIManager:
    """初始化API管理器并加载检查点"""
    global _api_manager
    _api_manager = GeminiAPIManager(API_KEYS, MODEL_NAMES)
    return _api_manager
