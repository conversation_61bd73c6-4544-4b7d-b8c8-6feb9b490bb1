{"node": "setup_and_prepare_sources", "event": "\u8282\u70b9\u5f00\u59cb\u6267\u884c", "logger": "src.logger", "level": "info", "timestamp": "2025-07-18T07:28:31.779386Z"}
{"node": "setup_and_prepare_sources", "duration_seconds": 0.0005097389221191406, "event": "\u8282\u70b9\u6267\u884c\u5b8c\u6210", "logger": "src.logger", "level": "info", "timestamp": "2025-07-18T07:28:31.780839Z"}
{"node": "design_framework", "event": "\u8282\u70b9\u5f00\u59cb\u6267\u884c", "logger": "src.logger", "level": "info", "timestamp": "2025-07-18T07:28:31.785378Z"}
{"model": "gemini-2.5-pro", "operation": "orchestrator_json", "success": false, "duration_seconds": 1.55324387550354, "input_tokens": 0, "output_tokens": 0, "total_tokens": 0, "estimated_cost_usd": 0.0, "error": "400 API key not valid. Please pass a valid API key. [reason: \"API_KEY_INVALID\"\ndomain: \"googleapis.com\"\nmetadata {\n  key: \"service\"\n  value: \"generativelanguage.googleapis.com\"\n}\n, locale: \"en-US\"\nmessage: \"API key not valid. Please pass a valid API key.\"\n]", "event": "API\u8c03\u7528\u5931\u8d25", "logger": "src.logger", "level": "error", "timestamp": "2025-07-18T07:28:33.339720Z"}
{"model": "gemini-2.5-pro", "operation": "orchestrator_json", "success": false, "duration_seconds": 0.30278849601745605, "input_tokens": 0, "output_tokens": 0, "total_tokens": 0, "estimated_cost_usd": 0.0, "error": "400 API key not valid. Please pass a valid API key. [reason: \"API_KEY_INVALID\"\ndomain: \"googleapis.com\"\nmetadata {\n  key: \"service\"\n  value: \"generativelanguage.googleapis.com\"\n}\n, locale: \"en-US\"\nmessage: \"API key not valid. Please pass a valid API key.\"\n]", "event": "API\u8c03\u7528\u5931\u8d25", "logger": "src.logger", "level": "error", "timestamp": "2025-07-18T07:28:35.645020Z"}
{"model": "gemini-2.5-pro", "operation": "orchestrator_json", "success": false, "duration_seconds": 0.29941248893737793, "input_tokens": 0, "output_tokens": 0, "total_tokens": 0, "estimated_cost_usd": 0.0, "error": "400 API key not valid. Please pass a valid API key. [reason: \"API_KEY_INVALID\"\ndomain: \"googleapis.com\"\nmetadata {\n  key: \"service\"\n  value: \"generativelanguage.googleapis.com\"\n}\n, locale: \"en-US\"\nmessage: \"API key not valid. Please pass a valid API key.\"\n]", "event": "API\u8c03\u7528\u5931\u8d25", "logger": "src.logger", "level": "error", "timestamp": "2025-07-18T07:28:39.946155Z"}
{"node": "design_framework", "error": "RetryError[<Future at 0x271535ed1f0 state=finished raised InvalidArgument>]", "event": "\u8282\u70b9\u6267\u884c\u9519\u8bef", "logger": "src.logger", "level": "error", "timestamp": "2025-07-18T07:28:39.947155Z"}
