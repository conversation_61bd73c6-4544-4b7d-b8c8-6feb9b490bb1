from _typeshed import SupportsWrite
from codecs import StreamReaderWriter, StreamWriter
from collections.abc import Mapping
from io import RawIOBase, TextIOBase
from typing import Literal, NoReturn
from xml.sax import _Source, handler, xmlreader

def escape(data: str, entities: Mapping[str, str] = {}) -> str: ...
def unescape(data: str, entities: Mapping[str, str] = {}) -> str: ...
def quoteattr(data: str, entities: Mapping[str, str] = {}) -> str: ...

class XMLGenerator(handler.ContentHandler):
    def __init__(
        self,
        out: TextIOBase | RawIOBase | StreamWriter | StreamReaderWriter | SupportsWrite[bytes] | None = None,
        encoding: str = "iso-8859-1",
        short_empty_elements: bool = False,
    ) -> None: ...
    def _qname(self, name: tuple[str | None, str]) -> str: ...
    def startDocument(self) -> None: ...
    def endDocument(self) -> None: ...
    def startPrefixMapping(self, prefix: str | None, uri: str) -> None: ...
    def endPrefixMapping(self, prefix: str | None) -> None: ...
    def startElement(self, name: str, attrs: xmlreader.AttributesImpl) -> None: ...
    def endElement(self, name: str) -> None: ...
    def startElementNS(self, name: tuple[str | None, str], qname: str | None, attrs: xmlreader.AttributesNSImpl) -> None: ...
    def endElementNS(self, name: tuple[str | None, str], qname: str | None) -> None: ...
    def characters(self, content: str) -> None: ...
    def ignorableWhitespace(self, content: str) -> None: ...
    def processingInstruction(self, target: str, data: str) -> None: ...

class XMLFilterBase(xmlreader.XMLReader):
    def __init__(self, parent: xmlreader.XMLReader | None = None) -> None: ...
    # ErrorHandler methods
    def error(self, exception: BaseException) -> NoReturn: ...
    def fatalError(self, exception: BaseException) -> NoReturn: ...
    def warning(self, exception: BaseException) -> None: ...
    # ContentHandler methods
    def setDocumentLocator(self, locator: xmlreader.Locator) -> None: ...
    def startDocument(self) -> None: ...
    def endDocument(self) -> None: ...
    def startPrefixMapping(self, prefix: str | None, uri: str) -> None: ...
    def endPrefixMapping(self, prefix: str | None) -> None: ...
    def startElement(self, name: str, attrs: xmlreader.AttributesImpl) -> None: ...
    def endElement(self, name: str) -> None: ...
    def startElementNS(self, name: tuple[str | None, str], qname: str | None, attrs: xmlreader.AttributesNSImpl) -> None: ...
    def endElementNS(self, name: tuple[str | None, str], qname: str | None) -> None: ...
    def characters(self, content: str) -> None: ...
    def ignorableWhitespace(self, chars: str) -> None: ...
    def processingInstruction(self, target: str, data: str) -> None: ...
    def skippedEntity(self, name: str) -> None: ...
    # DTDHandler methods
    def notationDecl(self, name: str, publicId: str | None, systemId: str) -> None: ...
    def unparsedEntityDecl(self, name: str, publicId: str | None, systemId: str, ndata: str) -> None: ...
    # EntityResolver methods
    def resolveEntity(self, publicId: str | None, systemId: str) -> str: ...
    # XMLReader methods
    def parse(self, source: xmlreader.InputSource | _Source) -> None: ...
    def setLocale(self, locale: str) -> None: ...
    def getFeature(self, name: str) -> Literal[1, 0] | bool: ...
    def setFeature(self, name: str, state: Literal[1, 0] | bool) -> None: ...
    def getProperty(self, name: str) -> object: ...
    def setProperty(self, name: str, value: object) -> None: ...
    # XMLFilter methods
    def getParent(self) -> xmlreader.XMLReader | None: ...
    def setParent(self, parent: xmlreader.XMLReader) -> None: ...

def prepare_input_source(source: xmlreader.InputSource | _Source, base: str = "") -> xmlreader.InputSource: ...
