"""
模型调用处理器 - 严格按照原始代码的模型调用实现
处理协调器模型（Orchestrator）和执行器模型（Executor）的调用
"""

import os
import time
import json
import asyncio
import aiohttp
import requests
from typing import Dict, Any, List, Optional, Union
from ..config import (
    DEEPSEEK_API_KEY, GEMINI_API_KEY, 
    DEEPSEEK_BASE_URL, GEMINI_BASE_URL,
    ORCHESTRATOR_MODEL, EXECUTOR_MODEL,
    MAX_RETRIES, RETRY_DELAY
)


class ModelCaller:
    """模型调用器基类"""
    
    def __init__(self):
        """初始化模型调用器"""
        self.deepseek_api_key = DEEPSEEK_API_KEY
        self.gemini_api_key = GEMINI_API_KEY
        
    def call_orchestrator_model(self, prompt: str, **kwargs) -> str:
        """
        调用协调器模型（DeepSeek）- 与原始代码完全相同
        
        Args:
            prompt: 输入提示词
            **kwargs: 其他参数
            
        Returns:
            模型响应文本
        """
        if not self.deepseek_api_key:
            raise ValueError("DeepSeek API key not found")
        
        headers = {
            "Authorization": f"Bearer {self.deepseek_api_key}",
            "Content-Type": "application/json"
        }
        
        data = {
            "model": ORCHESTRATOR_MODEL,
            "messages": [
                {"role": "user", "content": prompt}
            ],
            "temperature": kwargs.get("temperature", 0.7),
            "max_tokens": kwargs.get("max_tokens", 8000),
            "stream": False
        }
        
        url = f"{DEEPSEEK_BASE_URL}/chat/completions"
        
        for attempt in range(MAX_RETRIES):
            try:
                response = requests.post(url, headers=headers, json=data, timeout=120)
                
                if response.status_code == 200:
                    result = response.json()
                    return result["choices"][0]["message"]["content"]
                else:
                    print(f"❌ DeepSeek API错误 (尝试 {attempt + 1}/{MAX_RETRIES}): {response.status_code}")
                    print(f"   响应: {response.text}")
                    
            except Exception as e:
                print(f"❌ 调用DeepSeek失败 (尝试 {attempt + 1}/{MAX_RETRIES}): {str(e)}")
            
            if attempt < MAX_RETRIES - 1:
                time.sleep(RETRY_DELAY * (attempt + 1))
        
        raise Exception("DeepSeek API调用失败")
    
    def call_executor_model(self, prompt: str, **kwargs) -> str:
        """
        调用执行器模型（Gemini）- 与原始代码完全相同
        
        Args:
            prompt: 输入提示词
            **kwargs: 其他参数
            
        Returns:
            模型响应文本
        """
        if not self.gemini_api_key:
            raise ValueError("Gemini API key not found")
        
        url = f"{GEMINI_BASE_URL}/{EXECUTOR_MODEL}:generateContent?key={self.gemini_api_key}"
        
        headers = {
            "Content-Type": "application/json"
        }
        
        data = {
            "contents": [{
                "parts": [{
                    "text": prompt
                }]
            }],
            "generationConfig": {
                "temperature": kwargs.get("temperature", 0.7),
                "maxOutputTokens": kwargs.get("max_tokens", 20000),
                "topP": kwargs.get("top_p", 0.95),
                "topK": kwargs.get("top_k", 40)
            }
        }
        
        for attempt in range(MAX_RETRIES):
            try:
                response = requests.post(url, headers=headers, json=data, timeout=120)
                
                if response.status_code == 200:
                    result = response.json()
                    if "candidates" in result and result["candidates"]:
                        content = result["candidates"][0]["content"]["parts"][0]["text"]
                        return content
                    else:
                        print(f"❌ Gemini响应格式错误: {result}")
                else:
                    print(f"❌ Gemini API错误 (尝试 {attempt + 1}/{MAX_RETRIES}): {response.status_code}")
                    print(f"   响应: {response.text}")
                    
            except Exception as e:
                print(f"❌ 调用Gemini失败 (尝试 {attempt + 1}/{MAX_RETRIES}): {str(e)}")
            
            if attempt < MAX_RETRIES - 1:
                time.sleep(RETRY_DELAY * (attempt + 1))
        
        raise Exception("Gemini API调用失败")
    
    async def call_orchestrator_model_async(self, prompt: str, **kwargs) -> str:
        """
        异步调用协调器模型（DeepSeek）- 与原始代码完全相同
        
        Args:
            prompt: 输入提示词
            **kwargs: 其他参数
            
        Returns:
            模型响应文本
        """
        if not self.deepseek_api_key:
            raise ValueError("DeepSeek API key not found")
        
        headers = {
            "Authorization": f"Bearer {self.deepseek_api_key}",
            "Content-Type": "application/json"
        }
        
        data = {
            "model": ORCHESTRATOR_MODEL,
            "messages": [
                {"role": "user", "content": prompt}
            ],
            "temperature": kwargs.get("temperature", 0.7),
            "max_tokens": kwargs.get("max_tokens", 8000),
            "stream": False
        }
        
        url = f"{DEEPSEEK_BASE_URL}/chat/completions"
        
        async with aiohttp.ClientSession() as session:
            for attempt in range(MAX_RETRIES):
                try:
                    timeout = aiohttp.ClientTimeout(total=120)
                    async with session.post(url, headers=headers, json=data, timeout=timeout) as response:
                        if response.status == 200:
                            result = await response.json()
                            return result["choices"][0]["message"]["content"]
                        else:
                            error_text = await response.text()
                            print(f"❌ DeepSeek API错误 (尝试 {attempt + 1}/{MAX_RETRIES}): {response.status}")
                            print(f"   响应: {error_text}")
                            
                except Exception as e:
                    print(f"❌ 异步调用DeepSeek失败 (尝试 {attempt + 1}/{MAX_RETRIES}): {str(e)}")
                
                if attempt < MAX_RETRIES - 1:
                    await asyncio.sleep(RETRY_DELAY * (attempt + 1))
        
        raise Exception("DeepSeek API异步调用失败")
    
    async def call_executor_model_async(self, prompt: str, **kwargs) -> str:
        """
        异步调用执行器模型（Gemini）- 与原始代码完全相同
        
        Args:
            prompt: 输入提示词
            **kwargs: 其他参数
            
        Returns:
            模型响应文本
        """
        if not self.gemini_api_key:
            raise ValueError("Gemini API key not found")
        
        url = f"{GEMINI_BASE_URL}/{EXECUTOR_MODEL}:generateContent?key={self.gemini_api_key}"
        
        headers = {
            "Content-Type": "application/json"
        }
        
        data = {
            "contents": [{
                "parts": [{
                    "text": prompt
                }]
            }],
            "generationConfig": {
                "temperature": kwargs.get("temperature", 0.7),
                "maxOutputTokens": kwargs.get("max_tokens", 20000),
                "topP": kwargs.get("top_p", 0.95),
                "topK": kwargs.get("top_k", 40)
            }
        }
        
        async with aiohttp.ClientSession() as session:
            for attempt in range(MAX_RETRIES):
                try:
                    async with session.post(url, headers=headers, json=data, timeout=120) as response:
                        if response.status == 200:
                            result = await response.json()
                            if "candidates" in result and result["candidates"]:
                                content = result["candidates"][0]["content"]["parts"][0]["text"]
                                return content
                            else:
                                print(f"❌ Gemini响应格式错误: {result}")
                        else:
                            error_text = await response.text()
                            print(f"❌ Gemini API错误 (尝试 {attempt + 1}/{MAX_RETRIES}): {response.status}")
                            print(f"   响应: {error_text}")
                            
                except Exception as e:
                    print(f"❌ 异步调用Gemini失败 (尝试 {attempt + 1}/{MAX_RETRIES}): {str(e)}")
                
                if attempt < MAX_RETRIES - 1:
                    await asyncio.sleep(RETRY_DELAY * (attempt + 1))
        
        raise Exception("Gemini API异步调用失败")
