"""
日志管理模块
提供结构化日志记录功能
"""
import structlog
import logging
from pathlib import Path
from datetime import datetime
from typing import Any, Dict


def setup_logger(config: Dict[str, Any]) -> structlog.BoundLogger:
    """
    设置结构化日志
    
    Args:
        config: 日志配置字典
        
    Returns:
        配置好的日志记录器
    """
    # 创建日志目录
    log_dir = Path("logs")
    log_dir.mkdir(exist_ok=True)
    
    # 设置日志级别
    log_level = getattr(logging, config.get("level", "INFO").upper())
    
    # 配置处理器
    processors = [
        structlog.stdlib.filter_by_level,
        structlog.stdlib.add_logger_name,
        structlog.stdlib.add_log_level,
        structlog.stdlib.PositionalArgumentsFormatter(),
        structlog.processors.TimeStamper(fmt="iso"),
        structlog.processors.StackInfoRenderer(),
        structlog.processors.format_exc_info,
        structlog.processors.UnicodeDecoder(),
    ]
    
    if config.get("format") == "structured":
        processors.append(structlog.processors.JSONRenderer())
    else:
        processors.append(structlog.dev.ConsoleRenderer())
    
    # 配置structlog
    structlog.configure(
        processors=processors,
        context_class=dict,
        logger_factory=structlog.stdlib.LoggerFactory(),
        cache_logger_on_first_use=True,
    )
    
    # 配置标准日志
    logging.basicConfig(
        format="%(message)s",
        stream=None,
        level=log_level,
    )
    
    # 添加文件处理器
    log_file = log_dir / f"report_agent_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"
    file_handler = logging.FileHandler(log_file, encoding="utf-8")
    file_handler.setLevel(log_level)
    logging.getLogger().addHandler(file_handler)
    
    return structlog.get_logger()


class APICallLogger:
    """API调用日志记录器"""
    
    def __init__(self, logger: structlog.BoundLogger, config: Dict[str, Any]):
        self.logger = logger
        self.log_costs = config.get("log_api_costs", True)
        self.log_tokens = config.get("log_token_usage", True)
        
    def log_api_call(self, 
                     model: str,
                     operation: str,
                     input_tokens: int = 0,
                     output_tokens: int = 0,
                     cost: float = 0.0,
                     duration: float = 0.0,
                     success: bool = True,
                     error: str = None):
        """记录API调用信息"""
        log_data = {
            "model": model,
            "operation": operation,
            "success": success,
            "duration_seconds": duration
        }
        
        if self.log_tokens:
            log_data.update({
                "input_tokens": input_tokens,
                "output_tokens": output_tokens,
                "total_tokens": input_tokens + output_tokens
            })
            
        if self.log_costs:
            log_data["estimated_cost_usd"] = cost
            
        if error:
            log_data["error"] = error
            
        if success:
            self.logger.info("API调用成功", **log_data)
        else:
            self.logger.error("API调用失败", **log_data)


class ProcessLogger:
    """流程日志记录器"""
    
    def __init__(self, logger: structlog.BoundLogger):
        self.logger = logger
        
    def log_node_start(self, node_name: str, **kwargs):
        """记录节点开始执行"""
        self.logger.info(
            "节点开始执行",
            node=node_name,
            **kwargs
        )
        
    def log_node_complete(self, node_name: str, duration: float, **kwargs):
        """记录节点执行完成"""
        self.logger.info(
            "节点执行完成",
            node=node_name,
            duration_seconds=duration,
            **kwargs
        )
        
    def log_node_error(self, node_name: str, error: str, **kwargs):
        """记录节点执行错误"""
        self.logger.error(
            "节点执行错误",
            node=node_name,
            error=error,
            **kwargs
        )
        
    def log_state_checkpoint(self, checkpoint_path: str):
        """记录状态检查点保存"""
        self.logger.info(
            "状态检查点已保存",
            checkpoint_path=checkpoint_path
        )
        
    def log_file_processing(self, file_path: str, file_type: str, success: bool, **kwargs):
        """记录文件处理"""
        self.logger.info(
            "文件处理",
            file_path=file_path,
            file_type=file_type,
            success=success,
            **kwargs
        )
