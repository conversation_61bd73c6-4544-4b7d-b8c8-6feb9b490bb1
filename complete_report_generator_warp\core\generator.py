"""
核心报告生成器 - 严格按照原始代码的CompleteReportGenerator实现
包含所有报告生成、优化和处理逻辑
"""

import os
import sys
import time
import json
import asyncio
import re
import hashlib
from pathlib import Path
from datetime import datetime
from typing import Dict, Any, List, Optional, Tuple, Union
from concurrent.futures import ThreadPoolExecutor
import logging
from tqdm import tqdm
from tqdm.asyncio import tqdm as async_tqdm

import pandas as pd
import docx
from docx.shared import Pt, RGBColor, Inches
from docx.enum.text import WD_ALIGN_PARAGRAPH
from docx.enum.style import WD_STYLE_TYPE
from docx.oxml.ns import qn
from docx.oxml import OxmlElement
import markdown
from bs4 import BeautifulSoup

# 导入内部模块
from ..models.token_manager import TokenManager
from ..models.api_rotator import APIRotator, AsyncAPIRotator
from ..models.model_caller import ModelCaller
from ..handlers.checkpoint_manager import CheckpointManager
from ..handlers.file_reader import FileReader
from ..handlers.search_handler import SearchHandler
from ..handlers.image_handler import ImageHandler
from ..processors.content_validator import ContentValidator
from ..config import *


class CompleteReportGenerator(ModelCaller):
    """
    完整的报告生成器 - 与原始代码完全相同的实现
    """
    
    def __init__(self, 
                 topic: str = None,
                 framework_path: str = None,
                 data_sources: List[str] = None,
                 output_dir: str = None,
                 max_tokens: int = None,
                 primary_sections: int = None,
                 max_depth: int = None,
                 target_words: int = None,
                 reference_report_path: str = None,
                 use_async: bool = True):
        """
        初始化报告生成器 - 与原始代码完全相同
        
        Args:
            topic: 报告主题
            framework_path: 报告框架文件路径
            data_sources: 数据源路径列表
            output_dir: 输出目录
            max_tokens: 最大token限制
            primary_sections: 一级标题数量
            max_depth: 最大层级深度
            target_words: 目标字数
            reference_report_path: 参考报告路径
            use_async: 是否使用异步模式
        """
        super().__init__()
        
        # 基础配置
        self.topic = topic or "产业研究报告"
        self.framework_path = framework_path
        self.data_sources = data_sources or []
        self.output_dir = Path(output_dir or OUTPUT_DIR)
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        # Token和内容配置
        self.max_tokens = max_tokens or DEFAULT_MAX_TOKENS
        self.token_manager = TokenManager()
        self.target_words = target_words or DEFAULT_TARGET_WORDS
        
        # 框架配置
        self.primary_sections = primary_sections or DEFAULT_PRIMARY_SECTIONS
        self.max_depth = max_depth or DEFAULT_MAX_DEPTH
        
        # 参考报告
        self.reference_report_path = reference_report_path
        
        # 异步配置
        self.use_async = use_async
        
        # 初始化组件
        self._init_components()
        
        # 报告数据
        self.framework = None
        self.content_cache = {}
        self.optimization_history = []
        self.audit_results = []
        
        # 性能统计
        self.api_call_count = 0
        self.start_time = None
        self.end_time = None
        
        print(f"\n🚀 报告生成器初始化完成")
        print(f"   主题: {self.topic}")
        print(f"   输出目录: {self.output_dir}")
        print(f"   异步模式: {'启用' if self.use_async else '禁用'}")
        print(f"   目标字数: {self.target_words:,} 字")
        print(f"   Token限制: {self.max_tokens:,}")
    
    def _init_components(self):
        """初始化组件 - 与原始代码完全相同"""
        # 生成唯一报告ID
        self.report_id = f"report_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{hashlib.md5(self.topic.encode()).hexdigest()[:8]}"
        
        # 初始化各组件
        self.checkpoint_manager = CheckpointManager(self.report_id)
        self.file_reader = FileReader()
        self.search_handler = SearchHandler()
        self.image_handler = ImageHandler()
        self.content_validator = ContentValidator()
        
        # API轮询器
        if DEEPSEEK_API_KEY:
            self.deepseek_rotator = APIRotator([DEEPSEEK_API_KEY])
        if GEMINI_API_KEY:
            self.gemini_rotator = APIRotator([GEMINI_API_KEY])
        
        # 设置日志
        self._setup_logging()
    
    def _setup_logging(self):
        """设置日志 - 与原始代码完全相同"""
        log_dir = Path("logs")
        log_dir.mkdir(exist_ok=True)
        
        log_file = log_dir / f"{self.report_id}.log"
        
        logging.basicConfig(
            level=logging.INFO,
            format=LOG_FORMAT,
            handlers=[
                logging.FileHandler(log_file, encoding='utf-8'),
                logging.StreamHandler()
            ]
        )
        
        self.logger = logging.getLogger(self.report_id)
    
    def generate_report(self) -> str:
        """
        生成完整报告 - 与原始代码完全相同
        
        Returns:
            报告文件路径
        """
        self.start_time = datetime.now()
        print(f"\n{'='*60}")
        print(f"🎯 开始生成报告: {self.topic}")
        print(f"{'='*60}\n")
        
        try:
            # 1. 读取数据源
            print("\n📚 步骤1: 读取数据源")
            all_data = self._read_data_sources()
            
            # 2. 生成或加载框架
            print("\n🏗️ 步骤2: 生成报告框架")
            self.framework = self._generate_or_load_framework(all_data)
            
            # 3. 生成内容
            print("\n✍️ 步骤3: 生成报告内容")
            if self.use_async:
                asyncio.run(self._generate_content_async(all_data))
            else:
                self._generate_content_sync(all_data)
            
            # 4. 迭代优化
            print("\n🔄 步骤4: 迭代优化")
            if self.use_async:
                asyncio.run(self._iterative_optimization_async())
            else:
                self._iterative_optimization_sync()
            
            # 5. 生成最终报告
            print("\n📄 步骤5: 生成最终报告")
            report_path = self._generate_final_report()
            
            # 6. 清理和统计
            self.end_time = datetime.now()
            self._print_statistics()
            
            print(f"\n✅ 报告生成完成: {report_path}")
            return report_path
            
        except Exception as e:
            self.logger.error(f"报告生成失败: {str(e)}", exc_info=True)
            print(f"\n❌ 报告生成失败: {str(e)}")
            raise
    
    def _read_data_sources(self) -> str:
        """读取所有数据源 - 与原始代码完全相同"""
        all_content = []
        
        # 添加主题信息
        all_content.append(f"报告主题：{self.topic}\n\n")
        
        # 读取数据源文件
        for i, source_path in enumerate(self.data_sources, 1):
            print(f"   📖 读取数据源 {i}/{len(self.data_sources)}: {source_path}")
            
            source_path = Path(source_path)
            if not source_path.exists():
                print(f"   ⚠️ 数据源不存在: {source_path}")
                continue
            
            # 处理目录
            if source_path.is_dir():
                files = list(source_path.rglob("*"))
                for file_path in files:
                    if file_path.is_file() and file_path.suffix in SUPPORTED_FILE_TYPES:
                        content = self.file_reader.read_file(str(file_path))
                        if content:
                            all_content.append(f"\n\n--- 文件: {file_path.name} ---\n")
                            all_content.append(content)
            # 处理文件
            else:
                content = self.file_reader.read_file(str(source_path))
                if content:
                    all_content.append(f"\n\n--- 文件: {source_path.name} ---\n")
                    all_content.append(content)
        
        # 合并所有内容
        combined_content = "\n".join(all_content)
        
        # 显示token信息
        token_info = self.token_manager.get_token_info(combined_content)
        print(f"\n   📊 数据统计:")
        print(f"      - 总字数: {token_info['text_length']:,}")
        print(f"      - 预估tokens: {token_info['estimated_tokens']:,}")
        print(f"      - 需要分批: {'是' if token_info['needs_splitting'] else '否'}")
        
        if token_info['needs_splitting']:
            print(f"      - 批次数量: {token_info['batches_needed']}")
        
        return combined_content
    
    def _generate_or_load_framework(self, data_content: str) -> Dict[str, Any]:
        """生成或加载报告框架 - 与原始代码完全相同"""
        # 检查是否有预定义框架
        if self.framework_path and Path(self.framework_path).exists():
            print(f"   📋 加载预定义框架: {self.framework_path}")
            return self._load_predefined_framework(self.framework_path)
        
        # 检查检查点
        checkpoint_data = self.checkpoint_manager.load_checkpoint()
        if checkpoint_data and "framework" in checkpoint_data:
            print("   💾 从检查点恢复框架")
            return checkpoint_data["framework"]
        
        # 生成新框架
        print("   🤖 使用AI生成报告框架...")
        framework = self._generate_framework_with_ai(data_content)
        
        # 保存检查点
        self.checkpoint_manager.save_framework_checkpoint(framework, "framework_generated")
        
        return framework
    
    def _load_predefined_framework(self, framework_path: str) -> Dict[str, Any]:
        """加载预定义框架 - 与原始代码完全相同"""
        try:
            file_ext = Path(framework_path).suffix.lower()
            
            if file_ext == '.json':
                with open(framework_path, 'r', encoding='utf-8') as f:
                    return json.load(f)
            elif file_ext in ['.txt', '.md']:
                with open(framework_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                # 解析文本格式的框架
                return self._parse_text_framework(content)
            else:
                print(f"   ⚠️ 不支持的框架文件格式: {file_ext}")
                return None
        except Exception as e:
            print(f"   ❌ 加载框架失败: {str(e)}")
            return None
    
    def _parse_text_framework(self, content: str) -> Dict[str, Any]:
        """解析文本格式的框架 - 与原始代码完全相同"""
        framework = {
            "title": self.topic,
            "sections": []
        }
        
        # 简单的Markdown解析
        lines = content.split('\n')
        current_section = None
        current_subsection = None
        
        for line in lines:
            line = line.strip()
            if not line:
                continue
            
            # 一级标题
            if line.startswith('## ') and not line.startswith('### '):
                if current_section:
                    framework["sections"].append(current_section)
                current_section = {
                    "title": line[3:].strip(),
                    "children": []
                }
                current_subsection = None
            # 二级标题
            elif line.startswith('### ') and current_section:
                if current_subsection:
                    current_section["children"].append(current_subsection)
                current_subsection = {
                    "title": line[4:].strip(),
                    "children": []
                }
            # 三级及更深标题
            elif line.startswith('####') and current_subsection:
                level = len(line.split()[0])  # 计算#的数量
                title = line[level:].strip()
                current_subsection["children"].append({
                    "title": title,
                    "level": level - 1
                })
        
        # 添加最后的部分
        if current_subsection and current_section:
            current_section["children"].append(current_subsection)
        if current_section:
            framework["sections"].append(current_section)
        
        return framework
