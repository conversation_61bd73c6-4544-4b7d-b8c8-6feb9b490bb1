"""
报告生成器主类
提供用户友好的接口来生成产业研究报告
"""
from typing import List, Optional
from pathlib import Path
import asyncio

from .config import Config
from .langgraph_workflow import ReportWorkflowBuilder
from .state import ReportState


class ReportGenerator:
    """
    AI产业研究报告生成器
    主要的用户接口类
    """
    
    def __init__(self, config_path: str = "config.yaml"):
        """
        初始化报告生成器
        
        Args:
            config_path: 配置文件路径
        """
        self.config = Config.from_yaml(config_path)
        self.config.ensure_directories()
        
        # 构建工作流
        self.workflow_builder = ReportWorkflowBuilder(self.config)
        self.workflow = self.workflow_builder.build_workflow()
    
    def generate_report(
        self,
        topic: str,
        data_sources: List[str],
        framework_file: Optional[str] = None,
        checkpoint_path: Optional[str] = None
    ) -> str:
        """
        生成产业研究报告
        
        Args:
            topic: 报告主题
            data_sources: 8个数据源文件夹路径的列表
            framework_file: 参考框架文件路径（可选）
            checkpoint_path: 检查点文件路径，用于恢复之前的生成任务（可选）
            
        Returns:
            生成的Word文档路径
            
        Raises:
            ValueError: 如果参数不符合要求
            Exception: 生成过程中的其他错误
        """
        # 验证参数
        self._validate_inputs(topic, data_sources)
        
        # 创建或恢复状态
        if checkpoint_path and Path(checkpoint_path).exists():
            initial_state = self.workflow_builder.restore_from_checkpoint(checkpoint_path)
            print(f"从检查点恢复: {checkpoint_path}")
        else:
            initial_state = ReportState(
                topic=topic,
                framework_file=framework_file or "",
                data_sources=data_sources
            )
        
        # 运行工作流
        print(f"开始生成报告: {topic}")
        print(f"数据源: {len(data_sources)} 个文件夹")
        
        try:
            # 执行工作流（需要提供配置）
            config = {"configurable": {"thread_id": "1"}}
            final_state = self.workflow.invoke(initial_state, config)
            
            if final_state.last_checkpoint and Path(final_state.last_checkpoint).exists():
                print(f"报告生成成功: {final_state.last_checkpoint}")
                return final_state.last_checkpoint
            else:
                raise Exception("报告生成失败：未找到输出文件")
                
        except Exception as e:
            print(f"报告生成过程中出现错误: {str(e)}")
            raise
    
    async def generate_report_async(
        self,
        topic: str,
        data_sources: List[str],
        framework_file: Optional[str] = None,
        checkpoint_path: Optional[str] = None
    ) -> str:
        """
        异步生成产业研究报告
        
        Args:
            topic: 报告主题
            data_sources: 8个数据源文件夹路径的列表
            framework_file: 参考框架文件路径（可选）
            checkpoint_path: 检查点文件路径（可选）
            
        Returns:
            生成的Word文档路径
        """
        # 在事件循环中运行同步方法
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(
            None,
            self.generate_report,
            topic,
            data_sources,
            framework_file,
            checkpoint_path
        )
    
    def _validate_inputs(self, topic: str, data_sources: List[str]):
        """
        验证输入参数
        
        Args:
            topic: 报告主题
            data_sources: 数据源列表
            
        Raises:
            ValueError: 如果输入不符合要求
        """
        if not topic or not topic.strip():
            raise ValueError("报告主题不能为空")
        
        if len(data_sources) != self.config.report.num_top_level_sections:
            raise ValueError(
                f"数据源数量必须为 {self.config.report.num_top_level_sections} 个，"
                f"当前提供了 {len(data_sources)} 个"
            )
        
        # 验证路径格式
        for idx, source in enumerate(data_sources):
            if not source or not source.strip():
                raise ValueError(f"数据源路径 {idx + 1} 不能为空")
    
    def get_config(self) -> Config:
        """
        获取当前配置
        
        Returns:
            配置对象
        """
        return self.config
    
    def list_checkpoints(self) -> List[str]:
        """
        列出所有可用的检查点
        
        Returns:
            检查点文件路径列表
        """
        checkpoint_dir = Path(self.config.persistence.checkpoint_dir)
        if not checkpoint_dir.exists():
            return []
        
        checkpoints = list(checkpoint_dir.glob("checkpoint_*.json"))
        return sorted([str(cp) for cp in checkpoints])
