"""
测试异步执行修复效果
验证卡死问题是否解决，tqdm进度条是否正常工作
"""
import asyncio
import time
import sys
from pathlib import Path

async def test_async_execution_fix():
    """测试异步执行修复"""
    print("🧪 测试异步执行修复")
    print("=" * 60)
    
    try:
        from complete_report_generator import CompleteReportGenerator
        
        # 创建生成器
        generator = CompleteReportGenerator(use_async=True, max_tokens=100000)
        
        print(f"✅ 异步生成器创建成功")
        
        # 测试简单的异步调用
        test_prompts = [
            "简要介绍人工智能",
            "解释机器学习概念", 
            "描述深度学习应用",
            "分析自然语言处理",
            "讨论计算机视觉技术"
        ]
        
        print(f"📋 创建 {len(test_prompts)} 个测试任务")
        
        # 创建异步任务
        async def generate_content(prompt):
            return await generator.api_manager.generate_content_with_model_async(
                prompt, "gemini-2.5-flash"
            )
        
        tasks = [generate_content(prompt) for prompt in test_prompts]
        
        print(f"🚀 开始异步执行测试...")
        start_time = time.time()
        
        # 使用修复后的批处理方法
        await generator._execute_tasks_in_batches(tasks, 3, "异步执行测试")
        
        end_time = time.time()
        duration = end_time - start_time
        
        print(f"✅ 异步执行测试完成")
        print(f"   总时间: {duration:.2f} 秒")
        print(f"   平均每任务: {duration/len(test_prompts):.2f} 秒")
        
        # 检查是否在合理时间内完成（不超过5分钟）
        if duration < 300:
            print(f"✅ 执行时间正常，未发生卡死")
            return True
        else:
            print(f"⚠️ 执行时间过长，可能仍有问题")
            return False
        
    except Exception as e:
        print(f"❌ 异步执行测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

async def test_timeout_handling():
    """测试超时处理"""
    print("\n🧪 测试超时处理")
    print("=" * 60)
    
    try:
        from complete_report_generator import CompleteReportGenerator
        
        generator = CompleteReportGenerator(use_async=True)
        
        # 创建一个可能超时的任务
        async def timeout_task():
            # 模拟长时间运行的任务
            await asyncio.sleep(10)
            return "完成"
        
        tasks = [timeout_task() for _ in range(3)]
        
        print(f"🚀 测试超时处理...")
        start_time = time.time()
        
        # 使用修复后的批处理方法（应该有超时保护）
        await generator._execute_tasks_in_batches(tasks, 2, "超时测试")
        
        end_time = time.time()
        duration = end_time - start_time
        
        print(f"✅ 超时处理测试完成")
        print(f"   总时间: {duration:.2f} 秒")
        
        # 检查是否正确处理了超时
        if duration < 320:  # 应该在5分钟+缓冲时间内完成
            print(f"✅ 超时处理正常")
            return True
        else:
            print(f"⚠️ 超时处理可能有问题")
            return False
        
    except Exception as e:
        print(f"❌ 超时处理测试失败: {str(e)}")
        return False

def test_tqdm_import():
    """测试tqdm导入"""
    print("\n🧪 测试tqdm导入")
    print("=" * 60)
    
    try:
        from tqdm.asyncio import tqdm
        print(f"✅ tqdm.asyncio导入成功")
        
        # 测试基本功能
        import asyncio
        
        async def test_progress():
            pbar = tqdm(total=5, desc="测试进度", unit="项")
            for i in range(5):
                await asyncio.sleep(0.1)
                pbar.update(1)
            pbar.close()
        
        asyncio.run(test_progress())
        print(f"✅ tqdm进度条测试成功")
        return True
        
    except ImportError as e:
        print(f"❌ tqdm导入失败: {str(e)}")
        print(f"💡 请安装tqdm: pip install tqdm")
        return False
    except Exception as e:
        print(f"❌ tqdm测试失败: {str(e)}")
        return False

async def test_api_manager_stability():
    """测试API管理器稳定性"""
    print("\n🧪 测试API管理器稳定性")
    print("=" * 60)
    
    try:
        from complete_report_generator import CompleteReportGenerator
        
        generator = CompleteReportGenerator(use_async=True)
        
        # 测试API配置获取
        api_config = generator.api_manager._get_available_api_config()
        if api_config:
            print(f"✅ API配置获取成功: {api_config['api_name']}")
        else:
            print(f"⚠️ 暂时无可用API配置")
        
        # 测试多次快速调用
        print(f"🚀 测试多次快速API调用...")
        
        quick_tasks = []
        for i in range(10):
            task = generator.api_manager.generate_content_with_model_async(
                f"测试调用 {i+1}", "gemini-2.5-flash"
            )
            quick_tasks.append(task)
        
        start_time = time.time()
        
        # 使用gather而不是批处理，测试并发稳定性
        try:
            results = await asyncio.wait_for(
                asyncio.gather(*quick_tasks, return_exceptions=True),
                timeout=180  # 3分钟超时
            )
            
            success_count = sum(1 for r in results if not isinstance(r, Exception))
            print(f"✅ 快速调用完成: {success_count}/{len(quick_tasks)} 成功")
            
            end_time = time.time()
            duration = end_time - start_time
            print(f"   总时间: {duration:.2f} 秒")
            
            return success_count >= len(quick_tasks) * 0.8  # 80%成功率
            
        except asyncio.TimeoutError:
            print(f"⏰ 快速调用测试超时")
            return False
        
    except Exception as e:
        print(f"❌ API管理器稳定性测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_configuration():
    """测试配置"""
    print("\n🧪 测试配置")
    print("=" * 60)
    
    try:
        from complete_report_generator import AsyncConfig, TokenManager
        
        # 测试AsyncConfig
        api_count = AsyncConfig.get_available_api_count()
        max_concurrent = AsyncConfig.get_max_concurrent_requests()
        batch_size = AsyncConfig.calculate_optimal_batch_size(20)
        
        print(f"📊 配置信息:")
        print(f"   可用API数量: {api_count}")
        print(f"   最大并发数: {max_concurrent}")
        print(f"   批次大小(20任务): {batch_size}")
        
        # 测试TokenManager
        token_manager = TokenManager(250000)
        test_text = "这是一个测试文本" * 1000
        token_info = token_manager.get_token_info(test_text)
        
        print(f"📊 Token信息:")
        print(f"   文本长度: {len(test_text)} 字符")
        print(f"   估算tokens: {token_info['estimated_tokens']:,}")
        print(f"   需要分批: {token_info['needs_splitting']}")
        
        print(f"✅ 配置测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 配置测试失败: {str(e)}")
        return False

async def main():
    """主测试函数"""
    print("🚀 异步执行修复测试")
    print("=" * 60)
    
    tests = [
        ("tqdm导入", test_tqdm_import, False),
        ("配置", test_configuration, False),
        ("异步执行修复", test_async_execution_fix, True),
        ("超时处理", test_timeout_handling, True),
        ("API管理器稳定性", test_api_manager_stability, True)
    ]
    
    results = []
    
    for test_name, test_func, is_async in tests:
        try:
            print(f"\n{'='*60}")
            
            if is_async:
                result = await test_func()
            else:
                result = test_func()
            
            results.append((test_name, result))
            
            if result:
                print(f"✅ {test_name}: 测试通过")
            else:
                print(f"❌ {test_name}: 测试失败")
                
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {str(e)}")
            results.append((test_name, False))
    
    # 总结
    print(f"\n📊 异步执行修复测试总结:")
    print("=" * 60)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {test_name}: {status}")
    
    print(f"\n🎯 总体结果: {passed}/{total} 个测试通过")
    
    if passed >= total * 0.8:  # 80%通过率
        print(f"\n🎉 异步执行修复测试基本通过！")
        print(f"\n💡 修复效果:")
        print(f"   1. ✅ 添加了超时保护，避免无限等待")
        print(f"   2. ✅ 简化了API配置获取逻辑")
        print(f"   3. ✅ 添加了tqdm进度条显示")
        print(f"   4. ✅ 优化了错误处理机制")
        print(f"   5. ✅ 减少了重试次数，提高稳定性")
    else:
        print(f"\n💡 如果测试失败，请检查:")
        print(f"   1. 是否安装了tqdm: pip install tqdm")
        print(f"   2. API密钥配置是否正确")
        print(f"   3. 网络连接是否稳定")
        print(f"   4. 是否有API配额限制")
    
    return passed >= total * 0.8

if __name__ == "__main__":
    try:
        success = asyncio.run(main())
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\n❌ 用户中断测试")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 测试程序错误: {str(e)}")
        sys.exit(1)
