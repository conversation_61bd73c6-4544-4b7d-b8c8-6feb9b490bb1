PIL/AvifImagePlugin.py,sha256=-fkL5LJRs9nAmi3gZQOOMjSlIw29aEXBFgvYKNXXKjs,9285
PIL/BdfFontFile.py,sha256=bApbMl2vYRceQKIkSDFa002Ywr9laVrp2NGK9xzlS_8,3407
PIL/BlpImagePlugin.py,sha256=GT1ujm8Xwg2OSTfzTdFrhH6mTYG0u6mHjsQN9FIwD6E,17030
PIL/BmpImagePlugin.py,sha256=qjhwXlKomN-TExyDoI_ud564b1WSwiYslQeyaqBEFyQ,20370
PIL/BufrStubImagePlugin.py,sha256=5dcwg2o6eh4HZdPihFXqRBaS-H514M-oQrs6iZBDGbY,1805
PIL/ContainerIO.py,sha256=I6yO_YFEEqMKA1ckgEEzF2r_Ik5p_GjM-RrWOJYjSlY,4777
PIL/CurImagePlugin.py,sha256=zU9es1wY49NZn9p2UgxFcSi3ERH43C9luys9WK3MTt0,1872
PIL/DcxImagePlugin.py,sha256=BTWIsAzKuATO640-xC-RQy1Mni1Huwqk7KUuMHO0mzw,2228
PIL/DdsImagePlugin.py,sha256=tUFTv6GYGos86zwWTJUxqck-2I_T-nGSQpVL5mQhBLc,19530
PIL/EpsImagePlugin.py,sha256=cUbIQL2BrfJrVB9flwo-Ie_OQZyQOkV0oByNTFjYZH0,16865
PIL/ExifTags.py,sha256=xG6PuZIJSXk7bfkBfXh11YgKlWVkF1HWQ_I7XywFBMs,10313
PIL/FitsImagePlugin.py,sha256=v4peLA6wIdySjHBzDMzVI5UWx8zr0jI2A3qYZSaMixs,4796
PIL/FliImagePlugin.py,sha256=h-RylemfZ05aQSjphswvuA6xO5bMqgGTKz0pCJHr8gM,4964
PIL/FontFile.py,sha256=iLSV32yQetLnE4SgG8HnHb2FdqkqFBjY9n--E6u5UE0,3711
PIL/FpxImagePlugin.py,sha256=0XAD64rzIVGTK0qzwp8Tn3pmoAP6Hj7I0Bvgk3MNVEs,7550
PIL/FtexImagePlugin.py,sha256=MZfpAImS_Tb7xa_sA0N1d_Rt6p8IngWiG9jDEf1VvJk,3649
PIL/GbrImagePlugin.py,sha256=x49ki3fwwQQre9Gi_Q4xb50ui4o-3TyE9S0mMqHTBR0,3109
PIL/GdImageFile.py,sha256=LjMalK77Vw9U5pBH4KQlbi2PaIr73u9DqtxN58O1rBs,2890
PIL/GifImagePlugin.py,sha256=I-Z_1-2zWD0dl-uzQS7ZdhPwakfnPkheS2rCLFl9dwY,43414
PIL/GimpGradientFile.py,sha256=tcSiN7MwWWJ7BEdk8g_3zefGr1O9fb2sleYw3rJmNsk,4055
PIL/GimpPaletteFile.py,sha256=F7R4QQqeUyHuFoisPkYLOdulChZ1MXe9XprCqyT7h0U,1887
PIL/GribStubImagePlugin.py,sha256=xoQxNLKaNfxdxGfYU1rKj8Zr86wl3-_eQvydLgP0zmU,1813
PIL/Hdf5StubImagePlugin.py,sha256=-Oz4WIQ_oXM_zk7PFpRgfaSHGGEXFTcidCJWg5Vfb5c,1816
PIL/IcnsImagePlugin.py,sha256=XyEKFLHdVmy9_s7db6HxNr1nUqlkdqwN1h-BxZ_iQxs,13360
PIL/IcoImagePlugin.py,sha256=3lRMAPIMX68AlDVHXnQ-DHOCsd_4FlPg6HA7m6NR1WI,12872
PIL/ImImagePlugin.py,sha256=1decJTjz8DGwLl4eHlnh4FRq2J0Y4PLlP4zE0quUrAo,11956
PIL/Image.py,sha256=aeg__oSAg6glVG45IW71-74SIfiQkN8d3g00IcYyUs0,152577
PIL/ImageChops.py,sha256=hZ8EPUPlQIzugsEedV8trkKX0jBCDGb6Cszma6ZeMZQ,8257
PIL/ImageCms.py,sha256=nLrb2iN62ANvZcg9-kp9mhWnQZUVyzsG94-qVRPistQ,43057
PIL/ImageColor.py,sha256=KV-u7HnZWrrL3zuBAOLqerI-7vFcXTxdLeoaYVjsnwI,9761
PIL/ImageDraw.py,sha256=x6-XaWLsYPjaox91uE18YF7-oM1CjAa7AtziNstmBYM,44077
PIL/ImageDraw2.py,sha256=_e6I2nPxeiZwjOjBEJzUvxoyAklzmc-xF6R8z8ieOAA,7470
PIL/ImageEnhance.py,sha256=ugDU0sljaR_zaTibtumlTvf-qFH1x1W6l2QMpc386XU,3740
PIL/ImageFile.py,sha256=2aFChKC4nuN1Sb4MSgltp5tNb-MvbEf3CsOkRa1Xdds,30256
PIL/ImageFilter.py,sha256=52m5fVj9pwCzN2ayyGRy3CxvcbzQ3zOk9evHCQmhA_g,19275
PIL/ImageFont.py,sha256=B9rvTnmZ97rSivc8CMXju8UQKTTcfH4jfJ3LYUwdng8,65631
PIL/ImageGrab.py,sha256=iFr90quXDE9p5yicxE09atuixPBGpkvvH358vANLAH4,6667
PIL/ImageMath.py,sha256=CTITjxiEdaMxJzxDNSbDItO8kS8TXXmkYfUdXiIT52A,12287
PIL/ImageMode.py,sha256=n4-2kSolyB7v2u6dXyIf3_vDL_LMvSNwhJvd9Do8cc8,2773
PIL/ImageMorph.py,sha256=E6kZhhpRypnHU8LoFgG4HkUoW3LfTr6rbv-fuFS9fDQ,8828
PIL/ImageOps.py,sha256=eRYdsbnTLDwMX82aY1ob2GXUSaaXKJMvrCcyfCGzXmo,26270
PIL/ImagePalette.py,sha256=kv7EuK55y6qYqC_PyFc-jP_fwRzJqwRCNcfuxvIrfXg,9295
PIL/ImagePath.py,sha256=ZnnJuvQNtbKRhCmr61TEmEh1vVV5_90WMEPL8Opy5l8,391
PIL/ImageQt.py,sha256=EHgnC27-Ept6Q4Khs7b4Rcz7dRuEns4VEsLTdVc-8pQ,7061
PIL/ImageSequence.py,sha256=5UohDzcf-2PA3NfGnMRd15zDDA3eD9Wo3SP3DxyRsCU,2286
PIL/ImageShow.py,sha256=TqO6nzj5EMwp5dExag3jhBJXdHojccnFx59LOnMQao0,10468
PIL/ImageStat.py,sha256=iA5KJrQeEpbwk-FhczGD8L4TaLWUt4VT_mp4drvMhv8,5485
PIL/ImageTk.py,sha256=Tx0zf9kTaBuAGvZuo0JAHrNbk3n6ewr4ZndWnWxZK0Q,8398
PIL/ImageTransform.py,sha256=Zu6oySXENtq712-nkqFLqafYPviRAgsgkzfLWG0K3V8,4052
PIL/ImageWin.py,sha256=b-fO6kn4icHpy-zk-Xg-nO382zPXl-MKjZcs3vAXl1Q,8332
PIL/ImtImagePlugin.py,sha256=r-ZDz3Xji6EBF8eUnK6iuCEZEtllp0xLRSIQ0_h14UE,2768
PIL/IptcImagePlugin.py,sha256=Q9TXBGi7vxByn9OAY4enmtJf_i2CyS9nuxmj3jCW14U,6969
PIL/Jpeg2KImagePlugin.py,sha256=u1sc7pMDVKIjvVQ-mLHKYYtRu4czfFfKQ5cY7-XRZBU,14307
PIL/JpegImagePlugin.py,sha256=MGuCHwvWM4Fa6E1CTYC7nTFsHbevC3phKdC0GJhmgms,32688
PIL/JpegPresets.py,sha256=UUIsKzvyzdPzsndODd90eu_luMqauG1PJh10UOuQvmg,12621
PIL/McIdasImagePlugin.py,sha256=P9d1kr4Ste0QVkxfqix31sfGD8sWn-eVmYK0wg6DvIw,1955
PIL/MicImagePlugin.py,sha256=rh_HxdUdd6pYHB7qiMmYLTmD9jlLWnYfQcDx415rcI8,2666
PIL/MpegImagePlugin.py,sha256=kem9zofsBtIq6xlMJcsFkOXTcJCPEICk9lchTfR9pDI,2094
PIL/MpoImagePlugin.py,sha256=7ogrGzUL_tC64idGl4OfrcPJ0SAvHMRZw3jv21mvN7c,6924
PIL/MspImagePlugin.py,sha256=W8ruM60OZUbiPuUBQ7BBkFzkl1fiaPEvsvRMyNrg18A,6092
PIL/PSDraw.py,sha256=uAr-Mg5j9_G3RLwWx4_J1wutJg_DVeDAE4qWd3Mvi9U,7155
PIL/PaletteFile.py,sha256=QdEa99jLC-PPhEqGy6_4fDua8TzKSSUzkAeYcClFX7M,1270
PIL/PalmImagePlugin.py,sha256=fNIhWH06V35uLgwIHUMMc_2xumelIEJsmCwhw2whBxI,8965
PIL/PcdImagePlugin.py,sha256=W4xMr8ex-K4r2r-1tKlRsI_x4E362PLtVhHAWmOt1pQ,1665
PIL/PcfFontFile.py,sha256=RkM5wUp3SgRpQhpsTBEtk8uuFrQPnBSYBryOmcoRphQ,7401
PIL/PcxImagePlugin.py,sha256=DUk8AoXKTmXl5yVknXVOAB6ZU3HWaCDCQ3SKPd3BQmc,6452
PIL/PdfImagePlugin.py,sha256=-01K9TRr_ekIRxokuBw3N-_t5gQmn23rJXj6-B2-Gpk,9660
PIL/PdfParser.py,sha256=64t7lZv85TJ6FXLiTIyX5kwCM-LwqceA-2amqBX0MtQ,39061
PIL/PixarImagePlugin.py,sha256=-RnuJSGhGF8MsUsiHZq3VzBxV-BNnK35kBjJfhYzwXs,1830
PIL/PngImagePlugin.py,sha256=o_OUyE1dxN0mO3FfLZMl0598fVZVHY76tNvcq-G50Z4,52668
PIL/PpmImagePlugin.py,sha256=fykSAFcXmPlo2agWtPzr3pWBiU445yOdEalpoyWHJQI,12745
PIL/PsdImagePlugin.py,sha256=eYQ33CuZDZ_eGw4gJSVsXYw2r8VP8VYlGDroBZOgaSg,9018
PIL/QoiImagePlugin.py,sha256=3dnB03zEw3swx_o8PHZZw15W7tBdAKv0eQ7FkjOC-gA,8806
PIL/SgiImagePlugin.py,sha256=w1xV4-ELYBHKL_AaGf9oJSYJzBwTpdJ-rNPqiArKgGs,6620
PIL/SpiderImagePlugin.py,sha256=6zvFg874J8FU0GDWHrZQP8KEjZnoDjqtqiSpjgfdR5o,10580
PIL/SunImagePlugin.py,sha256=YKYEvuG4QUkies34OKtXWKTYSZ8U3qzcE_vdTrOuRsw,4734
PIL/TarIO.py,sha256=K6tLkFDcaar3vb6EyYb7G4oVEB41x_LMuGJeUT9w5DQ,1503
PIL/TgaImagePlugin.py,sha256=OMvZn_xKjB1dZ1_4MkOquzJBHpSUIpAf5mUEJZiLBTI,7244
PIL/TiffImagePlugin.py,sha256=s02yD54IKv9qMl6hIb3Z5f9rQYdho3ck_VxU3lfsG9g,87368
PIL/TiffTags.py,sha256=CmDDo0yRJ4lD-tvB00RWyNlDbSjhQx8QhDzJOr1zoZI,17644
PIL/WalImageFile.py,sha256=XzvTP_kO_JuumDBXV4FTRJJG1xhx4KqMnXDkStpaYbk,5831
PIL/WebPImagePlugin.py,sha256=7h1Wz1R5vPV6hOQKgtQ_EtBTASENaZOTGUjG2TJqe0U,10330
PIL/WmfImagePlugin.py,sha256=-v-G3iEWKdQtAuAuVuip6eeS-dPWkdJ_IALlcod7QQk,5429
PIL/XVThumbImagePlugin.py,sha256=_sd2nB_kXi9V-4QwRH39qIMjIwrwc2JpP-_xZGO7MFk,2198
PIL/XbmImagePlugin.py,sha256=wc0NpfTzlShx9Ymi0zwo0aVZjDa4_B7NEiG2TgCO65Q,2767
PIL/XpmImagePlugin.py,sha256=wYf7Q7TGo4JL6n5ouQx8pbTMnMZVshgp-xwoAMZEUKg,4557
PIL/__init__.py,sha256=Z-sXBmtIAVmDwqwBdI6L4RWjWxcgVvQ3v4SG0NxKeWU,2118
PIL/__main__.py,sha256=X8eIpGlmHfnp7zazp5mdav228Itcf2lkiMP0tLU6X9c,140
PIL/__pycache__/AvifImagePlugin.cpython-312.pyc,,
PIL/__pycache__/BdfFontFile.cpython-312.pyc,,
PIL/__pycache__/BlpImagePlugin.cpython-312.pyc,,
PIL/__pycache__/BmpImagePlugin.cpython-312.pyc,,
PIL/__pycache__/BufrStubImagePlugin.cpython-312.pyc,,
PIL/__pycache__/ContainerIO.cpython-312.pyc,,
PIL/__pycache__/CurImagePlugin.cpython-312.pyc,,
PIL/__pycache__/DcxImagePlugin.cpython-312.pyc,,
PIL/__pycache__/DdsImagePlugin.cpython-312.pyc,,
PIL/__pycache__/EpsImagePlugin.cpython-312.pyc,,
PIL/__pycache__/ExifTags.cpython-312.pyc,,
PIL/__pycache__/FitsImagePlugin.cpython-312.pyc,,
PIL/__pycache__/FliImagePlugin.cpython-312.pyc,,
PIL/__pycache__/FontFile.cpython-312.pyc,,
PIL/__pycache__/FpxImagePlugin.cpython-312.pyc,,
PIL/__pycache__/FtexImagePlugin.cpython-312.pyc,,
PIL/__pycache__/GbrImagePlugin.cpython-312.pyc,,
PIL/__pycache__/GdImageFile.cpython-312.pyc,,
PIL/__pycache__/GifImagePlugin.cpython-312.pyc,,
PIL/__pycache__/GimpGradientFile.cpython-312.pyc,,
PIL/__pycache__/GimpPaletteFile.cpython-312.pyc,,
PIL/__pycache__/GribStubImagePlugin.cpython-312.pyc,,
PIL/__pycache__/Hdf5StubImagePlugin.cpython-312.pyc,,
PIL/__pycache__/IcnsImagePlugin.cpython-312.pyc,,
PIL/__pycache__/IcoImagePlugin.cpython-312.pyc,,
PIL/__pycache__/ImImagePlugin.cpython-312.pyc,,
PIL/__pycache__/Image.cpython-312.pyc,,
PIL/__pycache__/ImageChops.cpython-312.pyc,,
PIL/__pycache__/ImageCms.cpython-312.pyc,,
PIL/__pycache__/ImageColor.cpython-312.pyc,,
PIL/__pycache__/ImageDraw.cpython-312.pyc,,
PIL/__pycache__/ImageDraw2.cpython-312.pyc,,
PIL/__pycache__/ImageEnhance.cpython-312.pyc,,
PIL/__pycache__/ImageFile.cpython-312.pyc,,
PIL/__pycache__/ImageFilter.cpython-312.pyc,,
PIL/__pycache__/ImageFont.cpython-312.pyc,,
PIL/__pycache__/ImageGrab.cpython-312.pyc,,
PIL/__pycache__/ImageMath.cpython-312.pyc,,
PIL/__pycache__/ImageMode.cpython-312.pyc,,
PIL/__pycache__/ImageMorph.cpython-312.pyc,,
PIL/__pycache__/ImageOps.cpython-312.pyc,,
PIL/__pycache__/ImagePalette.cpython-312.pyc,,
PIL/__pycache__/ImagePath.cpython-312.pyc,,
PIL/__pycache__/ImageQt.cpython-312.pyc,,
PIL/__pycache__/ImageSequence.cpython-312.pyc,,
PIL/__pycache__/ImageShow.cpython-312.pyc,,
PIL/__pycache__/ImageStat.cpython-312.pyc,,
PIL/__pycache__/ImageTk.cpython-312.pyc,,
PIL/__pycache__/ImageTransform.cpython-312.pyc,,
PIL/__pycache__/ImageWin.cpython-312.pyc,,
PIL/__pycache__/ImtImagePlugin.cpython-312.pyc,,
PIL/__pycache__/IptcImagePlugin.cpython-312.pyc,,
PIL/__pycache__/Jpeg2KImagePlugin.cpython-312.pyc,,
PIL/__pycache__/JpegImagePlugin.cpython-312.pyc,,
PIL/__pycache__/JpegPresets.cpython-312.pyc,,
PIL/__pycache__/McIdasImagePlugin.cpython-312.pyc,,
PIL/__pycache__/MicImagePlugin.cpython-312.pyc,,
PIL/__pycache__/MpegImagePlugin.cpython-312.pyc,,
PIL/__pycache__/MpoImagePlugin.cpython-312.pyc,,
PIL/__pycache__/MspImagePlugin.cpython-312.pyc,,
PIL/__pycache__/PSDraw.cpython-312.pyc,,
PIL/__pycache__/PaletteFile.cpython-312.pyc,,
PIL/__pycache__/PalmImagePlugin.cpython-312.pyc,,
PIL/__pycache__/PcdImagePlugin.cpython-312.pyc,,
PIL/__pycache__/PcfFontFile.cpython-312.pyc,,
PIL/__pycache__/PcxImagePlugin.cpython-312.pyc,,
PIL/__pycache__/PdfImagePlugin.cpython-312.pyc,,
PIL/__pycache__/PdfParser.cpython-312.pyc,,
PIL/__pycache__/PixarImagePlugin.cpython-312.pyc,,
PIL/__pycache__/PngImagePlugin.cpython-312.pyc,,
PIL/__pycache__/PpmImagePlugin.cpython-312.pyc,,
PIL/__pycache__/PsdImagePlugin.cpython-312.pyc,,
PIL/__pycache__/QoiImagePlugin.cpython-312.pyc,,
PIL/__pycache__/SgiImagePlugin.cpython-312.pyc,,
PIL/__pycache__/SpiderImagePlugin.cpython-312.pyc,,
PIL/__pycache__/SunImagePlugin.cpython-312.pyc,,
PIL/__pycache__/TarIO.cpython-312.pyc,,
PIL/__pycache__/TgaImagePlugin.cpython-312.pyc,,
PIL/__pycache__/TiffImagePlugin.cpython-312.pyc,,
PIL/__pycache__/TiffTags.cpython-312.pyc,,
PIL/__pycache__/WalImageFile.cpython-312.pyc,,
PIL/__pycache__/WebPImagePlugin.cpython-312.pyc,,
PIL/__pycache__/WmfImagePlugin.cpython-312.pyc,,
PIL/__pycache__/XVThumbImagePlugin.cpython-312.pyc,,
PIL/__pycache__/XbmImagePlugin.cpython-312.pyc,,
PIL/__pycache__/XpmImagePlugin.cpython-312.pyc,,
PIL/__pycache__/__init__.cpython-312.pyc,,
PIL/__pycache__/__main__.cpython-312.pyc,,
PIL/__pycache__/_binary.cpython-312.pyc,,
PIL/__pycache__/_deprecate.cpython-312.pyc,,
PIL/__pycache__/_tkinter_finder.cpython-312.pyc,,
PIL/__pycache__/_typing.cpython-312.pyc,,
PIL/__pycache__/_util.cpython-312.pyc,,
PIL/__pycache__/_version.cpython-312.pyc,,
PIL/__pycache__/features.cpython-312.pyc,,
PIL/__pycache__/report.cpython-312.pyc,,
PIL/_avif.cp312-win_amd64.pyd,sha256=f5k67YDU38uhuufjB-ENoP1Crg_EaHYDKsE46lEbVcM,7833600
PIL/_avif.pyi,sha256=zD8vAoPC8aEIVjfckLtFskRW5saiVel3-sJUA2pHaGc,66
PIL/_binary.py,sha256=cb9p-_mwzBYumlVsWbnoTWsrLo59towA6atLOZvjO3w,2662
PIL/_deprecate.py,sha256=-UAXsGjFgZP5LWo9n3qtrgSW8w9jZmS8_yBvhYzFX0A,2106
PIL/_imaging.cp312-win_amd64.pyd,sha256=A8ZimzfVFHcNM7_rHAIXnlB3muZ1hzEDEKlrLeXnFCo,2491392
PIL/_imaging.pyi,sha256=0c3GC20XgHn8HaIrEYPErvCABBq_wibJlRa8A3RsUk8,899
PIL/_imagingcms.cp312-win_amd64.pyd,sha256=Dj3BuAbEEuBydv5DaXUN4CAGUCYliBABbBv3q6vJOIU,266240
PIL/_imagingcms.pyi,sha256=oB0dV9kzqnZk3CtnVzgZvwpRsPUqbltBZ19xLin7uHo,4532
PIL/_imagingft.cp312-win_amd64.pyd,sha256=_Uay6eNB865RGrSjMqdZw9fVOxR7k4yGudM_FoDo2n8,2011136
PIL/_imagingft.pyi,sha256=EJ7RbmqGSewhT6E5EM1y1dmcOUdhPGP0u4Dub2KZZG0,1875
PIL/_imagingmath.cp312-win_amd64.pyd,sha256=eAfX71JqBXaOwQCH35p1zFj8M17jcHtnxmxdRfo_6e0,25088
PIL/_imagingmath.pyi,sha256=zD8vAoPC8aEIVjfckLtFskRW5saiVel3-sJUA2pHaGc,66
PIL/_imagingmorph.cp312-win_amd64.pyd,sha256=6AN0lZSfOGDMJPSkOZoplf6llTiEkt802sWUj0BwT74,13824
PIL/_imagingmorph.pyi,sha256=zD8vAoPC8aEIVjfckLtFskRW5saiVel3-sJUA2pHaGc,66
PIL/_imagingtk.cp312-win_amd64.pyd,sha256=HtEDgzE8FTWRcuwvIktmU3yok_jpr6hTVSBIJoBL-7M,14848
PIL/_imagingtk.pyi,sha256=zD8vAoPC8aEIVjfckLtFskRW5saiVel3-sJUA2pHaGc,66
PIL/_tkinter_finder.py,sha256=H1Uw3dV7gWHIj_osUnJFxWh1aRsQ2MdKpNP3vQwQcW0,558
PIL/_typing.py,sha256=CN6gMruPZC60YxL7q6GFki1__cnV9pNdjyZ4or8bj1s,1305
PIL/_util.py,sha256=c1SFb0eh9D_Sho4-YMFDZP5YOlpkOicqY7k5TCSrj_A,661
PIL/_version.py,sha256=3szxQoJBLuHlwr0BI4V_LdVJDR8BUMLWavUP6suteuw,91
PIL/_webp.cp312-win_amd64.pyd,sha256=snnGJfB7yy6uzFM_6kGNKlOMv7AWHwn31Qv0hJWO80M,409600
PIL/_webp.pyi,sha256=zD8vAoPC8aEIVjfckLtFskRW5saiVel3-sJUA2pHaGc,66
PIL/features.py,sha256=6KvgHmp0TcEJepMBiFBRRaOCtDB8rHfsy_QikEs8Mwg,11840
PIL/py.typed,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
PIL/report.py,sha256=6m7NOv1a24577ZiJoxX89ip5JeOgf2O1F95f6-1K5aM,105
pillow-11.3.0.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
pillow-11.3.0.dist-info/METADATA,sha256=9kqN5VBRkuotyWIwvRVvOpQAlhPyFhiyFYUBSZbAeu0,9200
pillow-11.3.0.dist-info/RECORD,,
pillow-11.3.0.dist-info/REQUESTED,sha256=47DEQpj8HBSa-_TImW-5JCeuQeRkm5NMpJWZG3hSuFU,0
pillow-11.3.0.dist-info/WHEEL,sha256=8UP9x9puWI0P1V_d7K2oMTBqfeLNm21CTzZ_Ptr0NXU,101
pillow-11.3.0.dist-info/licenses/LICENSE,sha256=GVRL9Tr2iaMxmk-1BnNw8PDWjSxTAKIVdnT174pP2WU,76724
pillow-11.3.0.dist-info/top_level.txt,sha256=riZqrk-hyZqh5f1Z0Zwii3dKfxEsByhu9cU9IODF-NY,4
pillow-11.3.0.dist-info/zip-safe,sha256=frcCV1k9oG9oKj3dpUqdJg1PxRT2RSN_XKdLCPjaYaY,2
