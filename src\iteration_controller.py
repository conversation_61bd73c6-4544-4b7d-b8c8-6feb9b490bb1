"""
迭代控制器模块
实现多轮迭代的主控制逻辑
"""
import time
from typing import Dict, Any, Optional, List
from datetime import datetime

from .state import (
    ReportState, IterationStepResult, GenerationPhaseResult, 
    QualityCheckPhaseResult, IterationPhase, QualityMetrics
)
from .config import Config
from .logger import ProcessLogger


class IIterationController:
    """迭代控制器接口"""
    
    def execute_iterative_report_generation(self, state: ReportState) -> ReportState:
        """执行迭代式报告生成"""
        raise NotImplementedError
    
    def should_continue_iteration(self, state: ReportState) -> bool:
        """判断是否应该继续迭代"""
        raise NotImplementedError
    
    def calculate_convergence(self, state: ReportState) -> bool:
        """计算是否已收敛"""
        raise NotImplementedError


class IterationController(IIterationController):
    """
    迭代控制器实现
    负责管理3轮迭代的主控制流程
    """
    
    def __init__(self, config: Config, logger: ProcessLogger):
        self.config = config
        self.logger = logger
        self.max_iterations = config.report.get("max_iterations", 3)
        self.quality_threshold = config.report.get("quality_threshold", 0.8)
        self.convergence_threshold = config.report.get("convergence_threshold", 0.05)
    
    def execute_iterative_report_generation(self, state: ReportState) -> ReportState:
        """
        执行3轮迭代的报告生成主流程
        
        Args:
            state: 报告状态
            
        Returns:
            更新后的报告状态
        """
        self.logger.logger.info("开始执行多轮迭代报告生成")
        state.iteration_result.start_time = datetime.now()
        
        try:
            # 执行迭代循环
            while self.should_continue_iteration(state):
                self.logger.logger.info(f"开始第 {state.current_iteration + 1} 轮迭代")
                
                # 开始新的迭代步骤
                iteration_step = state.start_new_iteration_step()
                
                # 执行生成阶段
                generation_result = self._execute_generation_phase(state)
                iteration_step.generation_result = generation_result
                
                # 执行质量检查阶段
                quality_result = self._execute_quality_check_phase(state)
                iteration_step.quality_check_result = quality_result
                
                # 应用改进
                improvements_count = self._apply_improvements(state, quality_result)
                iteration_step.improvements_applied = improvements_count
                
                # 完成迭代步骤
                state.complete_current_iteration_step()
                state.increment_iteration()
                
                # 检查收敛性
                if self.calculate_convergence(state):
                    self.logger.logger.info(f"在第 {state.current_iteration} 轮迭代后达到收敛")
                    break
                
                self.logger.logger.info(f"第 {state.current_iteration} 轮迭代完成")
            
            # 标记迭代完成
            state.iteration_result.mark_completed()
            state.iteration_result.final_quality_score = state.get_current_quality_score()
            
            self.logger.logger.info(
                f"迭代过程完成，总共 {state.current_iteration} 轮，"
                f"最终质量分数: {state.iteration_result.final_quality_score:.3f}"
            )
            
        except Exception as e:
            self.logger.logger.error(f"迭代过程中发生错误: {str(e)}")
            state.add_error(f"迭代控制器错误: {str(e)}", "iteration_controller")
            raise
        
        return state
    
    def should_continue_iteration(self, state: ReportState) -> bool:
        """
        判断是否应该继续迭代
        
        Args:
            state: 报告状态
            
        Returns:
            是否继续迭代
        """
        # 检查最大迭代次数
        if state.current_iteration >= self.max_iterations:
            self.logger.logger.info(f"达到最大迭代次数 {self.max_iterations}")
            return False
        
        # 检查质量阈值
        current_quality = state.get_current_quality_score()
        if current_quality >= self.quality_threshold:
            self.logger.logger.info(f"达到质量阈值 {self.quality_threshold}")
            return False
        
        # 检查收敛性
        if state.iteration_result.convergence_achieved:
            self.logger.logger.info("检测到收敛")
            return False
        
        return True
    
    def calculate_convergence(self, state: ReportState) -> bool:
        """
        计算是否已收敛
        
        Args:
            state: 报告状态
            
        Returns:
            是否已收敛
        """
        if len(state.iteration_result.iteration_steps) < 2:
            return False
        
        # 获取最近两次迭代的质量分数
        recent_steps = state.iteration_result.iteration_steps[-2:]
        quality_improvement = abs(
            recent_steps[1].quality_score_after - recent_steps[0].quality_score_after
        )
        
        # 如果质量改进小于阈值，认为已收敛
        converged = quality_improvement < self.convergence_threshold
        
        if converged:
            state.iteration_result.convergence_achieved = True
            self.logger.logger.info(
                f"检测到收敛，质量改进: {quality_improvement:.4f} < {self.convergence_threshold}"
            )
        
        return converged
    
    def _execute_generation_phase(self, state: ReportState) -> GenerationPhaseResult:
        """执行生成阶段"""
        result = GenerationPhaseResult(
            phase=IterationPhase.GENERATION,
            iteration_number=state.current_iteration + 1
        )
        
        try:
            # 这里会调用分层内容生成器
            # 暂时返回成功结果，具体实现在分层生成器中
            result.success = True
            result.mark_completed()
            
        except Exception as e:
            result.success = False
            result.error_message = str(e)
            self.logger.logger.error(f"生成阶段失败: {str(e)}")
        
        return result
    
    def _execute_quality_check_phase(self, state: ReportState) -> QualityCheckPhaseResult:
        """执行质量检查阶段"""
        result = QualityCheckPhaseResult(
            iteration_number=state.current_iteration + 1
        )
        
        try:
            # 这里会调用质量检查器
            # 暂时返回成功结果，具体实现在质量检查器中
            result.success = True
            result.mark_completed()
            
        except Exception as e:
            result.success = False
            result.error_message = str(e)
            self.logger.logger.error(f"质量检查阶段失败: {str(e)}")
        
        return result
    
    def _apply_improvements(self, state: ReportState, quality_result: QualityCheckPhaseResult) -> int:
        """应用改进"""
        improvements_count = 0
        
        try:
            # 这里会调用质量改进引擎
            # 暂时返回0，具体实现在质量改进引擎中
            pass
            
        except Exception as e:
            self.logger.logger.error(f"应用改进失败: {str(e)}")
        
        return improvements_count


class IterationStageCoordinator:
    """
    迭代阶段协调器
    负责协调生成阶段和检查阶段的数据传递和状态同步
    """
    
    def __init__(self, config: Config, logger: ProcessLogger):
        self.config = config
        self.logger = logger
    
    def coordinate_generation_to_quality_check(
        self, 
        state: ReportState, 
        generation_result: GenerationPhaseResult
    ) -> Dict[str, Any]:
        """
        协调从生成阶段到质量检查阶段的数据传递
        
        Args:
            state: 报告状态
            generation_result: 生成阶段结果
            
        Returns:
            传递给质量检查阶段的数据
        """
        coordination_data = {
            "report_structure": state.report_structure,
            "generated_content": generation_result.content_generated,
            "iteration_number": generation_result.iteration_number,
            "nodes_to_check": generation_result.nodes_processed
        }
        
        self.logger.logger.info(
            f"协调数据传递：生成阶段 -> 质量检查阶段，"
            f"节点数: {generation_result.nodes_processed}"
        )
        
        return coordination_data
    
    def coordinate_quality_check_to_improvement(
        self, 
        state: ReportState, 
        quality_result: QualityCheckPhaseResult
    ) -> Dict[str, Any]:
        """
        协调从质量检查阶段到改进阶段的数据传递
        
        Args:
            state: 报告状态
            quality_result: 质量检查结果
            
        Returns:
            传递给改进阶段的数据
        """
        coordination_data = {
            "quality_issues": state.get_total_quality_issues(),
            "average_quality_score": quality_result.average_quality_score,
            "issues_by_severity": quality_result.issues_by_severity,
            "issues_by_dimension": quality_result.issues_by_dimension,
            "iteration_number": quality_result.iteration_number
        }
        
        self.logger.logger.info(
            f"协调数据传递：质量检查阶段 -> 改进阶段，"
            f"问题数: {quality_result.total_issues_found}"
        )
        
        return coordination_data
    
    def handle_stage_exception(self, stage_name: str, exception: Exception, state: ReportState):
        """
        处理阶段异常
        
        Args:
            stage_name: 阶段名称
            exception: 异常对象
            state: 报告状态
        """
        error_msg = f"阶段 {stage_name} 发生异常: {str(exception)}"
        self.logger.logger.error(error_msg)
        state.add_error(error_msg, stage_name, {"exception_type": type(exception).__name__})
        
        # 实现恢复机制
        self._attempt_stage_recovery(stage_name, state)
    
    def _attempt_stage_recovery(self, stage_name: str, state: ReportState):
        """尝试阶段恢复"""
        self.logger.logger.info(f"尝试恢复阶段: {stage_name}")
        
        # 这里可以实现具体的恢复逻辑
        # 例如：重置阶段状态、使用备用策略等
        pass
