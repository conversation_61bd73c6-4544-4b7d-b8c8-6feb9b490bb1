#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
并行处理模块
负责可以并发执行的工作流步骤
"""

import asyncio
from typing import List, Dict, Any, Optional
from pathlib import Path


class ParallelProcessor:
    """
    并行处理器
    处理可以并发执行的工作流步骤
    """

    def __init__(self, generator):
        """
        初始化并行处理器
        
        Args:
            generator: 主生成器实例
        """
        self.generator = generator
        self.api_manager = generator.api_manager
        self.report_config = generator.report_config

    # ==================== 第二阶段：内容生成（并行） ====================

    async def content_generation_stage(self, sections: List[Dict[str, Any]], data_sources: List[str]):
        """
        第二阶段：执行模型并行生成具体内容
        
        这个阶段可以并行执行，因为：
        1. 各章节内容相对独立
        2. 执行模型可以并发工作
        3. 大幅提升生成速度
        """
        print("\n" + "="*80)
        print("⚡ 第二阶段：执行模型(gemini-2.5-flash)并行生成具体内容")
        print("="*80)

        await self._generate_all_content_parallel(sections, data_sources)

    async def _generate_all_content_parallel(self, sections: List[Dict[str, Any]], data_sources: List[str]):
        """并行生成所有内容"""
        print("   🎯 所有gemini-2.5-flash并行生成具体内容")

        # 收集所有需要生成内容的节点
        all_nodes = []
        def collect_nodes_with_data_source(nodes, section_idx=0):
            for node in nodes:
                node_info = {
                    'node': node,
                    'section_idx': section_idx,
                    'data_source': data_sources[section_idx] if section_idx < len(data_sources) else ""
                }
                all_nodes.append(node_info)
                
                if 'children' in node and node['children']:
                    collect_nodes_with_data_source(node['children'], section_idx)

        for section_idx, section in enumerate(sections):
            collect_nodes_with_data_source([section], section_idx)

        # 创建并行任务
        content_tasks = []
        for node_info in all_nodes:
            task = self._generate_node_content_parallel(
                node_info['node'], 
                node_info['data_source'], 
                node_info['section_idx']
            )
            content_tasks.append(task)

        # 并行执行所有任务
        if content_tasks:
            batch_size = self._calculate_optimal_batch_size(len(content_tasks))
            print(f"   🚀 并行生成: {len(content_tasks)}个节点，批次大小: {batch_size}")
            print(f"   ⚡ 使用模型: gemini-2.5-flash（执行模型）")

            # 分批并行执行（优化版，带进度条）
            await self._execute_tasks_in_batches(content_tasks, batch_size, "内容生成")

            print(f"   🎉 所有内容生成完成！")

    async def _generate_node_content_parallel(self, node: Dict[str, Any], data_source: str, section_idx: int) -> str:
        """并行生成单个节点内容"""
        title = node.get("title", "")
        level = node.get("level", 1)
        task_instruction = node.get("task_instruction", "")

        # 读取数据源
        loop = asyncio.get_event_loop()
        data_content = await loop.run_in_executor(None, self.generator.read_data_source, data_source)

        # 构建内容生成prompt
        prompt = f"""
根据统筹模型的任务指导，为节点"{title}"（第{level}级标题）生成详细内容。

任务指导：
{task_instruction}

相关数据源：
{data_content[:2000] if data_content else "无特定数据"}

要求：
1. 严格按照任务指导执行
2. 内容详细、专业、准确
3. 字数控制在{self._get_word_count_by_level(level)}字左右
4. 语言专业、逻辑清晰
5. 包含适当的数据引用，格式为[来源: 数据源]

请生成内容：
"""

        try:
            content = await self.generator.call_executor_model_async(prompt)
            if content:
                cleaned_content = self.generator._clean_model_response(content)
                final_content = self.generator._extract_final_content_only(cleaned_content)
                node["content"] = final_content
                return final_content
            else:
                node["content"] = f"节点 {title} 的内容生成失败"
                return ""
        except Exception as e:
            print(f"⚠️ 节点 {title} 内容生成失败: {str(e)}")
            node["content"] = f"节点 {title} 的内容生成遇到技术问题"
            return ""

    # ==================== 第三阶段：章节优化（并行） ====================

    async def section_optimization_stage(self, sections: List[Dict[str, Any]], data_sources: List[str], iteration: int):
        """
        章节审核和优化（并行）
        
        这个阶段可以并行执行，因为：
        1. 各章节可以独立审核和优化
        2. 统筹模型可以并发处理多个章节
        3. 显著提升优化效率
        """
        print(f"📋 第{iteration}轮：八个一级标题并行审核和优化")

        # 创建章节审核任务
        audit_tasks = []
        for i, section in enumerate(sections):
            if i < len(data_sources):
                task = self._audit_and_optimize_section_parallel(
                    section, data_sources[i], iteration, i+1
                )
                audit_tasks.append(task)

        # 并行执行章节审核和优化
        if audit_tasks:
            batch_size = self._calculate_optimal_batch_size(len(audit_tasks))
            print(f"   🚀 使用批次大小: {batch_size}，总任务: {len(audit_tasks)}")

            # 分批执行以避免超过API并发限制
            await self._execute_tasks_in_batches(audit_tasks, batch_size, "章节优化")

    async def _audit_and_optimize_section_parallel(self, section: Dict[str, Any], data_source: str, iteration: int, section_num: int):
        """并行审核和优化单个章节"""
        title = section.get("title", f"章节{section_num}")
        print(f"   📋 并行处理章节 {section_num}: {title}")

        try:
            # 步骤1：审核章节
            audit_result = await self._audit_section_parallel(section, data_source, iteration)

            # 步骤2：优化章节（如果需要）
            if audit_result.get("needs_optimization", False):
                await self._optimize_section_parallel(section, data_source, audit_result, iteration)
                print(f"   ✅ 章节 {section_num} 优化完成")
            else:
                print(f"   ✅ 章节 {section_num} 无需优化")

        except Exception as e:
            print(f"   ❌ 章节 {section_num} 处理失败: {str(e)}")

    async def _audit_section_parallel(self, section: Dict[str, Any], data_source: str, iteration: int) -> Dict[str, Any]:
        """并行审核单个章节"""
        title = section.get("title", "")
        content = section.get("content", "")

        # 读取相关数据源作为参考
        loop = asyncio.get_event_loop()
        data_content = await loop.run_in_executor(None, self.generator.read_data_source, data_source)

        prompt = f"""
作为专业的报告统筹模型，请对以下章节进行深度审核：

章节标题：{title}
章节内容：
{content}

参考数据源：
{data_content[:2000] if data_content else ""}

请从以下维度进行严格审核：
1. 内容完整性：是否涵盖了该章节应有的所有要点
2. 逻辑严谨性：论述是否逻辑清晰、前后一致
3. 数据准确性：引用的数据是否准确、来源是否可靠
4. 深度分析：是否进行了深入的分析和洞察
5. 客观性：是否保持客观中立的立场
6. 专业性：表述是否专业、术语使用是否准确

这是第{iteration}轮审核。

请以JSON格式返回审核结果：
{{
    "overall_score": 8.5,
    "needs_optimization": true,
    "issues": [
        {{
            "category": "内容完整性",
            "description": "缺少对XX方面的分析",
            "severity": "中等"
        }}
    ],
    "optimization_requirements": [
        "补充XX方面的详细分析",
        "更新最新的市场数据"
    ]
}}
"""

        response = await self.generator.call_orchestrator_model_async(prompt)

        try:
            import json
            if "```json" in response:
                start = response.find("```json") + 7
                end = response.find("```", start)
                if end != -1:
                    json_str = response[start:end].strip()
                    return json.loads(json_str)

            if response.strip().startswith("{"):
                return json.loads(response.strip())

            return {"overall_score": 7.0, "needs_optimization": False, "issues": [], "optimization_requirements": []}

        except Exception as e:
            print(f"解析章节审核结果失败: {str(e)}")
            return {"overall_score": 7.0, "needs_optimization": False, "issues": [], "optimization_requirements": []}

    async def _optimize_section_parallel(self, section: Dict[str, Any], data_source: str, audit_result: Dict[str, Any], iteration: int):
        """并行优化单个章节"""
        title = section.get("title", "")
        content = section.get("content", "")
        optimization_requirements = audit_result.get("optimization_requirements", [])
        issues = audit_result.get("issues", [])

        # 读取相关数据源
        loop = asyncio.get_event_loop()
        data_content = await loop.run_in_executor(None, self.generator.read_data_source, data_source)

        prompt = f"""
作为专业的报告统筹模型，请根据审核结果优化以下章节：

章节标题：{title}
当前内容：
{content}

审核发现的问题：
{json.dumps(issues, ensure_ascii=False, indent=2)}

优化要求：
{chr(10).join(f"- {req}" for req in optimization_requirements)}

参考数据源：
{data_content[:2000] if data_content else ""}

请按照以下要求进行优化：
1. 针对每个问题提供具体的改进
2. 确保内容更加全面、深度、严谨、客观
3. 保持专业的表述和准确的数据引用
4. 增强逻辑性和可读性
5. 保持原有结构的基础上进行内容优化

这是第{iteration}轮优化。

请直接返回优化后的完整章节内容：
"""

        response = await self.generator.call_orchestrator_model_async(prompt)
        
        if response:
            cleaned_content = self.generator._clean_model_response(response)
            final_content = self.generator._extract_final_content_only(cleaned_content)
            section["content"] = final_content

    # ==================== 参考报告学习（并行） ====================

    async def reference_learning_stage(self, sections: List[Dict[str, Any]], topic: str):
        """
        参考报告学习优化（并行）
        
        这个阶段可以并行执行，因为：
        1. 各章节可以独立应用学习结果
        2. 并行处理提升效率
        3. 学习结果应用相对独立
        """
        reference_path = self.report_config.get("reference_report", "")
        if not reference_path or not Path(reference_path).exists():
            print("   ⚠️ 参考报告路径无效，跳过参考优化")
            return

        print(f"   📖 读取参考报告: {reference_path}")
        
        # 读取和分析参考报告
        loop = asyncio.get_event_loop()
        reference_content = await loop.run_in_executor(None, self.generator.read_framework_file, reference_path)

        if not reference_content:
            print("   ⚠️ 参考报告内容为空，跳过参考优化")
            return

        # 学习参考报告（这部分需要串行）
        learning_data = await self._analyze_reference_report(reference_content, topic)

        # 应用学习结果到所有章节（这部分可以并行）
        await self._apply_reference_learning_parallel(sections, learning_data, topic)

        print(f"   ✅ 参考报告优化完成")

    async def _analyze_reference_report(self, reference_content: str, topic: str) -> Dict[str, Any]:
        """分析参考报告（串行）"""
        prompt = f"""
请分析以下参考报告，提取可用于优化当前报告的关键信息：

参考报告内容：
{reference_content[:3000]}

当前报告主题：{topic}

请从以下维度分析：
1. 内容结构特点
2. 写作风格特色
3. 格式规范标准
4. 专业表达方式

请以JSON格式返回分析结果：
{{
    "content_structure": "结构特点描述",
    "writing_style": "写作风格描述",
    "format_standards": "格式标准描述",
    "professional_standards": "专业标准描述"
}}
"""

        response = await self.generator.call_orchestrator_model_async(prompt)
        
        try:
            import json
            if "```json" in response:
                start = response.find("```json") + 7
                end = response.find("```", start)
                if end != -1:
                    json_str = response[start:end].strip()
                    return json.loads(json_str)

            if response.strip().startswith("{"):
                return json.loads(response.strip())

        except:
            pass

        return {
            "content_structure": "标准产业报告结构",
            "writing_style": "专业严谨",
            "format_standards": "规范格式",
            "professional_standards": "行业标准"
        }

    async def _apply_reference_learning_parallel(self, sections: List[Dict[str, Any]], learning_data: Dict[str, Any], topic: str):
        """并行应用参考学习到所有章节"""
        try:
            from tqdm.asyncio import tqdm
        except ImportError:
            from tqdm import tqdm

        # 创建所有章节的优化任务
        optimization_tasks = []
        for i, section in enumerate(sections):
            task = self._apply_reference_learning_to_section(section, learning_data, topic, i+1)
            optimization_tasks.append(task)

        if optimization_tasks:
            print(f"   🚀 并行优化 {len(optimization_tasks)} 个章节")

            # 分批并行执行
            batch_size = min(len(optimization_tasks), 5)  # 最多5个并发
            await self._execute_tasks_in_batches(optimization_tasks, batch_size, "参考学习优化")

    async def _apply_reference_learning_to_section(self, section: Dict[str, Any], learning_data: Dict[str, Any], topic: str, section_num: int):
        """将参考学习应用到单个章节"""
        title = section.get("title", f"章节{section_num}")
        content = section.get("content", "")

        prompt = f"""
基于参考报告的学习结果，优化以下章节：

章节标题：{title}
当前内容：
{content}

学习结果：
- 内容结构：{learning_data.get('content_structure', '')}
- 写作风格：{learning_data.get('writing_style', '')}
- 格式标准：{learning_data.get('format_standards', '')}
- 专业标准：{learning_data.get('professional_standards', '')}

请按照学习结果优化章节内容，保持原有信息的基础上提升质量。

请直接返回优化后的内容：
"""

        try:
            response = await self.generator.call_executor_model_async(prompt)
            if response:
                cleaned_content = self.generator._clean_model_response(response)
                final_content = self.generator._extract_final_content_only(cleaned_content)
                section["content"] = final_content
        except Exception as e:
            print(f"   ⚠️ 章节 {section_num} 参考学习应用失败: {str(e)}")

    # ==================== 工具方法 ====================

    def _calculate_optimal_batch_size(self, total_tasks: int) -> int:
        """计算最优批次大小"""
        # 根据API密钥数量和任务数量计算最优批次大小
        max_concurrent = getattr(self.api_manager, 'max_concurrent_requests', 10)
        
        if total_tasks <= max_concurrent:
            return total_tasks
        else:
            # 确保批次大小不超过最大并发数
            return min(max_concurrent, max(1, total_tasks // 4))

    async def _execute_tasks_in_batches(self, tasks: List, batch_size: int, task_name: str = "任务"):
        """分批执行任务"""
        try:
            from tqdm.asyncio import tqdm
        except ImportError:
            from tqdm import tqdm

        total_tasks = len(tasks)
        pbar = tqdm(total=total_tasks, desc=f"并行{task_name}", unit="任务")

        try:
            for i in range(0, total_tasks, batch_size):
                batch = tasks[i:i+batch_size]
                
                # 并行执行批次
                batch_results = await asyncio.gather(*batch, return_exceptions=True)
                
                # 更新进度条
                for result in batch_results:
                    pbar.update(1)
                    if isinstance(result, Exception):
                        print(f"   ⚠️ 任务执行失败: {str(result)}")

        finally:
            pbar.close()

    def _get_word_count_by_level(self, level: int) -> int:
        """根据层级获取建议字数"""
        word_counts = {
            1: 2000,  # 一级标题
            2: 1500,  # 二级标题
            3: 1000,  # 三级标题
            4: 800,   # 四级标题
            5: 600,   # 五级标题
            6: 400    # 六级标题
        }
        return word_counts.get(level, 800)
