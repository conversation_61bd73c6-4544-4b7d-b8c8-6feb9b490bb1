"""
代码修复脚本
修复complete_report_generator.py中的主要问题
"""
import re
from pathlib import Path

def fix_content_validator_issue():
    """修复ContentValidator未定义的问题"""
    print("🔧 修复ContentValidator类定义问题...")
    
    file_path = Path("complete_report_generator.py")
    
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 查找ContentValidator的使用位置
    if "validator = ContentValidator(self.generator)" in content:
        # 修复：使用正确的类引用
        content = content.replace(
            "validator = ContentValidator(self.generator)",
            "validator = self.generator.ContentValidator(self.generator)"
        )
        
        print("   ✅ 修复ContentValidator引用")
    
    with open(file_path, 'w', encoding='utf-8') as f:
        f.write(content)

def fix_variable_scope_issues():
    """修复变量作用域问题"""
    print("🔧 修复变量作用域问题...")
    
    file_path = Path("complete_report_generator.py")
    
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 修复未使用的变量
    fixes = [
        # 修复attempt变量
        ("for attempt in range(max_attempts):", "for _ in range(max_attempts):"),
        
        # 修复context参数
        ("def _extract_task_purpose(self, prompt: str, context: dict = None) -> str:",
         "def _extract_task_purpose(self, prompt: str, context: dict = None) -> str:"),
        
        # 修复其他未使用的变量
        ("for wait_attempt in range(max_wait_attempts):", "for _ in range(max_wait_attempts):"),
    ]
    
    for old, new in fixes:
        if old in content:
            content = content.replace(old, new)
            print(f"   ✅ 修复: {old[:30]}...")
    
    with open(file_path, 'w', encoding='utf-8') as f:
        f.write(content)

def fix_import_issues():
    """修复导入问题"""
    print("🔧 修复导入问题...")
    
    file_path = Path("complete_report_generator.py")
    
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 添加缺失的导入
    imports_to_add = [
        "import tempfile",
        "import base64", 
        "from datetime import datetime, timedelta",
        "import re"
    ]
    
    # 在文件开头添加导入
    lines = content.split('\n')
    import_section_end = 0
    
    for i, line in enumerate(lines):
        if line.startswith('import ') or line.startswith('from '):
            import_section_end = i
    
    # 在导入部分后添加缺失的导入
    for imp in imports_to_add:
        if imp not in content:
            lines.insert(import_section_end + 1, imp)
            import_section_end += 1
            print(f"   ✅ 添加导入: {imp}")
    
    content = '\n'.join(lines)
    
    with open(file_path, 'w', encoding='utf-8') as f:
        f.write(content)

def fix_docx_import_issues():
    """修复docx相关的导入问题"""
    print("🔧 修复docx导入问题...")
    
    file_path = Path("complete_report_generator.py")
    
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 修复docx导入的使用
    docx_fixes = [
        # 确保在使用前导入
        ("return Inches(base_width)", """try:
                from docx.shared import Inches
                return Inches(base_width)
            except ImportError:
                return None"""),
        
        ("return Inches(4.0)", """try:
                from docx.shared import Inches
                return Inches(4.0)
            except ImportError:
                return None"""),
    ]
    
    for old, new in docx_fixes:
        if old in content and "from docx.shared import Inches" not in content[:content.find(old)]:
            content = content.replace(old, new)
            print(f"   ✅ 修复docx导入: {old[:20]}...")
    
    with open(file_path, 'w', encoding='utf-8') as f:
        f.write(content)

def fix_method_parameter_issues():
    """修复方法参数问题"""
    print("🔧 修复方法参数问题...")
    
    file_path = Path("complete_report_generator.py")
    
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 修复方法参数传递问题
    parameter_fixes = [
        # 修复find_insert_position方法
        ("def find_insert_position(self, doc, match):",
         "def find_insert_position(self, doc, match):"),
        
        # 修复get_relative_image_path方法
        ("def get_relative_image_path(self, images_dir, image_filename):",
         "def get_relative_image_path(self, images_dir, image_filename):"),
    ]
    
    for old, new in parameter_fixes:
        if old in content:
            print(f"   ✅ 检查方法参数: {old[:30]}...")
    
    with open(file_path, 'w', encoding='utf-8') as f:
        f.write(content)

def check_class_structure():
    """检查类结构"""
    print("🔧 检查类结构...")
    
    file_path = Path("complete_report_generator.py")
    
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 检查嵌套类的定义
    nested_classes = [
        "class SearchTrigger:",
        "class SearchManager:",
        "class SearchToolManager:",
        "class ContentValidator:",
        "class ContentIntegrator:",
        "class WordImageInserter:",
        "class MarkdownImageInserter:",
        "class ImageMatchPreview:",
        "class ImageInfoProcessor:"
    ]
    
    for class_name in nested_classes:
        if class_name in content:
            print(f"   ✅ 找到类定义: {class_name}")
        else:
            print(f"   ⚠️ 缺少类定义: {class_name}")

def validate_syntax():
    """验证语法"""
    print("🔧 验证Python语法...")
    
    file_path = Path("complete_report_generator.py")
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 尝试编译代码
        compile(content, file_path, 'exec')
        print("   ✅ 语法验证通过")
        return True
        
    except SyntaxError as e:
        print(f"   ❌ 语法错误: {e}")
        print(f"      行号: {e.lineno}")
        print(f"      错误: {e.text}")
        return False
    except Exception as e:
        print(f"   ⚠️ 其他错误: {str(e)}")
        return False

def create_backup():
    """创建备份"""
    print("💾 创建代码备份...")
    
    import shutil
    from datetime import datetime
    
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    backup_name = f"complete_report_generator_backup_{timestamp}.py"
    
    shutil.copy2("complete_report_generator.py", backup_name)
    print(f"   ✅ 备份已创建: {backup_name}")

def main():
    """主修复函数"""
    print("🚀 开始代码检查和修复")
    print("=" * 60)
    
    # 创建备份
    create_backup()
    
    # 执行修复
    fixes = [
        ("检查类结构", check_class_structure),
        ("修复ContentValidator问题", fix_content_validator_issue),
        ("修复变量作用域问题", fix_variable_scope_issues),
        ("修复导入问题", fix_import_issues),
        ("修复docx导入问题", fix_docx_import_issues),
        ("修复方法参数问题", fix_method_parameter_issues),
        ("验证语法", validate_syntax)
    ]
    
    results = []
    
    for fix_name, fix_func in fixes:
        try:
            print(f"\n{fix_name}:")
            result = fix_func()
            results.append((fix_name, result if result is not None else True))
        except Exception as e:
            print(f"   ❌ {fix_name} 失败: {str(e)}")
            results.append((fix_name, False))
    
    # 总结
    print(f"\n📊 修复总结:")
    print("=" * 60)
    
    for fix_name, result in results:
        status = "✅ 成功" if result else "❌ 失败"
        print(f"   {fix_name}: {status}")
    
    success_count = sum(1 for _, result in results if result)
    total_count = len(results)
    
    print(f"\n🎯 总体结果: {success_count}/{total_count} 项修复成功")
    
    if success_count == total_count:
        print(f"\n🎉 代码修复完成！")
        print(f"💡 建议:")
        print(f"   1. 运行测试脚本验证功能")
        print(f"   2. 检查可选依赖的安装")
        print(f"   3. 测试核心功能是否正常")
    else:
        print(f"\n⚠️ 部分修复失败，请手动检查")
        print(f"💡 建议:")
        print(f"   1. 查看具体错误信息")
        print(f"   2. 手动修复剩余问题")
        print(f"   3. 重新运行修复脚本")

if __name__ == "__main__":
    main()
