"""
质量改进引擎模块
基于质量检查结果实现内容改进逻辑
"""
import time
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime

from .state import (
    ReportState, QualityIssue, ContentImprovement, QualityMetrics,
    QualityDimension, SeverityLevel
)
from .config import Config
from .logger import ProcessLogger
from .llm_client import LLMClient


class QualityImprovementEngine:
    """
    质量改进引擎
    基于质量检查结果的内容改进逻辑
    """
    
    def __init__(self, config: Config, llm_client: LLMClient, logger: ProcessLogger):
        self.config = config
        self.llm_client = llm_client
        self.logger = logger
    
    def apply_quality_improvements(self, state: ReportState) -> int:
        """
        应用质量改进
        
        Args:
            state: 报告状态
            
        Returns:
            应用的改进数量
        """
        self.logger.logger.info("开始应用质量改进")
        start_time = time.time()
        improvements_count = 0
        
        try:
            if not state.report_structure or "sections" not in state.report_structure:
                raise ValueError("报告结构未初始化")
            
            sections = state.report_structure["sections"]
            
            # 收集所有质量问题
            all_issues = self._collect_all_quality_issues(sections)
            
            # 按严重程度排序
            sorted_issues = self._sort_issues_by_priority(all_issues)
            
            self.logger.logger.info(f"发现 {len(sorted_issues)} 个质量问题")
            
            # 应用改进
            for issue_info in sorted_issues:
                issue, node = issue_info
                if self._apply_single_improvement(issue, node, state):
                    improvements_count += 1
            
            duration = time.time() - start_time
            self.logger.logger.info(
                f"质量改进完成，耗时: {duration:.2f}秒，"
                f"应用改进: {improvements_count} 个"
            )
            
        except Exception as e:
            self.logger.logger.error(f"质量改进失败: {str(e)}")
            state.add_error(f"质量改进引擎错误: {str(e)}", "quality_improvement")
            raise
        
        return improvements_count
    
    def _collect_all_quality_issues(
        self, 
        sections: List[Dict[str, Any]]
    ) -> List[Tuple[QualityIssue, Dict[str, Any]]]:
        """收集所有质量问题"""
        all_issues = []
        
        def collect_recursive(node: Dict[str, Any]):
            if "quality_issues" in node:
                for issue_dict in node["quality_issues"]:
                    if isinstance(issue_dict, dict):
                        issue = QualityIssue(**issue_dict)
                        all_issues.append((issue, node))
            
            if "children" in node:
                for child in node["children"]:
                    collect_recursive(child)
        
        for section in sections:
            collect_recursive(section)
        
        return all_issues
    
    def _sort_issues_by_priority(
        self, 
        issues: List[Tuple[QualityIssue, Dict[str, Any]]]
    ) -> List[Tuple[QualityIssue, Dict[str, Any]]]:
        """按优先级排序问题"""
        severity_order = {
            SeverityLevel.CRITICAL: 4,
            SeverityLevel.HIGH: 3,
            SeverityLevel.MEDIUM: 2,
            SeverityLevel.LOW: 1
        }
        
        return sorted(
            issues, 
            key=lambda x: severity_order.get(x[0].severity, 0), 
            reverse=True
        )
    
    def _apply_single_improvement(
        self, 
        issue: QualityIssue, 
        node: Dict[str, Any], 
        state: ReportState
    ) -> bool:
        """
        应用单个改进
        
        Args:
            issue: 质量问题
            node: 目标节点
            state: 报告状态
            
        Returns:
            是否成功应用改进
        """
        try:
            # 根据问题维度选择改进策略
            if issue.dimension == QualityDimension.COHERENCE:
                return self._improve_coherence(issue, node, state)
            elif issue.dimension == QualityDimension.CONSISTENCY:
                return self._improve_consistency(issue, node, state)
            elif issue.dimension == QualityDimension.ACCURACY:
                return self._improve_accuracy(issue, node, state)
            elif issue.dimension == QualityDimension.COMPLETENESS:
                return self._improve_completeness(issue, node, state)
            elif issue.dimension == QualityDimension.COMPREHENSIVENESS:
                return self._improve_comprehensiveness(issue, node, state)
            elif issue.dimension == QualityDimension.RIGOR:
                return self._improve_rigor(issue, node, state)
            else:
                self.logger.logger.warning(f"未知的质量维度: {issue.dimension}")
                return False
                
        except Exception as e:
            self.logger.logger.error(f"应用改进失败: {str(e)}")
            return False
    
    def _improve_coherence(
        self, 
        issue: QualityIssue, 
        node: Dict[str, Any], 
        state: ReportState
    ) -> bool:
        """改进连贯性"""
        original_content = node.get("content", "")
        if not original_content.strip():
            return False
        
        try:
            prompt = f"""
请改进以下内容的连贯性：

原始内容：
{original_content}

问题描述：{issue.description}
改进建议：{issue.suggestion}

要求：
1. 保持原有信息不变
2. 改进逻辑流畅性和语言连贯性
3. 保持所有引用标记[来源: xxx]不变
4. 确保段落间的自然衔接

请返回改进后的内容：
"""
            
            improved_content = self.llm_client.call_llm(
                prompt=prompt,
                model_type="executor",
                response_format="text"
            )
            
            # 应用改进
            improvement = ContentImprovement(
                target_location=issue.location,
                improvement_type="coherence",
                original_content=original_content,
                improved_content=improved_content.strip(),
                improvement_reason=f"连贯性改进: {issue.description}"
            )
            
            self._apply_improvement_to_node(improvement, node)
            
            self.logger.logger.debug(f"连贯性改进应用成功: {node.get('title', 'Unknown')}")
            return True
            
        except Exception as e:
            self.logger.logger.error(f"连贯性改进失败: {str(e)}")
            return False
    
    def _improve_consistency(
        self, 
        issue: QualityIssue, 
        node: Dict[str, Any], 
        state: ReportState
    ) -> bool:
        """改进一致性"""
        original_content = node.get("content", "")
        if not original_content.strip():
            return False
        
        try:
            # 获取相关上下文（父节点、同级节点等）
            context_content = self._get_consistency_context(node, state)
            
            prompt = f"""
请改进以下内容的一致性：

原始内容：
{original_content}

相关上下文：
{context_content}

问题描述：{issue.description}
改进建议：{issue.suggestion}

要求：
1. 确保与上下文内容保持逻辑一致
2. 统一术语和表述风格
3. 保持所有引用标记[来源: xxx]不变
4. 消除矛盾和不一致之处

请返回改进后的内容：
"""
            
            improved_content = self.llm_client.call_llm(
                prompt=prompt,
                model_type="executor",
                response_format="text"
            )
            
            # 应用改进
            improvement = ContentImprovement(
                target_location=issue.location,
                improvement_type="consistency",
                original_content=original_content,
                improved_content=improved_content.strip(),
                improvement_reason=f"一致性改进: {issue.description}"
            )
            
            self._apply_improvement_to_node(improvement, node)
            
            self.logger.logger.debug(f"一致性改进应用成功: {node.get('title', 'Unknown')}")
            return True
            
        except Exception as e:
            self.logger.logger.error(f"一致性改进失败: {str(e)}")
            return False
    
    def _improve_accuracy(
        self, 
        issue: QualityIssue, 
        node: Dict[str, Any], 
        state: ReportState
    ) -> bool:
        """改进准确性"""
        original_content = node.get("content", "")
        if not original_content.strip():
            return False
        
        try:
            prompt = f"""
请改进以下内容的准确性：

原始内容：
{original_content}

问题描述：{issue.description}
改进建议：{issue.suggestion}

要求：
1. 确保事实准确性
2. 添加或修正数据引用
3. 使用格式[来源: 文件名]标注引用
4. 消除可能的事实错误

请返回改进后的内容：
"""
            
            improved_content = self.llm_client.call_llm(
                prompt=prompt,
                model_type="executor",
                response_format="text"
            )
            
            # 应用改进
            improvement = ContentImprovement(
                target_location=issue.location,
                improvement_type="accuracy",
                original_content=original_content,
                improved_content=improved_content.strip(),
                improvement_reason=f"准确性改进: {issue.description}"
            )
            
            self._apply_improvement_to_node(improvement, node)
            
            self.logger.logger.debug(f"准确性改进应用成功: {node.get('title', 'Unknown')}")
            return True
            
        except Exception as e:
            self.logger.logger.error(f"准确性改进失败: {str(e)}")
            return False
    
    def _improve_completeness(
        self, 
        issue: QualityIssue, 
        node: Dict[str, Any], 
        state: ReportState
    ) -> bool:
        """改进完整性"""
        original_content = node.get("content", "")
        
        try:
            prompt = f"""
请改进以下内容的完整性：

原始内容：
{original_content}

章节标题：{node.get('title', '')}
问题描述：{issue.description}
改进建议：{issue.suggestion}

要求：
1. 补充缺失的关键信息
2. 确保内容充实完整
3. 保持逻辑结构清晰
4. 添加必要的数据支撑

请返回改进后的内容：
"""
            
            improved_content = self.llm_client.call_llm(
                prompt=prompt,
                model_type="executor",
                response_format="text"
            )
            
            # 应用改进
            improvement = ContentImprovement(
                target_location=issue.location,
                improvement_type="completeness",
                original_content=original_content,
                improved_content=improved_content.strip(),
                improvement_reason=f"完整性改进: {issue.description}"
            )
            
            self._apply_improvement_to_node(improvement, node)
            
            self.logger.logger.debug(f"完整性改进应用成功: {node.get('title', 'Unknown')}")
            return True
            
        except Exception as e:
            self.logger.logger.error(f"完整性改进失败: {str(e)}")
            return False
    
    def _improve_comprehensiveness(
        self, 
        issue: QualityIssue, 
        node: Dict[str, Any], 
        state: ReportState
    ) -> bool:
        """改进全面性"""
        return self._improve_completeness(issue, node, state)  # 使用相同的改进策略
    
    def _improve_rigor(
        self, 
        issue: QualityIssue, 
        node: Dict[str, Any], 
        state: ReportState
    ) -> bool:
        """改进严谨性"""
        original_content = node.get("content", "")
        if not original_content.strip():
            return False
        
        try:
            prompt = f"""
请改进以下内容的严谨性：

原始内容：
{original_content}

问题描述：{issue.description}
改进建议：{issue.suggestion}

要求：
1. 使用更专业的术语和表述
2. 增强逻辑论证的严密性
3. 添加数据支撑和引用
4. 保持客观中性的语调

请返回改进后的内容：
"""
            
            improved_content = self.llm_client.call_llm(
                prompt=prompt,
                model_type="executor",
                response_format="text"
            )
            
            # 应用改进
            improvement = ContentImprovement(
                target_location=issue.location,
                improvement_type="rigor",
                original_content=original_content,
                improved_content=improved_content.strip(),
                improvement_reason=f"严谨性改进: {issue.description}"
            )
            
            self._apply_improvement_to_node(improvement, node)
            
            self.logger.logger.debug(f"严谨性改进应用成功: {node.get('title', 'Unknown')}")
            return True
            
        except Exception as e:
            self.logger.logger.error(f"严谨性改进失败: {str(e)}")
            return False
    
    def _get_consistency_context(self, node: Dict[str, Any], state: ReportState) -> str:
        """获取一致性检查的上下文"""
        # 这里应该实现获取父节点、同级节点等相关内容的逻辑
        # 暂时返回空字符串
        return ""
    
    def _apply_improvement_to_node(self, improvement: ContentImprovement, node: Dict[str, Any]):
        """将改进应用到节点"""
        # 保存原始内容到历史
        if "iteration_history" not in node:
            node["iteration_history"] = []
        node["iteration_history"].append(node.get("content", ""))
        
        # 应用改进后的内容
        node["content"] = improvement.improved_content
        
        # 记录改进信息
        if "improvements" not in node:
            node["improvements"] = []
        node["improvements"].append(improvement.dict())


class QualityScoreCalculator:
    """
    质量评分系统
    计算整体质量分数和质量改进量化评估
    """
    
    def __init__(self, config: Config, logger: ProcessLogger):
        self.config = config
        self.logger = logger
        self.quality_weights = {
            "coherence": 0.2,
            "consistency": 0.2,
            "accuracy": 0.2,
            "completeness": 0.15,
            "comprehensiveness": 0.15,
            "rigor": 0.1
        }
    
    def calculate_overall_quality_score(self, state: ReportState) -> float:
        """
        计算整体质量分数
        
        Args:
            state: 报告状态
            
        Returns:
            整体质量分数 (0.0-1.0)
        """
        if not state.report_structure or "sections" not in state.report_structure:
            return 0.0
        
        total_score = 0.0
        node_count = 0
        
        def calculate_recursive(node: Dict[str, Any]):
            nonlocal total_score, node_count
            
            if "quality_metrics" in node:
                metrics = node["quality_metrics"]
                if isinstance(metrics, dict):
                    # 计算加权分数
                    weighted_score = (
                        metrics.get("coherence_score", 0.0) * self.quality_weights["coherence"] +
                        metrics.get("consistency_score", 0.0) * self.quality_weights["consistency"] +
                        metrics.get("accuracy_score", 0.0) * self.quality_weights["accuracy"] +
                        metrics.get("completeness_score", 0.0) * self.quality_weights["completeness"] +
                        metrics.get("comprehensiveness_score", 0.0) * self.quality_weights["comprehensiveness"] +
                        metrics.get("rigor_score", 0.0) * self.quality_weights["rigor"]
                    )
                    total_score += weighted_score
                    node_count += 1
            
            if "children" in node:
                for child in node["children"]:
                    calculate_recursive(child)
        
        sections = state.report_structure["sections"]
        for section in sections:
            calculate_recursive(section)
        
        overall_score = total_score / node_count if node_count > 0 else 0.0
        
        self.logger.logger.info(
            f"整体质量分数计算完成: {overall_score:.3f} "
            f"(基于 {node_count} 个节点)"
        )
        
        return overall_score
    
    def track_quality_improvement(
        self, 
        before_score: float, 
        after_score: float
    ) -> Dict[str, float]:
        """
        跟踪质量改进
        
        Args:
            before_score: 改进前分数
            after_score: 改进后分数
            
        Returns:
            改进统计信息
        """
        improvement = after_score - before_score
        improvement_percentage = (improvement / before_score * 100) if before_score > 0 else 0.0
        
        improvement_stats = {
            "before_score": before_score,
            "after_score": after_score,
            "absolute_improvement": improvement,
            "percentage_improvement": improvement_percentage,
            "is_improved": improvement > 0
        }
        
        self.logger.logger.info(
            f"质量改进跟踪: {before_score:.3f} -> {after_score:.3f} "
            f"(改进: {improvement:+.3f}, {improvement_percentage:+.1f}%)"
        )
        
        return improvement_stats
