"""
搜索处理器模块 - 严格按照原始代码的搜索功能实现
包含Google、Bing和Metaso搜索功能
"""

import requests
import time
from typing import List, Dict, Any, Optional
from urllib.parse import quote

from ..config import (
    GOOGLE_API_KEY, GOOGLE_CSE_ID,
    BING_SUBSCRIPTION_KEY,
    METASO_API_KEY,
    MAX_SEARCH_RESULTS,
    API_RATE_LIMITS
)


class SearchHandler:
    """搜索处理器 - 与原始代码完全相同的实现"""
    
    def __init__(self):
        """初始化搜索处理器"""
        self.google_api_key = GOOGLE_API_KEY
        self.google_cse_id = GOOGLE_CSE_ID
        self.bing_key = BING_SUBSCRIPTION_KEY
        self.metaso_key = METASO_API_KEY
        
        # 记录API调用时间
        self.last_call_time = {
            'google': 0,
            'bing': 0,
            'metaso': 0
        }
    
    def search(self, query: str, engine: str = "google", num_results: int = 10) -> List[Dict[str, Any]]:
        """
        执行搜索 - 与原始代码完全相同
        
        Args:
            query: 搜索查询
            engine: 搜索引擎（google/bing/metaso）
            num_results: 结果数量
            
        Returns:
            搜索结果列表
        """
        # 限制结果数量
        num_results = min(num_results, MAX_SEARCH_RESULTS)
        
        # 速率限制
        self._rate_limit(engine)
        
        try:
            if engine == "google":
                return self._search_google(query, num_results)
            elif engine == "bing":
                return self._search_bing(query, num_results)
            elif engine == "metaso":
                return self._search_metaso(query, num_results)
            else:
                print(f"❌ 不支持的搜索引擎: {engine}")
                return []
        except Exception as e:
            print(f"❌ 搜索失败 ({engine}): {str(e)}")
            return []
    
    def _rate_limit(self, engine: str):
        """执行速率限制 - 与原始代码完全相同"""
        current_time = time.time()
        last_call = self.last_call_time.get(engine, 0)
        
        # 计算需要等待的时间
        rate_limit = API_RATE_LIMITS.get(engine, 60)
        min_interval = 60.0 / rate_limit  # 转换为秒
        
        time_passed = current_time - last_call
        if time_passed < min_interval:
            wait_time = min_interval - time_passed
            print(f"   ⏳ 速率限制，等待 {wait_time:.1f} 秒...")
            time.sleep(wait_time)
        
        self.last_call_time[engine] = time.time()
    
    def _search_google(self, query: str, num_results: int) -> List[Dict[str, Any]]:
        """
        使用Google Custom Search API搜索 - 与原始代码完全相同
        
        Args:
            query: 搜索查询
            num_results: 结果数量
            
        Returns:
            搜索结果列表
        """
        if not self.google_api_key or not self.google_cse_id:
            print("❌ Google API密钥或CSE ID未配置")
            return []
        
        results = []
        
        # Google CSE每次最多返回10个结果
        for start in range(0, num_results, 10):
            url = "https://www.googleapis.com/customsearch/v1"
            params = {
                'key': self.google_api_key,
                'cx': self.google_cse_id,
                'q': query,
                'start': start + 1,
                'num': min(10, num_results - start)
            }
            
            try:
                response = requests.get(url, params=params, timeout=10)
                
                if response.status_code == 200:
                    data = response.json()
                    
                    if 'items' in data:
                        for item in data['items']:
                            result = {
                                'title': item.get('title', ''),
                                'link': item.get('link', ''),
                                'snippet': item.get('snippet', ''),
                                'source': 'google'
                            }
                            results.append(result)
                else:
                    print(f"❌ Google搜索失败: {response.status_code}")
                    break
                    
            except Exception as e:
                print(f"❌ Google搜索请求失败: {str(e)}")
                break
        
        return results[:num_results]
    
    def _search_bing(self, query: str, num_results: int) -> List[Dict[str, Any]]:
        """
        使用Bing Search API搜索 - 与原始代码完全相同
        
        Args:
            query: 搜索查询
            num_results: 结果数量
            
        Returns:
            搜索结果列表
        """
        if not self.bing_key:
            print("❌ Bing API密钥未配置")
            return []
        
        url = "https://api.bing.microsoft.com/v7.0/search"
        headers = {
            'Ocp-Apim-Subscription-Key': self.bing_key
        }
        params = {
            'q': query,
            'count': num_results,
            'textDecorations': True,
            'textFormat': 'HTML'
        }
        
        try:
            response = requests.get(url, headers=headers, params=params, timeout=10)
            
            if response.status_code == 200:
                data = response.json()
                results = []
                
                if 'webPages' in data and 'value' in data['webPages']:
                    for item in data['webPages']['value']:
                        result = {
                            'title': item.get('name', ''),
                            'link': item.get('url', ''),
                            'snippet': item.get('snippet', ''),
                            'source': 'bing'
                        }
                        results.append(result)
                
                return results[:num_results]
            else:
                print(f"❌ Bing搜索失败: {response.status_code}")
                return []
                
        except Exception as e:
            print(f"❌ Bing搜索请求失败: {str(e)}")
            return []
    
    def _search_metaso(self, query: str, num_results: int) -> List[Dict[str, Any]]:
        """
        使用Metaso API搜索 - 与原始代码完全相同
        
        Args:
            query: 搜索查询
            num_results: 结果数量
            
        Returns:
            搜索结果列表
        """
        if not self.metaso_key:
            print("❌ Metaso API密钥未配置")
            return []
        
        url = "https://api.metaso.cn/v1/search"
        headers = {
            'Authorization': f'Bearer {self.metaso_key}',
            'Content-Type': 'application/json'
        }
        data = {
            'query': query,
            'count': num_results
        }
        
        try:
            response = requests.post(url, headers=headers, json=data, timeout=10)
            
            if response.status_code == 200:
                result_data = response.json()
                results = []
                
                if 'results' in result_data:
                    for item in result_data['results']:
                        result = {
                            'title': item.get('title', ''),
                            'link': item.get('url', ''),
                            'snippet': item.get('description', ''),
                            'source': 'metaso'
                        }
                        results.append(result)
                
                return results[:num_results]
            else:
                print(f"❌ Metaso搜索失败: {response.status_code}")
                return []
                
        except Exception as e:
            print(f"❌ Metaso搜索请求失败: {str(e)}")
            return []
    
    def multi_search(self, query: str, engines: List[str] = None, num_results_per_engine: int = 5) -> List[Dict[str, Any]]:
        """
        使用多个搜索引擎搜索并合并结果 - 与原始代码完全相同
        
        Args:
            query: 搜索查询
            engines: 要使用的搜索引擎列表
            num_results_per_engine: 每个引擎的结果数量
            
        Returns:
            合并后的搜索结果列表
        """
        if engines is None:
            # 使用所有可用的搜索引擎
            engines = []
            if self.google_api_key and self.google_cse_id:
                engines.append('google')
            if self.bing_key:
                engines.append('bing')
            if self.metaso_key:
                engines.append('metaso')
        
        all_results = []
        
        for engine in engines:
            print(f"   🔍 使用 {engine} 搜索: {query}")
            results = self.search(query, engine, num_results_per_engine)
            all_results.extend(results)
            print(f"   ✅ {engine} 返回 {len(results)} 个结果")
        
        # 去重（基于URL）
        seen_urls = set()
        unique_results = []
        
        for result in all_results:
            url = result.get('link', '')
            if url and url not in seen_urls:
                seen_urls.add(url)
                unique_results.append(result)
        
        return unique_results
    
    def search_with_fallback(self, query: str, preferred_engine: str = "google", num_results: int = 10) -> List[Dict[str, Any]]:
        """
        带回退的搜索 - 与原始代码完全相同
        如果首选引擎失败，尝试其他引擎
        
        Args:
            query: 搜索查询
            preferred_engine: 首选搜索引擎
            num_results: 结果数量
            
        Returns:
            搜索结果列表
        """
        # 定义引擎优先级
        engine_priority = [preferred_engine]
        
        # 添加其他可用引擎作为备选
        all_engines = ['google', 'bing', 'metaso']
        for engine in all_engines:
            if engine != preferred_engine:
                engine_priority.append(engine)
        
        # 依次尝试每个引擎
        for engine in engine_priority:
            print(f"   🔍 尝试使用 {engine} 搜索...")
            results = self.search(query, engine, num_results)
            
            if results:
                print(f"   ✅ {engine} 搜索成功，返回 {len(results)} 个结果")
                return results
            else:
                print(f"   ⚠️ {engine} 搜索失败，尝试下一个引擎...")
        
        print(f"   ❌ 所有搜索引擎都失败了")
        return []
