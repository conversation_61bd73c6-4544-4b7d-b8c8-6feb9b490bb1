#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
内容验证器模块
验证搜索结果的质量和相关性
"""

from typing import List, Dict, Any


class ContentValidator:
    """内容验证器"""
    
    def __init__(self, generator):
        self.generator = generator
    
    def validate_search_results(self, 
                               results: List[Dict[str, Any]], 
                               topic: str,
                               content_gap: Dict[str, Any]) -> List[Dict[str, Any]]:
        """验证搜索结果"""
        validated_results = []
        
        for result in results:
            if self._is_relevant(result, topic, content_gap):
                score = self._calculate_relevance_score(result, topic, content_gap)
                result['relevance_score'] = score
                validated_results.append(result)
        
        # 按相关性评分排序
        validated_results.sort(key=lambda x: x.get('relevance_score', 0), reverse=True)
        
        return validated_results
    
    def _is_relevant(self, 
                    result: Dict[str, Any], 
                    topic: str, 
                    content_gap: Dict[str, Any]) -> bool:
        """检查结果是否相关"""
        title = result.get('title', '').lower()
        snippet = result.get('snippet', '').lower()
        content = f"{title} {snippet}"
        
        # 检查主题相关性
        topic_keywords = topic.lower().split()
        topic_matches = sum(1 for keyword in topic_keywords if keyword in content)
        
        if topic_matches == 0:
            return False
        
        # 检查内容缺口相关性
        gap_type = content_gap.get('type', '')
        
        if gap_type == 'latest_data':
            return self._check_latest_data_relevance(content)
        elif gap_type == 'market_data':
            return self._check_market_data_relevance(content)
        elif gap_type == 'technology':
            return self._check_technology_relevance(content)
        elif gap_type == 'policy':
            return self._check_policy_relevance(content)
        elif gap_type == 'cases':
            return self._check_cases_relevance(content)
        
        return True
    
    def _check_latest_data_relevance(self, content: str) -> bool:
        """检查最新数据相关性"""
        data_keywords = ['2024', '2025', '最新', '数据', '报告', '统计']
        return any(keyword in content for keyword in data_keywords)
    
    def _check_market_data_relevance(self, content: str) -> bool:
        """检查市场数据相关性"""
        market_keywords = ['市场', '规模', '份额', '竞争', '增长', '预测']
        return any(keyword in content for keyword in market_keywords)
    
    def _check_technology_relevance(self, content: str) -> bool:
        """检查技术相关性"""
        tech_keywords = ['技术', '创新', '发展', '突破', '趋势', '应用']
        return any(keyword in content for keyword in tech_keywords)
    
    def _check_policy_relevance(self, content: str) -> bool:
        """检查政策相关性"""
        policy_keywords = ['政策', '法规', '标准', '监管', '规范', '指导']
        return any(keyword in content for keyword in policy_keywords)
    
    def _check_cases_relevance(self, content: str) -> bool:
        """检查案例相关性"""
        case_keywords = ['案例', '项目', '应用', '实践', '成功', '示例']
        return any(keyword in content for keyword in case_keywords)
    
    def _calculate_relevance_score(self, 
                                  result: Dict[str, Any], 
                                  topic: str, 
                                  content_gap: Dict[str, Any]) -> float:
        """计算相关性评分"""
        title = result.get('title', '').lower()
        snippet = result.get('snippet', '').lower()
        content = f"{title} {snippet}"
        
        score = 0.0
        
        # 主题匹配评分
        topic_keywords = topic.lower().split()
        topic_matches = sum(1 for keyword in topic_keywords if keyword in content)
        score += topic_matches * 0.3
        
        # 标题匹配加分
        title_matches = sum(1 for keyword in topic_keywords if keyword in title)
        score += title_matches * 0.2
        
        # 来源权重
        source = result.get('source', '')
        if source.startswith('metaso'):
            score += 0.3
        elif source == 'google':
            score += 0.2
        elif source == 'bing':
            score += 0.1
        
        # 内容长度评分
        content_length = len(snippet)
        if content_length > 100:
            score += 0.1
        
        # 时效性评分
        date = result.get('date', '')
        if '2024' in date or '2025' in date:
            score += 0.2
        
        return score
    
    def filter_by_quality(self, 
                         results: List[Dict[str, Any]], 
                         min_score: float = 0.5) -> List[Dict[str, Any]]:
        """按质量过滤结果"""
        return [result for result in results 
                if result.get('relevance_score', 0) >= min_score]
