#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
搜索管理器模块
管理多个搜索API，提供统一的搜索接口
"""

import os
import json
import http.client
from typing import List, Dict, Any

try:
    from ..core.config import get_environment_config
except ImportError:
    from core.config import get_environment_config


class SearchManager:
    """搜索API管理器"""

    def __init__(self, generator):
        self.generator = generator
        self.search_apis = {}
        self.init_search_apis()

    def init_search_apis(self):
        """初始化搜索API"""
        env_config = get_environment_config()
        
        # Metaso Search API (优先使用)
        try:
            metaso_api_key = env_config.get('METASO_API_KEY')
            if metaso_api_key:
                self.search_apis['metaso'] = {
                    'api_key': metaso_api_key,
                    'enabled': True
                }
                print("✅ Metaso Search API 已配置")
            else:
                print("⚠️ Metaso Search API 未配置")
        except Exception as e:
            print(f"⚠️ Metaso Search API 配置失败: {str(e)}")

        # Google Custom Search API
        try:
            google_api_key = env_config.get('GOOGLE_SEARCH_API_KEY')
            google_cx = env_config.get('GOOGLE_SEARCH_CX')
            if google_api_key and google_cx:
                self.search_apis['google'] = {
                    'api_key': google_api_key,
                    'cx': google_cx,
                    'enabled': True
                }
                print("✅ Google Search API 已配置")
            else:
                print("⚠️ Google Search API 未配置")
        except Exception as e:
            print(f"⚠️ Google Search API 配置失败: {str(e)}")

        # Bing Search API
        try:
            bing_api_key = env_config.get('BING_SEARCH_API_KEY')
            if bing_api_key:
                self.search_apis['bing'] = {
                    'api_key': bing_api_key,
                    'enabled': True
                }
                print("✅ Bing Search API 已配置")
            else:
                print("⚠️ Bing Search API 未配置")
        except Exception as e:
            print(f"⚠️ Bing Search API 配置失败: {str(e)}")

    def search_google(self, query: str, num_results: int = 5) -> List[Dict[str, Any]]:
        """使用Google Custom Search API搜索"""
        if 'google' not in self.search_apis or not self.search_apis['google']['enabled']:
            return []

        try:
            import requests

            api_key = self.search_apis['google']['api_key']
            cx = self.search_apis['google']['cx']

            url = "https://www.googleapis.com/customsearch/v1"
            params = {
                'key': api_key,
                'cx': cx,
                'q': query,
                'num': min(num_results, 10),
                'dateRestrict': 'y1'  # 限制在一年内的结果
            }

            response = requests.get(url, params=params, timeout=10)
            response.raise_for_status()

            data = response.json()
            results = []

            for item in data.get('items', []):
                results.append({
                    'title': item.get('title', ''),
                    'url': item.get('link', ''),
                    'snippet': item.get('snippet', ''),
                    'source': 'google',
                    'date': item.get('pagemap', {}).get('metatags', [{}])[0].get('article:published_time', '')
                })

            return results

        except Exception as e:
            print(f"⚠️ Google搜索失败: {str(e)}")
            return []

    def search_bing(self, query: str, num_results: int = 5) -> List[Dict[str, Any]]:
        """使用Bing Search API搜索"""
        if 'bing' not in self.search_apis or not self.search_apis['bing']['enabled']:
            return []

        try:
            import requests

            api_key = self.search_apis['bing']['api_key']

            url = "https://api.bing.microsoft.com/v7.0/search"
            headers = {
                'Ocp-Apim-Subscription-Key': api_key
            }
            params = {
                'q': query,
                'count': min(num_results, 10),
                'freshness': 'Year'  # 限制在一年内的结果
            }

            response = requests.get(url, headers=headers, params=params, timeout=10)
            response.raise_for_status()

            data = response.json()
            results = []

            for item in data.get('webPages', {}).get('value', []):
                results.append({
                    'title': item.get('name', ''),
                    'url': item.get('url', ''),
                    'snippet': item.get('snippet', ''),
                    'source': 'bing',
                    'date': item.get('dateLastCrawled', '')
                })

            return results

        except Exception as e:
            print(f"⚠️ Bing搜索失败: {str(e)}")
            return []

    def search_metaso(self, query: str, scope: str = 'webpage', num_results: int = 5) -> List[Dict[str, Any]]:
        """使用Metaso Search API搜索"""
        if 'metaso' not in self.search_apis or not self.search_apis['metaso']['enabled']:
            return []

        try:
            api_key = self.search_apis['metaso']['api_key']

            conn = http.client.HTTPSConnection("metaso.cn")
            payload = json.dumps({
                "q": f"{query}",  # 动态查询内容
                "scope": scope,  # 'webpage' 或 'scholar'
                "includeSummary": True,
                "size": str(min(num_results, 10))
            })
            headers = {
                'Authorization': f'Bearer {api_key}',
                'Accept': 'application/json',
                'Content-Type': 'application/json'
            }

            conn.request("POST", "/api/v1/search", payload, headers)
            res = conn.getresponse()
            data = res.read()

            if res.status == 200:
                response_data = json.loads(data.decode("utf-8"))
                results = []

                # 解析Metaso API响应
                items = response_data.get('data', {}).get('results', [])

                for item in items:
                    results.append({
                        'title': item.get('title', ''),
                        'url': item.get('url', ''),
                        'snippet': item.get('summary', '') or item.get('content', ''),
                        'source': f'metaso_{scope}',
                        'date': item.get('publishedDate', ''),
                        'score': item.get('score', 0)
                    })

                return results
            else:
                print(f"⚠️ Metaso搜索失败: HTTP {res.status}")
                return []

        except Exception as e:
            print(f"⚠️ Metaso搜索失败: {str(e)}")
            return []

    def multi_source_search(self, query: str, search_types: List[str] = None, num_results: int = 5) -> List[Dict[str, Any]]:
        """多源搜索"""
        if search_types is None:
            search_types = ['metaso']
            
        all_results = []

        for source in search_types:
            if source == 'metaso':
                # 同时搜索网页和学术内容
                webpage_results = self.search_metaso(query, 'webpage', num_results//2)
                scholar_results = self.search_metaso(query, 'scholar', num_results//2)
                results = webpage_results + scholar_results
            elif source == 'google':
                results = self.search_google(query, num_results)
            elif source == 'bing':
                results = self.search_bing(query, num_results)
            else:
                continue

            all_results.extend(results)

        # 去重和排序
        return self.merge_and_rank_results(all_results)

    def merge_and_rank_results(self, results: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """合并和排序搜索结果"""
        # 简单去重（基于URL）
        seen_urls = set()
        unique_results = []

        for result in results:
            url = result.get('url', '')
            if url and url not in seen_urls:
                seen_urls.add(url)
                unique_results.append(result)

        # 按来源权重排序（Metaso > Google > Bing）
        def sort_key(result):
            source = result.get('source', '')
            if source.startswith('metaso'):
                weight = 3  # Metaso最高权重
            elif source == 'google':
                weight = 2
            elif source == 'bing':
                weight = 1
            else:
                weight = 0

            # 结合评分（如果有的话）
            score = result.get('score', 0)
            return weight + score * 0.1

        unique_results.sort(key=sort_key, reverse=True)
        return unique_results

    def generate_search_queries(self, topic: str, content_gap: Dict[str, Any]) -> List[str]:
        """生成优化的搜索查询"""
        base_queries = []

        if content_gap['type'] == 'latest_data':
            base_queries = [
                f"{topic} 2024年最新数据",
                f"{topic} 市场报告 2024",
                f"{topic} 行业分析 最新"
            ]
        elif content_gap['type'] == 'market_data':
            base_queries = [
                f"{topic} 市场规模",
                f"{topic} 竞争格局 主要企业",
                f"{topic} 投资 融资 数据"
            ]
        elif content_gap['type'] == 'technology':
            base_queries = [
                f"{topic} 技术发展 2024",
                f"{topic} 创新 突破",
                f"{topic} 技术趋势"
            ]
        elif content_gap['type'] == 'policy':
            base_queries = [
                f"{topic} 政策 法规",
                f"{topic} 标准 规范",
                f"{topic} 监管 指导"
            ]
        elif content_gap['type'] == 'cases':
            base_queries = [
                f"{topic} 成功案例",
                f"{topic} 项目 应用",
                f"{topic} 实践 经验"
            ]
        else:
            base_queries = [content_gap.get('query', topic)]

        return base_queries


class SearchToolManager:
    """搜索工具管理器 - 支持Gemini工具调用"""

    def __init__(self, generator):
        self.generator = generator
        self.search_manager = SearchManager(generator)

    def get_search_tools_definition(self):
        """获取搜索工具的定义，供Gemini使用"""
        tools = [
            {
                "name": "search_web_content",
                "description": "搜索最新的网页内容，获取实时信息、新闻、市场数据等",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "query": {
                            "type": "string",
                            "description": "搜索查询词"
                        },
                        "num_results": {
                            "type": "integer",
                            "description": "返回结果数量，默认5个",
                            "default": 5
                        }
                    },
                    "required": ["query"]
                }
            },
            {
                "name": "search_academic_papers",
                "description": "搜索学术论文和研究报告",
                "parameters": {
                    "type": "object",
                    "properties": {
                        "query": {
                            "type": "string",
                            "description": "学术搜索查询词"
                        },
                        "num_results": {
                            "type": "integer",
                            "description": "返回结果数量，默认5个",
                            "default": 5
                        }
                    },
                    "required": ["query"]
                }
            }
        ]
        return tools

    def execute_tool_call(self, tool_name: str, parameters: dict):
        """执行工具调用"""
        try:
            if tool_name == "search_web_content":
                return self._search_web_content(**parameters)
            elif tool_name == "search_academic_papers":
                return self._search_academic_papers(**parameters)
            else:
                return {"error": f"未知的工具: {tool_name}"}
        except Exception as e:
            return {"error": f"工具执行失败: {str(e)}"}

    def _search_web_content(self, query: str, num_results: int = 5):
        """搜索网页内容"""
        try:
            results = self.search_manager.search_metaso(query, 'webpage', num_results)

            formatted_results = []
            for result in results:
                formatted_results.append({
                    "title": result.get('title', ''),
                    "url": result.get('url', ''),
                    "snippet": result.get('snippet', ''),
                    "source": result.get('source', ''),
                    "date": result.get('date', '')
                })

            return {
                "success": True,
                "query": query,
                "results": formatted_results
            }
        except Exception as e:
            return {"error": f"网页搜索失败: {str(e)}"}

    def _search_academic_papers(self, query: str, num_results: int = 5):
        """搜索学术论文"""
        try:
            results = self.search_manager.search_metaso(query, 'scholar', num_results)

            formatted_results = []
            for result in results:
                formatted_results.append({
                    "title": result.get('title', ''),
                    "url": result.get('url', ''),
                    "snippet": result.get('snippet', ''),
                    "source": result.get('source', ''),
                    "date": result.get('date', '')
                })

            return {
                "success": True,
                "query": query,
                "results": formatted_results
            }
        except Exception as e:
            return {"error": f"学术搜索失败: {str(e)}"}
