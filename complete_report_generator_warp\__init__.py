"""
AI Report Generator Module - Optimized and Refactored Version
Strictly follows the original code logic while improving organization
"""

from .core.generator import CompleteReportGenerator
from .utils.directories import create_directories, create_framework_file
from .utils.sample_data import create_sample_data
from .utils.user_interface import get_user_inputs

__version__ = "1.0.0"
__all__ = [
    "CompleteReportGenerator",
    "create_directories",
    "create_framework_file", 
    "create_sample_data",
    "get_user_inputs"
]
