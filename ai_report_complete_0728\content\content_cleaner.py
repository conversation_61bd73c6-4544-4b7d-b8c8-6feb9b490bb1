#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
内容清理模块
清理AI生成内容中的废话、思考过程和优化对比
"""

import re
from typing import List


class ContentCleaner:
    """内容清理器 - 清理AI生成内容中的无用信息"""

    def __init__(self):
        self.patterns_to_remove = self._get_removal_patterns()
        self.skip_patterns = self._get_skip_patterns()

    def _get_removal_patterns(self) -> List[str]:
        """获取需要移除的模式列表"""
        return [
            # 基础废话模式
            r"好的，遵照您的要求.*?(?=\n\n|\n#|\n\*|$)",
            r"作为.*?模型.*?(?=\n\n|\n#|\n\*|$)",
            r"您提供的.*?方案.*?(?=\n\n|\n#|\n\*|$)",
            r"以下是.*?确认.*?版本.*?(?=\n\n|\n#|\n\*|$)",
            r"我已.*?进行了.*?检查.*?(?=\n\n|\n#|\n\*|$)",
            r"该方案.*?成功.*?(?=\n\n|\n#|\n\*|$)",
            r"此版本.*?采纳.*?(?=\n\n|\n#|\n\*|$)",
            r"【.*?】✓.*?已实现.*?(?=\n\n|\n#|\n\*|$)",
            r"最终优化版本.*?确认稿.*?(?=\n\n|\n#|\n\*|$)",
            r"经我最终确认.*?(?=\n\n|\n#|\n\*|$)",
            r"---\s*\n\n",
            r"根据统筹模型的安排.*?(?=\n\n|\n#|\n\*|$)",
            r"基于您的优化方案.*?(?=\n\n|\n#|\n\*|$)",

            # 优化过程相关废话
            r"优化前版本：.*?(?=优化后版本：|$)",
            r"原始版本：.*?(?=优化版本：|$)",
            r"修改前：.*?(?=修改后：|$)",
            r"调整前：.*?(?=调整后：|$)",
            r"第.*?轮优化.*?(?=\n\n|\n#|\n\*|$)",
            r"优化说明：.*?(?=\n\n|\n#|\n\*|$)",
            r"修改说明：.*?(?=\n\n|\n#|\n\*|$)",
            r"调整说明：.*?(?=\n\n|\n#|\n\*|$)",
            r"改进说明：.*?(?=\n\n|\n#|\n\*|$)",

            # 思考过程相关
            r"思考过程：.*?(?=\n\n|\n#|\n\*|$)",
            r"分析过程：.*?(?=\n\n|\n#|\n\*|$)",
            r"推理过程：.*?(?=\n\n|\n#|\n\*|$)",
            r"考虑因素：.*?(?=\n\n|\n#|\n\*|$)",
            r"评估结果：.*?(?=\n\n|\n#|\n\*|$)",

            # 版本对比相关
            r"对比分析：.*?(?=\n\n|\n#|\n\*|$)",
            r"版本对比：.*?(?=\n\n|\n#|\n\*|$)",
            r"差异说明：.*?(?=\n\n|\n#|\n\*|$)",
            r"变更记录：.*?(?=\n\n|\n#|\n\*|$)",

            # AI模型自我介绍
            r"我是.*?AI.*?(?=\n\n|\n#|\n\*|$)",
            r"作为.*?助手.*?(?=\n\n|\n#|\n\*|$)",
            r"基于.*?模型.*?(?=\n\n|\n#|\n\*|$)",

            # 确认和总结性废话
            r"总结.*?以上.*?(?=\n\n|\n#|\n\*|$)",
            r"综上所述.*?(?=\n\n|\n#|\n\*|$)",
            r"最终确认.*?(?=\n\n|\n#|\n\*|$)",
            r"完成确认.*?(?=\n\n|\n#|\n\*|$)",
        ]

    def _get_skip_patterns(self) -> List[str]:
        """获取需要跳过的段落模式"""
        return [
            r'优化前.*?优化后',
            r'原始版本.*?优化版本',
            r'修改前.*?修改后',
            r'调整前.*?调整后',
            r'第.*?轮优化',
            r'优化说明',
            r'修改说明',
            r'调整说明',
            r'改进说明',
            r'思考过程',
            r'分析过程',
            r'推理过程',
            r'考虑因素',
            r'评估结果',
            r'对比分析',
            r'版本对比',
            r'差异说明',
            r'变更记录',
            r'我是.*?AI',
            r'作为.*?助手',
            r'基于.*?模型',
            r'总结.*?以上',
            r'综上所述',
            r'最终确认',
            r'完成确认',
            r'好的，遵照',
            r'您提供的.*?方案',
            r'以下是.*?确认.*?版本',
            r'我已.*?进行了.*?检查',
            r'该方案.*?成功',
            r'此版本.*?采纳',
            r'【.*?】✓.*?已实现',
            r'最终优化版本.*?确认稿',
            r'经我最终确认',
            r'根据统筹模型的安排',
            r'基于您的优化方案'
        ]

    def clean_model_response(self, response: str) -> str:
        """清理模型响应中的废话和思考过程"""
        if not response:
            return response

        cleaned_response = response

        for pattern in self.patterns_to_remove:
            cleaned_response = re.sub(pattern, "", cleaned_response, flags=re.DOTALL | re.MULTILINE)

        # 清理多余的空行
        cleaned_response = re.sub(r'\n\s*\n\s*\n', '\n\n', cleaned_response)
        cleaned_response = cleaned_response.strip()

        return cleaned_response

    def extract_final_content_only(self, content: str) -> str:
        """提取最终内容，彻底清理所有思考过程和优化对比"""
        if not content:
            return content

        # 分割内容为段落
        paragraphs = content.split('\n\n')
        final_paragraphs = []

        for paragraph in paragraphs:
            paragraph = paragraph.strip()
            if not paragraph:
                continue

            # 跳过包含优化过程的段落
            should_skip = False
            for pattern in self.skip_patterns:
                if re.search(pattern, paragraph, re.IGNORECASE):
                    should_skip = True
                    break

            if not should_skip:
                # 进一步清理段落内的废话
                cleaned_paragraph = self.clean_paragraph_content(paragraph)
                if cleaned_paragraph and len(cleaned_paragraph.strip()) > 10:  # 只保留有意义的内容
                    final_paragraphs.append(cleaned_paragraph)

        return '\n\n'.join(final_paragraphs)

    def clean_paragraph_content(self, paragraph: str) -> str:
        """清理段落内的废话内容"""
        # 移除段落开头的废话
        start_patterns = [
            r'^好的，.*?[。！？]',
            r'^作为.*?[，,]',
            r'^根据.*?要求[，,]',
            r'^基于.*?分析[，,]',
            r'^经过.*?考虑[，,]',
            r'^通过.*?研究[，,]'
        ]

        for pattern in start_patterns:
            paragraph = re.sub(pattern, '', paragraph, flags=re.MULTILINE)

        # 移除段落中的废话句子
        sentence_patterns = [
            r'这是.*?优化.*?版本[。！？]',
            r'经过.*?调整.*?如下[。！？]',
            r'修改.*?内容.*?如下[。！？]',
            r'优化.*?结果.*?如下[。！？]'
        ]

        for pattern in sentence_patterns:
            paragraph = re.sub(pattern, '', paragraph, flags=re.MULTILINE)

        # 清理多余空白
        paragraph = re.sub(r'\s+', ' ', paragraph)
        paragraph = paragraph.strip()

        return paragraph

    def clean_content_thoroughly(self, content: str) -> str:
        """彻底清理内容（组合所有清理方法）"""
        if not content:
            return content

        # 第一步：基础清理
        content = self.clean_model_response(content)
        
        # 第二步：提取最终内容
        content = self.extract_final_content_only(content)
        
        # 第三步：最终清理
        content = re.sub(r'\n\s*\n\s*\n', '\n\n', content)
        content = content.strip()
        
        return content
