"""
测试图片嵌入功能
验证基于Gemini智能匹配的图片嵌入功能
"""
import os
import sys
import time
import shutil
from pathlib import Path
from PIL import Image, ImageDraw, ImageFont

def create_test_report_data():
    """创建测试报告数据"""
    print("📁 创建测试报告数据...")
    
    test_dir = Path("test_image_embedding_data")
    if test_dir.exists():
        shutil.rmtree(test_dir)
    test_dir.mkdir()
    
    # 创建测试文档
    with open(test_dir / "report_content.md", "w", encoding="utf-8") as f:
        f.write("""# 地热发电技术分析报告

## 1. 技术概述

地热发电是一种利用地下热能进行发电的清洁能源技术。本报告将深入分析地热发电的技术原理、市场现状和发展前景。

### 1.1 技术原理

地热发电主要通过以下几种方式实现：

1. **干蒸汽发电**: 直接利用地下蒸汽驱动汽轮机
2. **闪蒸发电**: 将高温地热水转化为蒸汽
3. **双循环发电**: 使用有机工质的闭式循环系统

### 1.2 系统架构

地热发电系统通常包含以下主要组件：
- 地热井
- 蒸汽分离器
- 汽轮机
- 发电机
- 冷却系统

## 2. 市场分析

### 2.1 全球市场规模

根据最新数据显示，全球地热发电装机容量持续增长。主要市场包括美国、印尼、菲律宾等国家。

### 2.2 技术发展趋势

- 增强地热系统(EGS)技术不断成熟
- 双循环技术效率持续提升
- 数字化监控系统广泛应用

## 3. 案例研究

### 3.1 成功案例

本节将分析几个典型的地热发电项目，包括技术方案、投资规模和运营效果。

### 3.2 技术创新

介绍最新的技术创新和突破，包括新材料应用、系统优化等方面。

## 4. 发展前景

地热发电作为清洁能源的重要组成部分，在全球能源转型中发挥着重要作用。未来发展前景广阔。
""")
    
    # 创建相关的图片
    images_created = []
    
    # 1. 技术流程图
    try:
        img = Image.new('RGB', (800, 600), color='white')
        draw = ImageDraw.Draw(img)
        
        # 绘制流程图
        boxes = [
            (50, 100, 200, 150, "地热井"),
            (250, 100, 400, 150, "蒸汽分离"),
            (450, 100, 600, 150, "汽轮机"),
            (650, 100, 750, 150, "发电机")
        ]
        
        for x1, y1, x2, y2, text in boxes:
            draw.rectangle([x1, y1, x2, y2], outline='black', width=2)
            try:
                font = ImageFont.load_default()
                text_bbox = draw.textbbox((0, 0), text, font=font)
                text_width = text_bbox[2] - text_bbox[0]
                text_height = text_bbox[3] - text_bbox[1]
                text_x = x1 + (x2 - x1 - text_width) // 2
                text_y = y1 + (y2 - y1 - text_height) // 2
                draw.text((text_x, text_y), text, fill='black', font=font)
            except:
                pass
        
        # 绘制箭头
        arrows = [(200, 125, 250, 125), (400, 125, 450, 125), (600, 125, 650, 125)]
        for x1, y1, x2, y2 in arrows:
            draw.line([(x1, y1), (x2, y2)], fill='black', width=3)
            draw.polygon([(x2-10, y2-5), (x2, y2), (x2-10, y2+5)], fill='black')
        
        # 添加标题
        try:
            font = ImageFont.load_default()
            draw.text((300, 50), "地热发电技术流程图", fill='black', font=font)
        except:
            pass
        
        img_path = test_dir / "geothermal_process_flow.png"
        img.save(img_path)
        images_created.append("geothermal_process_flow.png")
        print(f"   ✅ 创建技术流程图: {img_path}")
        
    except Exception as e:
        print(f"   ⚠️ 创建技术流程图失败: {str(e)}")
    
    # 2. 市场数据图表
    try:
        img = Image.new('RGB', (700, 500), color='white')
        draw = ImageDraw.Draw(img)
        
        # 绘制柱状图
        years = ['2020', '2021', '2022', '2023', '2024']
        values = [13.9, 14.5, 15.2, 16.1, 17.0]  # GW
        
        bar_width = 80
        bar_spacing = 120
        start_x = 100
        start_y = 400
        max_height = 250
        
        for i, (year, value) in enumerate(zip(years, values)):
            x = start_x + i * bar_spacing
            height = int((value / max(values)) * max_height)
            
            # 绘制柱子
            draw.rectangle([x, start_y - height, x + bar_width, start_y], fill='blue')
            
            # 添加数值标签
            try:
                font = ImageFont.load_default()
                draw.text((x + 20, start_y - height - 20), f"{value}GW", fill='black', font=font)
                draw.text((x + 20, start_y + 10), year, fill='black', font=font)
            except:
                pass
        
        # 添加标题和轴标签
        try:
            font = ImageFont.load_default()
            draw.text((250, 30), "全球地热发电装机容量", fill='black', font=font)
            draw.text((50, start_y + 50), "年份", fill='black', font=font)
            draw.text((30, 200), "装机容量(GW)", fill='black', font=font)
        except:
            pass
        
        img_path = test_dir / "geothermal_market_data.png"
        img.save(img_path)
        images_created.append("geothermal_market_data.png")
        print(f"   ✅ 创建市场数据图表: {img_path}")
        
    except Exception as e:
        print(f"   ⚠️ 创建市场数据图表失败: {str(e)}")
    
    # 3. 系统架构图
    try:
        img = Image.new('RGB', (800, 600), color='white')
        draw = ImageDraw.Draw(img)
        
        # 绘制系统架构
        components = [
            (100, 450, 200, 500, "地热资源"),
            (300, 350, 400, 400, "生产井"),
            (500, 250, 600, 300, "发电厂"),
            (300, 150, 400, 200, "回注井"),
            (650, 350, 750, 400, "电网")
        ]
        
        for x1, y1, x2, y2, text in components:
            draw.rectangle([x1, y1, x2, y2], outline='black', width=2, fill='lightblue')
            try:
                font = ImageFont.load_default()
                text_bbox = draw.textbbox((0, 0), text, font=font)
                text_width = text_bbox[2] - text_bbox[0]
                text_height = text_bbox[3] - text_bbox[1]
                text_x = x1 + (x2 - x1 - text_width) // 2
                text_y = y1 + (y2 - y1 - text_height) // 2
                draw.text((text_x, text_y), text, fill='black', font=font)
            except:
                pass
        
        # 添加连接线
        connections = [
            (200, 475, 300, 375),  # 地热资源到生产井
            (400, 375, 500, 275),  # 生产井到发电厂
            (500, 275, 400, 175),  # 发电厂到回注井
            (400, 175, 200, 475),  # 回注井回到地热资源
            (600, 275, 650, 375)   # 发电厂到电网
        ]
        
        for x1, y1, x2, y2 in connections:
            draw.line([(x1, y1), (x2, y2)], fill='red', width=2)
        
        # 添加标题
        try:
            font = ImageFont.load_default()
            draw.text((300, 50), "地热发电系统架构图", fill='black', font=font)
        except:
            pass
        
        img_path = test_dir / "geothermal_system_architecture.png"
        img.save(img_path)
        images_created.append("geothermal_system_architecture.png")
        print(f"   ✅ 创建系统架构图: {img_path}")
        
    except Exception as e:
        print(f"   ⚠️ 创建系统架构图失败: {str(e)}")
    
    print(f"✅ 创建了 1 个文档文件和 {len(images_created)} 个图片文件")
    return test_dir, images_created

def test_image_embedding_functionality():
    """测试图片嵌入功能"""
    print("🧪 测试图片嵌入功能")
    print("=" * 60)
    
    try:
        from complete_report_generator import CompleteReportGenerator
        
        # 创建测试数据
        test_dir, images_created = create_test_report_data()
        
        # 初始化生成器
        generator = CompleteReportGenerator(use_async=False)
        
        # 首先读取数据源以创建图片缓存
        print("📖 读取数据源并创建图片缓存...")
        content = generator.read_data_source(str(test_dir))
        
        # 检查图片缓存是否创建
        processed_dir = test_dir / "processed"
        image_index_dir = processed_dir / "image_index"
        
        if not image_index_dir.exists():
            print("❌ 图片索引未创建，无法进行嵌入测试")
            return False
        
        # 创建一个简单的测试报告
        test_report_path = test_dir / "test_report.md"
        with open(test_report_path, 'w', encoding='utf-8') as f:
            f.write(content)  # 使用读取的内容作为测试报告
        
        print(f"📄 创建测试报告: {test_report_path}")
        
        # 测试图片嵌入功能
        print("🖼️ 测试图片嵌入功能...")
        
        try:
            enhanced_report_path = generator.embed_images_in_report(
                str(test_report_path), 
                [str(test_dir)], 
                "地热发电技术分析", 
                auto_confirm=True  # 自动确认以便测试
            )
            
            if enhanced_report_path != str(test_report_path):
                print(f"✅ 图片嵌入成功: {enhanced_report_path}")
                
                # 检查增强报告的内容
                with open(enhanced_report_path, 'r', encoding='utf-8') as f:
                    enhanced_content = f.read()
                
                # 统计图片引用
                image_refs = enhanced_content.count('![')
                print(f"📊 增强报告包含 {image_refs} 个图片引用")
                
                if image_refs > 0:
                    print("✅ 图片嵌入功能测试通过")
                    return True
                else:
                    print("⚠️ 增强报告中未找到图片引用")
                    return False
            else:
                print("⚠️ 未生成增强报告，可能没有合适的图片匹配")
                return False
                
        except Exception as e:
            print(f"❌ 图片嵌入测试失败: {str(e)}")
            import traceback
            traceback.print_exc()
            return False
        
        # 清理测试数据
        print(f"\n🧹 清理测试数据...")
        if test_dir.exists():
            shutil.rmtree(test_dir)
            print(f"✅ 测试数据清理完成")
        
        return True
        
    except ImportError as e:
        print(f"❌ 导入失败: {str(e)}")
        return False
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_individual_components():
    """测试各个组件"""
    print("\n🧪 测试各个组件")
    print("=" * 60)
    
    try:
        from complete_report_generator import CompleteReportGenerator
        
        generator = CompleteReportGenerator(use_async=False)
        
        # 测试图片信息处理器
        print("📋 测试图片信息处理器...")
        processor = generator.ImageInfoProcessor(generator)
        
        test_image_data = {
            "test_chart.png": {
                "content_analysis": "可能包含图表或数据可视化内容",
                "ocr_text": "地热发电 2024年数据",
                "image_properties": {"width": 800, "height": 600, "format": "PNG"},
                "file_size": 45678
            }
        }
        
        descriptions = processor.prepare_image_info_for_gemini(test_image_data)
        
        if descriptions:
            print(f"✅ 图片信息处理器测试通过: {len(descriptions)} 个描述")
            print(f"   示例描述: {descriptions[0]['description']}")
        else:
            print("❌ 图片信息处理器测试失败")
            return False
        
        # 测试Gemini匹配器
        print("\n🔍 测试Gemini匹配器...")
        matcher = generator.GeminiImageMatcher(generator)
        
        test_content = "地热发电是一种清洁能源技术，主要包括干蒸汽、闪蒸和双循环等技术路线。"
        
        prompt = matcher.create_image_matching_prompt(test_content, descriptions)
        
        if "地热发电" in prompt and "图片" in prompt:
            print("✅ Gemini匹配器Prompt生成测试通过")
        else:
            print("❌ Gemini匹配器Prompt生成测试失败")
            return False
        
        print("✅ 各个组件测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 组件测试失败: {str(e)}")
        return False

def main():
    """主测试函数"""
    print("🚀 AI报告生成器 - 图片嵌入功能测试")
    print("=" * 60)
    
    tests = [
        ("各个组件测试", test_individual_components),
        ("图片嵌入功能测试", test_image_embedding_functionality)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {str(e)}")
            results.append((test_name, False))
    
    # 总结
    print(f"\n📊 测试总结:")
    print("=" * 60)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {test_name}: {status}")
    
    print(f"\n🎯 总体结果: {passed}/{total} 个测试通过")
    
    if passed == total:
        print(f"\n🎉 图片嵌入功能测试通过！")
        print(f"\n💡 功能特点:")
        print(f"   1. 🧠 基于Gemini的智能图片匹配")
        print(f"   2. 📋 详细的匹配分析和推理")
        print(f"   3. 🖼️ 支持Word和Markdown格式")
        print(f"   4. 👤 用户确认机制")
        print(f"   5. 📊 匹配预览报告")
    else:
        print(f"\n💡 如果测试失败，请检查:")
        print(f"   1. 确保Pillow库已安装: pip install Pillow")
        print(f"   2. 确保python-docx库已安装: pip install python-docx")
        print(f"   3. 检查Gemini API配置")
        print(f"   4. 确保有足够的磁盘空间")
    
    return passed == total

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\n❌ 用户中断测试")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 测试程序错误: {str(e)}")
        sys.exit(1)
