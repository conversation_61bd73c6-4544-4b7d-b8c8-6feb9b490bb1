"""
LLM交互模块
封装所有与Google Gemini API的交互
"""
import time
import json
from typing import Dict, Any, Optional, Union, List
from tenacity import retry, stop_after_attempt, wait_exponential, retry_if_exception_type
import google.generativeai as genai

from .config import Config
from .logger import APICallLogger
from .rate_limiter import TokenRateLimiter


class LLMClient:
    """
    LLM客户端类
    负责所有与Gemini API的交互
    """
    
    def __init__(self, config: Config, api_logger: APICallLogger, rate_limiter: Optional[TokenRateLimiter] = None):
        self.config = config
        self.api_logger = api_logger
        self.rate_limiter = rate_limiter
        
        # 配置API密钥
        genai.configure(api_key=config.api.api_key)
        
        # 初始化模型
        self.orchestrator = genai.GenerativeModel(
            model_name=config.api.orchestrator_model,
            generation_config={
                "temperature": config.generation.temperature,
                "top_p": config.generation.top_p,
                "top_k": config.generation.top_k,
                "max_output_tokens": config.generation.max_output_tokens,
            }
        )
        
        self.executor = genai.GenerativeModel(
            model_name=config.api.executor_model,
            generation_config={
                "temperature": config.generation.temperature,
                "top_p": config.generation.top_p,
                "top_k": config.generation.top_k,
                "max_output_tokens": config.generation.max_output_tokens,
            }
        )
    
    @retry(
        stop=stop_after_attempt(3),
        wait=wait_exponential(multiplier=2, min=2, max=60),
        retry=retry_if_exception_type(Exception)
    )
    def call_llm(
        self, 
        prompt: str, 
        context: Optional[Dict[str, Any]] = None,
        model_type: str = "orchestrator",
        response_format: str = "text"
    ) -> Union[str, Dict[str, Any]]:
        """
        调用LLM API
        
        Args:
            prompt: 提示词
            context: 上下文内容（可包含多模态数据）
            model_type: 使用的模型类型 ("orchestrator" 或 "executor")
            response_format: 期望的响应格式 ("text" 或 "json")
            
        Returns:
            模型响应内容
        """
        start_time = time.time()
        model = self.orchestrator if model_type == "orchestrator" else self.executor
        
        try:
            # 构建请求内容
            if context and "parts" in context:
                # 多模态内容
                request_parts = context["parts"] + [{"text": prompt}]
            else:
                # 纯文本内容
                request_parts = [{"text": prompt}]
            
            # 如果有速率限制器，检查并等待
            if self.rate_limiter:
                # 估算token数
                estimated_tokens = self._estimate_request_tokens(request_parts)
                
                # 检查是否需要等待
                wait_time = self.rate_limiter.wait_if_needed(estimated_tokens)
                if wait_time > 0:
                    self.api_logger.logger.info(f"等待{wait_time:.1f}秒以避免超出速率限制")
                    time.sleep(wait_time)
                
                # 再次检查是否可以发起请求
                if not self.rate_limiter.can_make_request(estimated_tokens):
                    raise ValueError(f"请求token数({estimated_tokens})超出限制")
            
            # 发送请求
            response = model.generate_content(request_parts)
            
            # 处理响应
            result_text = response.text
            
            # 如果期望JSON格式，尝试解析
            if response_format == "json":
                try:
                    # 处理可能的markdown代码块包装
                    cleaned_text = result_text.strip()
                    if cleaned_text.startswith('```json'):
                        cleaned_text = cleaned_text[7:]
                    elif cleaned_text.startswith('```'):
                        cleaned_text = cleaned_text[3:]
                    if cleaned_text.endswith('```'):
                        cleaned_text = cleaned_text[:-3]
                    cleaned_text = cleaned_text.strip()
                    
                    result = json.loads(cleaned_text)
                except json.JSONDecodeError:
                    # JSON解析失败，尝试修复
                    result = self._fix_json_response(result_text, prompt, model)
            else:
                result = result_text
            
            # 记录API调用
            duration = time.time() - start_time
            input_tokens = response.usage_metadata.prompt_token_count
            output_tokens = response.usage_metadata.candidates_token_count
            
            self.api_logger.log_api_call(
                model=self.config.api.orchestrator_model if model_type == "orchestrator" else self.config.api.executor_model,
                operation=f"{model_type}_{response_format}",
                input_tokens=input_tokens,
                output_tokens=output_tokens,
                cost=self._calculate_cost(
                    input_tokens,
                    output_tokens,
                    model_type
                ),
                duration=duration,
                success=True
            )
            
            # 如果有速率限制器，记录token使用
            if self.rate_limiter:
                self.rate_limiter.record_usage(input_tokens)
            
            return result
            
        except Exception as e:
            duration = time.time() - start_time
            self.api_logger.log_api_call(
                model=self.config.api.orchestrator_model if model_type == "orchestrator" else self.config.api.executor_model,
                operation=f"{model_type}_{response_format}",
                duration=duration,
                success=False,
                error=str(e)
            )
            raise
    
    def _fix_json_response(self, invalid_json: str, original_prompt: str, model) -> Dict[str, Any]:
        """
        尝试修复无效的JSON响应
        
        Args:
            invalid_json: 无效的JSON字符串
            original_prompt: 原始提示词
            model: 使用的模型实例
            
        Returns:
            修复后的JSON对象
            
        Raises:
            ValueError: 如果无法修复JSON
        """
        fix_prompt = f"""
        你之前的响应包含无效的JSON格式。请修复以下JSON并返回有效的JSON：
        
        原始请求：{original_prompt}
        
        无效的JSON响应：
        {invalid_json}
        
        请只返回修复后的有效JSON，不要包含其他文字说明。
        """
        
        try:
            response = model.generate_content([{"text": fix_prompt}])
            fixed_json = json.loads(response.text)
            return fixed_json
        except Exception as e:
            raise ValueError(f"无法修复JSON响应: {str(e)}")
    
    def _calculate_cost(self, input_tokens: int, output_tokens: int, model_type: str) -> float:
        """
        计算API调用成本（估算）
        
        Args:
            input_tokens: 输入token数
            output_tokens: 输出token数
            model_type: 模型类型
            
        Returns:
            估算的成本（美元）
        """
        # Gemini定价（仅供参考，实际价格可能变化）
        if model_type == "orchestrator":
            # Gemini 1.5 Pro定价
            input_cost_per_1k = 0.00125
            output_cost_per_1k = 0.005
        else:
            # Gemini 1.5 Flash定价
            input_cost_per_1k = 0.00015
            output_cost_per_1k = 0.0006
        
        total_cost = (input_tokens / 1000 * input_cost_per_1k) + (output_tokens / 1000 * output_cost_per_1k)
        return round(total_cost, 6)
    
    def validate_json_response(self, response: Any, expected_structure: Optional[Dict[str, Any]] = None) -> bool:
        """
        验证JSON响应是否符合预期结构
        
        Args:
            response: 响应内容
            expected_structure: 预期的结构描述
            
        Returns:
            是否有效
        """
        if not isinstance(response, dict):
            return False
        
        if expected_structure:
            # 这里可以添加更复杂的结构验证逻辑
            pass
        
        return True

    def _estimate_request_tokens(self, request_parts: List[Dict[str, Any]]) -> int:
        """
        估算请求的token数量

        Args:
            request_parts: 请求部分列表

        Returns:
            估算的token数量
        """
        total_chars = 0
        for part in request_parts:
            if "text" in part:
                total_chars += len(part["text"])

        # 粗略估算：平均每个token约4个字符
        estimated_tokens = total_chars // 4
        return max(estimated_tokens, 100)  # 最少100个token
