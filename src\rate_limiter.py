"""
速率限制模块
用于控制API调用的token使用量，避免超出速率限制
"""
import time
import tiktoken
from typing import List, Dict, Any, Optional
from collections import deque
from datetime import datetime, timedelta

from .config import Config
from .logger import ProcessLogger


class TokenRateLimiter:
    """
    Token速率限制器
    跟踪并限制每分钟的token使用量
    """
    
    def __init__(self, config: Config, logger: ProcessLogger):
        self.config = config
        self.logger = logger
        self.max_tokens_per_minute = config.rate_limiting.max_tokens_per_minute
        self.max_tokens_per_request = config.rate_limiting.max_tokens_per_request
        self.batch_delay = config.rate_limiting.batch_delay_seconds
        
        # 使用双端队列跟踪最近一分钟的token使用
        self.token_usage_history = deque()
        
        # 初始化tiktoken编码器用于计算token数
        try:
            self.encoder = tiktoken.encoding_for_model("gpt-3.5-turbo")
        except:
            self.encoder = tiktoken.get_encoding("cl100k_base")
    
    def estimate_tokens(self, text: str) -> int:
        """
        估算文本的token数量
        
        Args:
            text: 输入文本
            
        Returns:
            估算的token数
        """
        try:
            return len(self.encoder.encode(text))
        except:
            # 粗略估算：平均每4个字符约等于1个token
            return len(text) // 4
    
    def estimate_context_tokens(self, context: Dict[str, Any]) -> int:
        """
        估算上下文的总token数
        
        Args:
            context: 包含parts的上下文字典
            
        Returns:
            估算的总token数
        """
        total_tokens = 0
        
        if "parts" in context:
            for part in context["parts"]:
                if "text" in part:
                    total_tokens += self.estimate_tokens(part["text"])
                elif "inline_data" in part:
                    # 图像数据的token估算（base64编码）
                    # Gemini API中，图像通常占用约258个token
                    total_tokens += 258
        
        return total_tokens
    
    def can_make_request(self, estimated_tokens: int) -> bool:
        """
        检查是否可以发起请求
        
        Args:
            estimated_tokens: 预估的请求token数
            
        Returns:
            是否可以发起请求
        """
        # 清理超过1分钟的历史记录
        self._cleanup_old_usage()
        
        # 检查单个请求是否超过限制
        if estimated_tokens > self.max_tokens_per_request:
            self.logger.logger.warning(
                f"单个请求token数({estimated_tokens})超过限制({self.max_tokens_per_request})"
            )
            return False
        
        # 计算当前分钟内已使用的token
        current_usage = sum(usage["tokens"] for usage in self.token_usage_history)
        
        # 检查是否会超过每分钟限制
        if current_usage + estimated_tokens > self.max_tokens_per_minute:
            self.logger.logger.warning(
                f"即将超过每分钟token限制：当前已用{current_usage}，"
                f"本次请求{estimated_tokens}，限制{self.max_tokens_per_minute}"
            )
            return False
        
        return True
    
    def wait_if_needed(self, estimated_tokens: int) -> float:
        """
        如果需要等待，返回需要等待的秒数
        
        Args:
            estimated_tokens: 预估的请求token数
            
        Returns:
            需要等待的秒数
        """
        self._cleanup_old_usage()
        
        current_usage = sum(usage["tokens"] for usage in self.token_usage_history)
        
        if current_usage + estimated_tokens > self.max_tokens_per_minute:
            # 找到最早的记录，计算需要等待的时间
            if self.token_usage_history:
                oldest_timestamp = self.token_usage_history[0]["timestamp"]
                wait_time = 60 - (datetime.now() - oldest_timestamp).total_seconds()
                return max(0, wait_time + 1)  # 额外等待1秒作为缓冲
        
        return 0
    
    def record_usage(self, tokens: int):
        """
        记录token使用
        
        Args:
            tokens: 使用的token数
        """
        self.token_usage_history.append({
            "timestamp": datetime.now(),
            "tokens": tokens
        })
        
        self.logger.logger.info(f"记录token使用：{tokens} tokens")
    
    def _cleanup_old_usage(self):
        """清理超过1分钟的使用记录"""
        cutoff_time = datetime.now() - timedelta(minutes=1)
        
        while self.token_usage_history and self.token_usage_history[0]["timestamp"] < cutoff_time:
            self.token_usage_history.popleft()
    
    def split_into_batches(self, snippets: List[Any], estimate_func=None) -> List[List[Any]]:
        """
        将内容片段分批，确保每批不超过token限制
        
        Args:
            snippets: 内容片段列表
            estimate_func: 用于估算单个片段token数的函数
            
        Returns:
            分批后的片段列表
        """
        if not estimate_func:
            estimate_func = lambda x: self.estimate_tokens(str(x))
        
        batches = []
        current_batch = []
        current_tokens = 0
        
        for snippet in snippets:
            snippet_tokens = estimate_func(snippet)
            
            # 如果单个片段就超过限制，需要特殊处理
            if snippet_tokens > self.max_tokens_per_request:
                self.logger.logger.warning(
                    f"单个片段token数({snippet_tokens})超过请求限制，将尝试截断"
                )
                # 将当前批次保存
                if current_batch:
                    batches.append(current_batch)
                    current_batch = []
                    current_tokens = 0
                # 单独处理这个大片段（可能需要截断）
                batches.append([snippet])
                continue
            
            # 检查是否需要开始新批次
            if current_tokens + snippet_tokens > self.max_tokens_per_request * 0.9:  # 留10%缓冲
                batches.append(current_batch)
                current_batch = [snippet]
                current_tokens = snippet_tokens
            else:
                current_batch.append(snippet)
                current_tokens += snippet_tokens
        
        # 添加最后一批
        if current_batch:
            batches.append(current_batch)
        
        self.logger.logger.info(f"将{len(snippets)}个片段分成{len(batches)}批处理")
        
        return batches
    
    def get_usage_stats(self) -> Dict[str, Any]:
        """
        获取当前使用统计
        
        Returns:
            使用统计信息
        """
        self._cleanup_old_usage()
        
        current_usage = sum(usage["tokens"] for usage in self.token_usage_history)
        
        return {
            "current_minute_usage": current_usage,
            "max_per_minute": self.max_tokens_per_minute,
            "usage_percentage": (current_usage / self.max_tokens_per_minute) * 100,
            "remaining_tokens": self.max_tokens_per_minute - current_usage
        }
