"""
扩展文件格式支持依赖安装脚本
安装支持更多文件格式读取的Python库
"""
import subprocess
import sys
import os

def install_package(package):
    """安装Python包"""
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", package])
        print(f"✅ 成功安装: {package}")
        return True
    except subprocess.CalledProcessError:
        print(f"❌ 安装失败: {package}")
        return False

def main():
    """主函数"""
    print("🔧 扩展文件格式支持依赖安装")
    print("=" * 60)
    
    # 基础文件读取库
    basic_packages = [
        ("PyPDF2", "PDF文件读取"),
        ("pdfplumber", "PDF文件读取（增强版）"),
        ("pandas", "Excel和CSV文件读取"),
        ("openpyxl", "Excel文件读取支持"),
        ("xlrd", "旧版Excel文件支持"),
        ("python-docx", "Word文件读取"),
        ("python-pptx", "PowerPoint文件读取"),
        ("tqdm", "进度条显示"),
    ]
    
    # 扩展格式支持库
    extended_packages = [
        ("striprtf", "RTF文件读取"),
        ("docx2txt", "旧版Word文档读取"),
        ("PyYAML", "YAML文件读取"),
        ("odfpy", "OpenDocument格式读取"),
        ("ebooklib", "EPUB电子书读取"),
        ("beautifulsoup4", "HTML/XML解析"),
        ("lxml", "XML解析增强"),
    ]
    
    # OCR和图像处理库
    ocr_packages = [
        ("pytesseract", "OCR文字识别"),
        ("Pillow", "图像处理"),
        ("pdf2image", "PDF转图片"),
        ("PyMuPDF", "PDF处理增强"),
    ]
    
    print("📦 第一步：安装基础文件读取库...")
    basic_success = 0
    for package, description in basic_packages:
        print(f"\n📥 安装 {package} ({description})...")
        if install_package(package):
            basic_success += 1
    
    print(f"\n📊 基础库安装结果: {basic_success}/{len(basic_packages)} 个包安装成功")
    
    print("\n📦 第二步：安装扩展格式支持库...")
    extended_success = 0
    for package, description in extended_packages:
        print(f"\n📥 安装 {package} ({description})...")
        if install_package(package):
            extended_success += 1
    
    print(f"\n📊 扩展库安装结果: {extended_success}/{len(extended_packages)} 个包安装成功")
    
    print("\n📦 第三步：安装OCR和图像处理库...")
    ocr_success = 0
    for package, description in ocr_packages:
        print(f"\n📥 安装 {package} ({description})...")
        if install_package(package):
            ocr_success += 1
    
    print(f"\n📊 OCR库安装结果: {ocr_success}/{len(ocr_packages)} 个包安装成功")
    
    total_packages = len(basic_packages) + len(extended_packages) + len(ocr_packages)
    total_success = basic_success + extended_success + ocr_success
    
    print(f"\n🎯 总体安装结果: {total_success}/{total_packages} 个包安装成功")
    
    if total_success >= total_packages * 0.8:  # 80%以上成功
        print("\n🎉 扩展文件格式支持安装基本完成！")
        print("\n📋 现在支持的文件格式包括:")
        print("   📄 文本文件: .txt, .md, .rst, .log, .rtf")
        print("   📊 Office文档: .docx, .doc, .xlsx, .xls, .csv, .tsv, .pptx, .ppt")
        print("   📑 PDF文件: .pdf (支持文本和OCR)")
        print("   🔧 数据文件: .json, .xml, .yaml, .yml")
        print("   💻 代码文件: .py, .js, .html, .css, .sql, .java, .cpp, .c, .h, .php, .rb, .go, .sh, .bat")
        print("   ⚙️ 配置文件: .ini, .cfg, .conf, .properties, .toml")
        print("   📚 其他格式: .odt, .ods, .epub")
        
        if ocr_success > 0:
            print("\n📋 OCR支持说明:")
            print("   如果需要OCR功能，还需要安装Tesseract OCR引擎:")
            print("   1. 访问: https://github.com/UB-Mannheim/tesseract/wiki")
            print("   2. 下载Windows版本的Tesseract")
            print("   3. 安装到默认路径: C:\\Program Files\\Tesseract-OCR")
        
        print(f"\n🚀 现在可以使用扩展的文件格式支持功能了！")
    else:
        print("\n⚠️ 部分包安装失败，请检查网络连接或权限")
        print("💡 可以尝试使用管理员权限运行")
        print("💡 或者手动安装失败的包: pip install <package_name>")

if __name__ == "__main__":
    main()
