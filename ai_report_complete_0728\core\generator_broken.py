#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
核心报告生成器模�?这是整个系统的核心，负责协调各个模块完成报告生成
"""

import os
import time
import asyncio
from pathlib import Path
from typing import List, Dict, Any, Optional
from datetime import datetime

try:
    from .config import ReportConfig, API_KEYS, MODEL_NAMES
    from ..api.gemini_manager_new import GeminiAPIManager, AsyncGeminiAPIManager
    from ..utils.token_manager import TokenManager
    from ..content.content_cleaner import ContentCleaner
    from ..search.search_trigger import SearchTrigger
    from ..search.search_manager import SearchManager
    from ..search.content_validator import ContentValidator
    from ..search.content_integrator import ContentIntegrator
    from .optimization import ReportOptimizer
except ImportError:
    # 绝对导入（用于直接运行）
    from core.config import ReportConfig, API_KEYS, MODEL_NAMES
    from api.gemini_manager_new import GeminiAPIManager, AsyncGeminiAPIManager
    from utils.token_manager import TokenManager
    from content.content_cleaner import ContentCleaner
    from search.search_trigger import SearchTrigger
    from search.search_manager import SearchManager
    from search.content_validator import ContentValidator
    from search.content_integrator import ContentIntegrator
    from core.optimization import ReportOptimizer


class CompleteReportGenerator:
    """
    完整的AI报告生成�?    严格按照用户需求实现所有功�?    支持异步并行优化
    """

    def __init__(self, use_async: bool = True, max_tokens: int = 250000):
        self.use_async = use_async
        self.config = ReportConfig(use_async=use_async, max_tokens=max_tokens)

        # 初始化API管理�?        if use_async:
            self.api_manager = AsyncGeminiAPIManager(API_KEYS, MODEL_NAMES)
        else:
            self.api_manager = GeminiAPIManager(API_KEYS, MODEL_NAMES)

        # 初始化工具组�?        self.token_manager = TokenManager(max_tokens)
        self.content_cleaner = ContentCleaner()

        # 初始化搜索组�?        self.search_trigger = SearchTrigger(self)
        self.search_manager = SearchManager(self)
        self.content_validator = ContentValidator(self)
        self.content_integrator = ContentIntegrator(self)

        # 初始化优化器
        self.optimizer = ReportOptimizer(self)

        # 报告配置
        self.report_config = self.config.to_dict()
        self.current_checkpoint_id = None

        # 报告配置（动态配置）
        self.report_config = {
            "title": "",
            "data_source": "",
            "output_dir": "output",
            "max_depth": 6,  # 最大层级深度（动态输入）
            "primary_sections": 8,  # 一级标题数量（动态输入）
            "target_words": 50000,  # 最终报告目标字数（动态输入）
            "reference_report": "",  # 参考报告路径（可选）
            "max_tokens": max_tokens  # Token限制
        }

        # 初始化checkpoint系统
        self.checkpoint_dir = Path("checkpoints")
        self.checkpoint_dir.mkdir(exist_ok=True)
        self.current_checkpoint_id = None
        self.checkpoint_data = {}

        # 显示配置信息
        self._display_config_info()

    def _display_config_info(self):
        """显示配置信息"""
        print(f"\n📋 模型配置:")
        print(f"   统筹模型: {MODEL_NAMES[0]}")
        print(f"   执行模型: {MODEL_NAMES[1]}")
        print(f"   异步模式: {'启用' if self.use_async else '禁用'}")
        print(f"   Token限制: {self.token_manager.max_tokens:,} tokens")
        print(f"   Checkpoint目录: {self.checkpoint_dir.absolute()}")

    # ==================== Checkpoint系统 ====================

    def create_checkpoint(self, stage: str, data: dict) -> str:
        """创建checkpoint保存�?""
        try:
            import json
            import time

            # 生成checkpoint ID
            timestamp = int(time.time())
            checkpoint_id = f"{stage}_{timestamp}"

            # 准备checkpoint数据
            checkpoint_data = {
                "checkpoint_id": checkpoint_id,
                "stage": stage,
                "timestamp": timestamp,
                "created_time": time.strftime("%Y-%m-%d %H:%M:%S"),
                "report_config": self.report_config.copy(),
                "data": data,
                "version": "1.0"
            }

            # 保存checkpoint文件
            checkpoint_file = self.checkpoint_dir / f"{checkpoint_id}.json"
            with open(checkpoint_file, 'w', encoding='utf-8') as f:
                json.dump(checkpoint_data, f, ensure_ascii=False, indent=2)

            # 更新当前checkpoint
            self.current_checkpoint_id = checkpoint_id
            self.checkpoint_data = checkpoint_data

            print(f"💾 Checkpoint已保�? {checkpoint_id}")
            print(f"   阶段: {stage}")
            print(f"   文件: {checkpoint_file}")

            return checkpoint_id

        except Exception as e:
            print(f"�?保存checkpoint失败: {str(e)}")
            return ""

    def load_checkpoint(self, checkpoint_id: str) -> dict:
        """加载checkpoint数据"""
        try:
            import json

            checkpoint_file = self.checkpoint_dir / f"{checkpoint_id}.json"
            if not checkpoint_file.exists():
                print(f"�?Checkpoint文件不存�? {checkpoint_file}")
                return {}

            with open(checkpoint_file, 'r', encoding='utf-8') as f:
                checkpoint_data = json.load(f)

            # 恢复配置
            if "report_config" in checkpoint_data:
                self.report_config.update(checkpoint_data["report_config"])

            self.current_checkpoint_id = checkpoint_id
            self.checkpoint_data = checkpoint_data

            print(f"📂 Checkpoint已加�? {checkpoint_id}")
            print(f"   阶段: {checkpoint_data.get('stage', '未知')}")
            print(f"   创建时间: {checkpoint_data.get('created_time', '未知')}")

            return checkpoint_data.get("data", {})

        except Exception as e:
            print(f"�?加载checkpoint失败: {str(e)}")
            return {}

    def list_checkpoints(self) -> list:
        """列出所有可用的checkpoint"""
        try:
            import json

            checkpoints = []
            for checkpoint_file in self.checkpoint_dir.glob("*.json"):
                try:
                    with open(checkpoint_file, 'r', encoding='utf-8') as f:
                        data = json.load(f)

                    checkpoints.append({
                        "id": data.get("checkpoint_id", checkpoint_file.stem),
                        "stage": data.get("stage", "未知"),
                        "created_time": data.get("created_time", "未知"),
                        "file": str(checkpoint_file)
                    })
                except:
                    continue

            # 按时间排�?            checkpoints.sort(key=lambda x: x["created_time"], reverse=True)
            return checkpoints

        except Exception as e:
            print(f"�?列出checkpoint失败: {str(e)}")
            return []

    def cleanup_old_checkpoints(self, keep_count: int = 10):
        """清理旧的checkpoint文件"""
        try:
            checkpoints = self.list_checkpoints()
            if len(checkpoints) > keep_count:
                for checkpoint in checkpoints[keep_count:]:
                    checkpoint_file = Path(checkpoint["file"])
                    if checkpoint_file.exists():
                        checkpoint_file.unlink()
                        print(f"🗑�?已删除旧checkpoint: {checkpoint['id']}")
        except Exception as e:
            print(f"�?清理checkpoint失败: {str(e)}")

    def call_orchestrator_model(self, prompt: str) -> str:
        """调用统筹模型（同步版本）"""
        print(f"🎯 调用统筹模型: {MODEL_NAMES[0]}")
        try:
            # 检查是否在异步模式�?            if self.use_async and hasattr(self.api_manager, 'generate_content_with_model_async'):
                # 在异步模式下，使用同步接口（内部会处理事件循环）
                response, key_index = self.api_manager.generate_content_with_model(
                    prompt, MODEL_NAMES[0]
                )
            else:
                # 同步模式
                response, key_index = self.api_manager.generate_content_with_model(
                    prompt, MODEL_NAMES[0]
                )
            content = self._extract_content(response)
            self.api_manager.record_successful_processing(key_index)
            if not content or content.strip() == "":
                print(f"⚠️ 统筹模型返回空内容，使用备用内容")
                return self._generate_fallback_content(prompt, "统筹模型")

            # 清理响应中的废话
            cleaned_content = self._clean_model_response(content)
            return cleaned_content
        except Exception as e:
            print(f"统筹模型调用失败: {str(e)}")
            # 返回有意义的备用内容而不是空字符�?            return f"统筹模型调用遇到技术问题。请检查API配置或稍后重试�?

    def call_executor_model(self, prompt: str) -> str:
        """调用执行模型（同步版本）"""
        print(f"�?调用执行模型: {MODEL_NAMES[1]}")
        try:
            response, key_index = self.api_manager.generate_content_with_model(
                prompt, MODEL_NAMES[1]
            )
            content = self._extract_content(response)
            self.api_manager.record_successful_processing(key_index)
            if not content or content.strip() == "":
                print(f"⚠️ 执行模型返回空内容，使用备用内容")
                return self._generate_fallback_content(prompt, "执行模型")

            # 清理响应中的废话
            cleaned_content = self._clean_model_response(content)
            return cleaned_content
        except Exception as e:
            print(f"执行模型调用失败: {str(e)}")
            # 返回有意义的备用内容而不是空字符�?            return f"内容生成遇到技术问题。请检查API配置或稍后重试�?

    async def call_orchestrator_model_async(self, prompt: str) -> str:
        """调用统筹模型（异步版本）"""
        print(f"🎯 异步调用统筹模型: {MODEL_NAMES[0]}")
        try:
            if hasattr(self.api_manager, 'generate_content_with_model_async'):
                response, key_index = await self.api_manager.generate_content_with_model_async(
                    prompt, MODEL_NAMES[0]
                )
            else:
                # 回退到同步版�?                response, key_index = self.api_manager.generate_content_with_model(
                    prompt, MODEL_NAMES[0]
                )
            content = self._extract_content(response)
            await self.api_manager.record_successful_processing(key_index)
            if not content or content.strip() == "":
                print(f"⚠️ 异步统筹模型返回空内容，使用备用内容")
                return self._generate_fallback_content(prompt, "统筹模型")
            return content
        except Exception as e:
            print(f"统筹模型异步调用失败: {str(e)}")
            # 返回有意义的备用内容而不是空字符�?            return f"统筹模型异步调用遇到技术问题。请检查API配置或稍后重试�?

    async def call_executor_model_async(self, prompt: str) -> str:
        """调用执行模型（异步版本）"""
        print(f"�?异步调用执行模型: {MODEL_NAMES[1]}")
        try:
            if hasattr(self.api_manager, 'generate_content_with_model_async'):
                response, key_index = await self.api_manager.generate_content_with_model_async(
                    prompt, MODEL_NAMES[1]
                )
            else:
                # 回退到同步版�?                response, key_index = self.api_manager.generate_content_with_model(
                    prompt, MODEL_NAMES[1]
                )
            content = self._extract_content(response)
            await self.api_manager.record_successful_processing(key_index)
            if not content or content.strip() == "":
                print(f"⚠️ 异步执行模型返回空内容，使用备用内容")
                return self._generate_fallback_content(prompt, "执行模型")
            return content
        except Exception as e:
            print(f"执行模型异步调用失败: {str(e)}")
            # 返回有意义的备用内容而不是空字符�?            return f"内容生成遇到技术问题。请检查API配置或稍后重试�?

    def _extract_content(self, response) -> str:
        """从API响应中提取文本内�?""
        try:
            if hasattr(response, 'text'):
                return response.text
            elif hasattr(response, 'content'):
                return response.content
            elif isinstance(response, str):
                return response
            else:
                return str(response)
        except Exception as e:
            print(f"⚠️ 提取响应内容失败: {str(e)}")
            return ""

    def _clean_model_response(self, content: str) -> str:
        """清理模型响应中的废话"""
        if not content:
            return content

        # 使用内容清理�?        cleaned = self.content_cleaner.clean_model_response(content)
        return cleaned

    def _generate_fallback_content(self, prompt: str, model_type: str) -> str:
        """生成备用内容"""
        return f"[{model_type}暂时无法生成内容，请稍后重试]"

    # ==================== 数据预处理系�?====================

    def preprocess_data_sources(self, data_sources: List[str]) -> Dict[str, Any]:
        """预处理数据源（包含PDF图片提取�?""
        print("📁 开始预处理数据�?..")

        processed_data = {
            "text_content": "",
            "image_data": {},
            "file_list": [],
            "processing_summary": {}
        }

        for data_source in data_sources:
            data_path = Path(data_source)

            if data_path.is_file():
                # 处理单个文件
                file_result = self._process_single_file(data_path)
                processed_data["text_content"] += file_result["content"]
                processed_data["image_data"].update(file_result["images"])
                processed_data["file_list"].append(str(data_path))

            elif data_path.is_dir():
                # 处理目录
                dir_result = self._process_directory(data_path)
                processed_data["text_content"] += dir_result["content"]
                processed_data["image_data"].update(dir_result["images"])
                processed_data["file_list"].extend(dir_result["files"])

        # 生成处理摘要
        processed_data["processing_summary"] = {
            "total_files": len(processed_data["file_list"]),
            "total_images": len(processed_data["image_data"]),
            "content_length": len(processed_data["text_content"]),
            "has_pdf": any(f.endswith('.pdf') for f in processed_data["file_list"])
        }

        print(f"�?数据预处理完�?")
        print(f"   文件数量: {processed_data['processing_summary']['total_files']}")
        print(f"   图片数量: {processed_data['processing_summary']['total_images']}")
        print(f"   内容长度: {processed_data['processing_summary']['content_length']:,} 字符")

        return processed_data

    def _process_single_file(self, file_path: Path) -> Dict[str, Any]:
        """处理单个文件"""
        result = {"content": "", "images": {}, "files": [str(file_path)]}

        try:
            if file_path.suffix.lower() == '.pdf':
                # PDF文件特殊处理（包含图片提取）
                pdf_result = self._process_pdf_with_images(file_path)
                result["content"] = pdf_result["text"]
                result["images"] = pdf_result["images"]
            else:
                # 其他文件类型
                from utils.complete_file_reader import CompleteFileReader
                file_reader = CompleteFileReader()
                result["content"] = file_reader.read_file(str(file_path))

        except Exception as e:
            print(f"⚠️ 处理文件失败 {file_path}: {str(e)}")

        return result

    def _process_directory(self, dir_path: Path) -> Dict[str, Any]:
        """处理目录"""
        result = {"content": "", "images": {}, "files": []}

        # 支持的文件扩展名
        supported_extensions = {'.txt', '.md', '.docx', '.pdf', '.xlsx', '.json', '.xml'}

        for file_path in dir_path.rglob('*'):
            if file_path.is_file() and file_path.suffix.lower() in supported_extensions:
                file_result = self._process_single_file(file_path)
                result["content"] += f"\n\n=== {file_path.name} ===\n{file_result['content']}"
                result["images"].update(file_result["images"])
                result["files"].append(str(file_path))

        return result

    def _process_pdf_with_images(self, pdf_path: Path) -> Dict[str, Any]:
        """处理PDF文件并提取图�?""
        result = {"text": "", "images": {}}

        try:
            # 尝试使用Gemini OCR处理PDF
            ocr_result = self._process_pdf_with_gemini_ocr(pdf_path)
            if ocr_result["success"]:
                result["text"] = ocr_result["text"]
                result["images"] = ocr_result["images"]
                return result

            # 回退到传统PDF处理
            from utils.complete_file_reader import CompleteFileReader
            file_reader = CompleteFileReader()
            result["text"] = file_reader.read_file(str(pdf_path))

            # 尝试提取PDF中的图片
            images = self._extract_images_from_pdf(pdf_path)
            result["images"] = images

        except Exception as e:
            print(f"⚠️ PDF处理失败 {pdf_path}: {str(e)}")

        return result

    def _process_pdf_with_gemini_ocr(self, pdf_path: Path) -> Dict[str, Any]:
        """使用Gemini OCR处理PDF"""
        try:
            import google.generativeai as genai

            # 将PDF转换为图�?            pdf_images = self._convert_pdf_to_images(pdf_path)
            if not pdf_images:
                return {"success": False, "text": "", "images": {}}

            # 使用Gemini处理每一�?            all_text = []
            extracted_images = {}

            for page_num, image_path in enumerate(pdf_images, 1):
                try:
                    # 上传图片到Gemini
                    uploaded_file = genai.upload_file(str(image_path))

                    # OCR提取文本
                    prompt = """
请分析这个PDF页面图片，提取其中的所有文本内容�?要求�?1. 保持原有的格式和结构
2. 准确识别所有文�?3. 如果有表格，请保持表格结�?4. 如果有图表，请描述图表内�?"""

                    response = self.call_orchestrator_model(prompt)
                    if response:
                        all_text.append(f"=== 第{page_num}�?===\n{response}")

                    # 保存图片信息
                    extracted_images[f"page_{page_num}"] = {
                        "path": str(image_path),
                        "page": page_num,
                        "description": f"PDF第{page_num}�?
                    }

                except Exception as e:
                    print(f"⚠️ 处理PDF第{page_num}页失�? {str(e)}")
                    continue

            return {
                "success": True,
                "text": "\n\n".join(all_text),
                "images": extracted_images
            }

        except Exception as e:
            print(f"⚠️ Gemini OCR处理失败: {str(e)}")
            return {"success": False, "text": "", "images": {}}

    def _convert_pdf_to_images(self, pdf_path: Path) -> List[Path]:
        """将PDF转换为图�?""
        try:
            # 尝试使用pdf2image
            try:
                from pdf2image import convert_from_path
                images = convert_from_path(str(pdf_path))

                image_paths = []
                temp_dir = Path("temp_pdf_images")
                temp_dir.mkdir(exist_ok=True)

                for i, image in enumerate(images):
                    image_path = temp_dir / f"{pdf_path.stem}_page_{i+1}.png"
                    image.save(str(image_path))
                    image_paths.append(image_path)

                return image_paths

            except ImportError:
                print("⚠️ pdf2image未安装，跳过PDF图片转换")
                return []

        except Exception as e:
            print(f"⚠️ PDF转图片失�? {str(e)}")
            return []

    def _extract_images_from_pdf(self, pdf_path: Path) -> Dict[str, Any]:
        """从PDF中提取图�?""
        images = {}

        try:
            # 尝试使用PyMuPDF提取图片
            try:
                import fitz  # PyMuPDF

                doc = fitz.open(str(pdf_path))
                for page_num in range(len(doc)):
                    page = doc.load_page(page_num)
                    image_list = page.get_images()

                    for img_index, img in enumerate(image_list):
                        xref = img[0]
                        pix = fitz.Pixmap(doc, xref)

                        if pix.n - pix.alpha < 4:  # 确保是RGB图片
                            img_name = f"pdf_page_{page_num+1}_img_{img_index+1}.png"
                            img_path = Path("temp_pdf_images") / img_name
                            img_path.parent.mkdir(exist_ok=True)

                            pix.save(str(img_path))

                            images[img_name] = {
                                "path": str(img_path),
                                "page": page_num + 1,
                                "index": img_index + 1,
                                "description": f"PDF第{page_num+1}页图片{img_index+1}"
                            }

                        pix = None

                doc.close()

            except ImportError:
                print("⚠️ PyMuPDF未安装，跳过PDF图片提取")

        except Exception as e:
            print(f"⚠️ PDF图片提取失败: {str(e)}")

        return images

    def _auto_install_pdf_libraries(self):
        """自动安装PDF处理�?""
        try:
            import subprocess
            import sys

            print("🔧 正在安装PDF处理�?..")

            # 安装PyMuPDF
            subprocess.check_call([sys.executable, "-m", "pip", "install", "PyMuPDF"])
            print("�?PyMuPDF安装成功")

            # 安装pdf2image
            subprocess.check_call([sys.executable, "-m", "pip", "install", "pdf2image"])
            print("�?pdf2image安装成功")

            return True

        except Exception as e:
            print(f"�?PDF库安装失�? {str(e)}")
            return False

    # ==================== 完整报告生成流程 ====================

    def generate_complete_report_with_all_features(self,
                                                  topic: str,
                                                  data_sources: List[str],
                                                  framework_file_path: Optional[str] = None,
                                                  target_words: int = 50000,
                                                  max_depth: int = 6,
                                                  enable_search: bool = True,
                                                  enable_images: bool = True,
                                                  enable_reference_optimization: bool = True,
                                                  reference_report_path: Optional[str] = None) -> str:
        """
        完整的报告生成流程（包含所有原代码功能�?        """
        start_time = time.time()
        print(f"🚀 开始完整报告生�? {topic}")
        print(f"📊 配置参数:")
        print(f"   目标字数: {target_words:,}")
        print(f"   最大层�? {max_depth}")
        print(f"   搜索增强: {'启用' if enable_search else '禁用'}")
        print(f"   图片嵌入: {'启用' if enable_images else '禁用'}")
        print(f"   参考优�? {'启用' if enable_reference_optimization else '禁用'}")

        try:
            # 更新配置
            self.report_config.update({
                "title": topic,
                "target_words": target_words,
                "max_depth": max_depth,
                "enable_search": enable_search,
                "enable_images": enable_images,
                "enable_reference_optimization": enable_reference_optimization,
                "reference_report_path": reference_report_path
            })

            # 第一步：数据预处理（包含PDF图片提取�?            print("\n📁 第一步：数据预处�?)
            processed_data = self.preprocess_data_sources(data_sources)

            # 保存checkpoint
            checkpoint_id = self.create_checkpoint("data_preprocessed", {
                "processed_data": processed_data,
                "topic": topic,
                "config": self.report_config
            })

            # 第二步：读取框架文件
            print("\n📖 第二步：读取框架文件")
            framework_content = ""
            if framework_file_path:
                framework_content = self.read_framework_file(framework_file_path)

            # 第三步：生成报告框架
            print("\n🏗�?第三步：生成报告框架")
            framework = self.generate_comprehensive_framework(topic, framework_content, max_depth)

            # 保存checkpoint
            self.create_checkpoint("framework_generated", {
                "framework": framework,
                "processed_data": processed_data
            })

            # 第四步：生成完整子结�?            print("\n🌳 第四步：生成完整子结�?)
            if self.use_async:
                framework = asyncio.run(self._generate_complete_substructure_async(framework, topic))
            else:
                framework = self._generate_complete_substructure(framework, topic)

            # 第五步：生成所有内�?            print("\n✍️ 第五步：生成所有内�?)
            if self.use_async:
                framework = asyncio.run(self._generate_all_content_with_data_async(framework, processed_data))
            else:
                framework = self._generate_all_content_with_data(framework, processed_data)

            # 保存checkpoint
            self.create_checkpoint("content_generated", {
                "framework": framework,
                "processed_data": processed_data
            })

            # 第六步：参考报告优化（如果启用�?            if enable_reference_optimization and reference_report_path:
                print("\n📚 第六步：参考报告优�?)
                framework = self._optimize_with_reference_report(framework, reference_report_path, topic)

            # 第七步：搜索增强（如果启用）
            if enable_search:
                print("\n🔍 第七步：搜索增强")
                framework = self._enhance_with_search(framework, topic)

            # 第八步：3轮迭代优�?            print("\n🔧 第八步：3轮迭代优�?)
            if self.use_async:
                framework = asyncio.run(self._iterative_optimization_async(framework, data_sources, topic))
            else:
                framework = self._iterative_optimization(framework, data_sources, topic)

            # 保存checkpoint
            self.create_checkpoint("optimization_completed", {
                "framework": framework,
                "processed_data": processed_data
            })

            # 第九步：字数控制
            print("\n📊 第九步：字数控制")
            if self.use_async:
                framework = asyncio.run(self._control_final_word_count_async(framework, target_words, topic))
            else:
                framework = self._control_final_word_count(framework, target_words, topic)

            # 第十步：图片嵌入（如果启用）
            if enable_images:
                print("\n🖼�?第十步：图片嵌入")
                framework = self._embed_all_images(framework, processed_data["image_data"])

            # 第十一步：生成最终文�?            print("\n📄 第十一步：生成最终文�?)
            output_path = self._generate_final_document(topic, framework, processed_data)

            # 计算总耗时
            total_time = time.time() - start_time
            print(f"\n�?完整报告生成完成�?)
            print(f"⏱️ 总耗时: {total_time:.1f}�?)
            print(f"📄 输出文件: {output_path}")
            print(f"💾 最终checkpoint: {self.current_checkpoint_id}")

            return output_path

        except Exception as e:
            print(f"\n�?报告生成失败: {str(e)}")
            if self.current_checkpoint_id:
                print(f"💾 可从checkpoint恢复: {self.current_checkpoint_id}")
            import traceback
            traceback.print_exc()
            raise

    def generate_comprehensive_framework(self, topic: str, framework_content: str = "", max_depth: int = 6) -> Dict[str, Any]:
        """生成全面的报告框�?""
        prompt = f"""
您是一位资深的产业研究专家，需要为"{topic}"生成一个专业、全面的研究报告框架�?
参考框架内容：
{framework_content if framework_content else "无参考框架，请根据主题自行设�?}

要求�?1. 生成{max_depth}级标题结�?2. 确保逻辑清晰、层次分�?3. 符合产业研究报告的专业标�?4. 包含市场分析、技术发展、竞争格局、政策环境、投资分析、发展趋势等核心内容
5. 返回JSON格式

请返回以下JSON格式�?{{
    "title": "报告标题",
    "sections": [
        {{
            "title": "一级标�?,
            "level": 1,
            "children": []
        }}
    ]
}}
"""

        response = self.call_orchestrator_model(prompt)
        framework = self._parse_framework_response(response)

        # 如果解析失败，使用增强的默认框架
        if not framework or "sections" not in framework:
            framework = self._get_enhanced_default_framework(topic, max_depth)

        return framework

    def _get_enhanced_default_framework(self, topic: str, max_depth: int = 6) -> Dict[str, Any]:
        """获取增强的默认框�?""
        return {
            "title": f"{topic}产业研究报告",
            "sections": [
                {
                    "title": "行业概述与现状分�?,
                    "level": 1,
                    "children": [
                        {"title": "行业定义与分�?, "level": 2, "children": []},
                        {"title": "发展历程回顾", "level": 2, "children": []},
                        {"title": "产业链结构分�?, "level": 2, "children": []},
                        {"title": "市场规模与增�?, "level": 2, "children": []}
                    ]
                },
                {
                    "title": "市场分析与竞争格局",
                    "level": 1,
                    "children": [
                        {"title": "市场规模统计", "level": 2, "children": []},
                        {"title": "细分市场分析", "level": 2, "children": []},
                        {"title": "主要企业分析", "level": 2, "children": []},
                        {"title": "竞争态势评估", "level": 2, "children": []}
                    ]
                },
                {
                    "title": "技术发展与创新趋势",
                    "level": 1,
                    "children": [
                        {"title": "核心技术分�?, "level": 2, "children": []},
                        {"title": "技术创新动�?, "level": 2, "children": []},
                        {"title": "研发投入情况", "level": 2, "children": []},
                        {"title": "技术发展路�?, "level": 2, "children": []}
                    ]
                },
                {
                    "title": "政策环境与法规标�?,
                    "level": 1,
                    "children": [
                        {"title": "政策支持体系", "level": 2, "children": []},
                        {"title": "法规标准现状", "level": 2, "children": []},
                        {"title": "监管政策影响", "level": 2, "children": []},
                        {"title": "政策趋势预测", "level": 2, "children": []}
                    ]
                },
                {
                    "title": "投资与融资分�?,
                    "level": 1,
                    "children": [
                        {"title": "投资规模统计", "level": 2, "children": []},
                        {"title": "融资事件分析", "level": 2, "children": []},
                        {"title": "投资热点领域", "level": 2, "children": []},
                        {"title": "资本市场表现", "level": 2, "children": []}
                    ]
                },
                {
                    "title": "发展趋势与前景预�?,
                    "level": 1,
                    "children": [
                        {"title": "短期发展趋势", "level": 2, "children": []},
                        {"title": "中长期前�?, "level": 2, "children": []},
                        {"title": "市场机遇分析", "level": 2, "children": []},
                        {"title": "风险挑战评估", "level": 2, "children": []}
                    ]
                },
                {
                    "title": "结论与建�?,
                    "level": 1,
                    "children": [
                        {"title": "主要结论总结", "level": 2, "children": []},
                        {"title": "发展建议", "level": 2, "children": []},
                        {"title": "投资建议", "level": 2, "children": []},
                        {"title": "政策建议", "level": 2, "children": []}
                    ]
                }
            ]
        }

    def _parse_framework_response(self, response: str) -> Dict[str, Any]:
        """解析框架响应"""
        try:
            import json
            import re

            # 尝试直接解析JSON
            try:
                return json.loads(response)
            except:
                pass

            # 尝试提取JSON部分
            json_match = re.search(r'\{.*\}', response, re.DOTALL)
            if json_match:
                try:
                    return json.loads(json_match.group())
                except:
                    pass

            # 如果都失败，返回空字�?            return {}

        except Exception as e:
            print(f"⚠️ 框架解析失败: {str(e)}")
            return {}

    def _generate_complete_substructure(self, framework: Dict[str, Any], topic: str) -> Dict[str, Any]:
        """为每个一级标题生成完整的子标题结构（2-5级）"""
        max_depth = self.report_config.get("max_depth", 5)

        print(f"   🌳 为每个一级标题生成{max_depth}级子结构")

        sections = framework.get("sections", [])
        for i, section in enumerate(sections):
            print(f"   📝 处理第{i+1}个一级标�? {section.get('title', '')}")

            # 为每个一级标题生成子结构
            section = self._generate_section_substructure(section, topic, max_depth)
            sections[i] = section

        framework["sections"] = sections
        return framework

    def _generate_section_substructure(self, section: Dict[str, Any], topic: str, max_depth: int) -> Dict[str, Any]:
        """为单个章节生成子结构"""
        section_title = section.get("title", "")

        prompt = f"""
您是一位资深的产业研究专家，需要为"{topic}"研究报告中的章节"{section_title}"设计详细的子标题结构�?
要求�?1. 生成2-{max_depth}级的详细子标�?2. 确保逻辑清晰、层次分�?3. 每个子标题都要有实际的研究价�?4. 符合产业研究报告的专业标�?5. 返回JSON格式

请返回以下JSON格式�?{{
    "title": "{section_title}",
    "level": 1,
    "children": [
        {{
            "title": "二级标题",
            "level": 2,
            "children": [
                {{
                    "title": "三级标题",
                    "level": 3,
                    "children": []
                }}
            ]
        }}
    ]
}}
"""

        response = self.call_orchestrator_model(prompt)
        parsed_section = self._parse_framework_response(response)

        if parsed_section and "children" in parsed_section:
            section["children"] = parsed_section["children"]

        return section

    async def _generate_complete_substructure_async(self, framework: Dict[str, Any], topic: str) -> Dict[str, Any]:
        """异步版本：为每个一级标题生成完整的子标题结�?""
        max_depth = self.report_config.get("max_depth", 5)

        print(f"   🌳 异步为每个一级标题生成{max_depth}级子结构")

        sections = framework.get("sections", [])

        # 创建异步任务
        tasks = []
        for i, section in enumerate(sections):
            task = self._generate_section_substructure_async(section, topic, max_depth, i+1)
            tasks.append(task)

        # 并行执行
        updated_sections = await asyncio.gather(*tasks)

        framework["sections"] = updated_sections
        return framework

    async def _generate_section_substructure_async(self, section: Dict[str, Any], topic: str, max_depth: int, section_num: int) -> Dict[str, Any]:
        """异步版本：为单个章节生成子结�?""
        section_title = section.get("title", "")
        print(f"   📝 异步处理第{section_num}个一级标�? {section_title}")

        prompt = f"""
您是一位资深的产业研究专家，需要为"{topic}"研究报告中的章节"{section_title}"设计详细的子标题结构�?
要求�?1. 生成2-{max_depth}级的详细子标�?2. 确保逻辑清晰、层次分�?3. 每个子标题都要有实际的研究价�?4. 符合产业研究报告的专业标�?5. 返回JSON格式

请返回以下JSON格式�?{{
    "title": "{section_title}",
    "level": 1,
    "children": [
        {{
            "title": "二级标题",
            "level": 2,
            "children": [
                {{
                    "title": "三级标题",
                    "level": 3,
                    "children": []
                }}
            ]
        }}
    ]
}}
"""

        response = await self.call_orchestrator_model_async(prompt)
        parsed_section = self._parse_framework_response(response)

        if parsed_section and "children" in parsed_section:
            section["children"] = parsed_section["children"]

        return section

    def read_framework_file(self, framework_file_path: str) -> str:
        """读取框架文件"""
        try:
            framework_path = Path(framework_file_path)
            if framework_path.exists():
                with open(framework_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                print(f"�?框架文件读取成功: {framework_file_path}")
                return content
            else:
                print(f"⚠️ 框架文件不存�? {framework_file_path}")
                return ""
        except Exception as e:
            print(f"�?框架文件读取失败: {str(e)}")
            return ""

    # ==================== 内容生成核心方法 ====================

    def _generate_all_content_with_data(self, framework: Dict[str, Any], processed_data: Dict[str, Any]) -> Dict[str, Any]:
        """生成所有节点内容（同步版本�?""
        print("   ✍️ 开始生成所有节点内�?)

        sections = framework.get("sections", [])
        data_content = processed_data.get("text_content", "")

        for i, section in enumerate(sections):
            print(f"   📝 生成第{i+1}个章�? {section.get('title', '')}")
            section = self._generate_section_content_recursive(section, data_content, framework.get("title", ""))
            sections[i] = section

        framework["sections"] = sections
        return framework

    async def _generate_all_content_with_data_async(self, framework: Dict[str, Any], processed_data: Dict[str, Any]) -> Dict[str, Any]:
        """异步生成所有节点内�?""
        print("   ✍️ 异步开始生成所有节点内�?)

        sections = framework.get("sections", [])
        data_content = processed_data.get("text_content", "")

        # 收集所有需要生成内容的节点
        all_nodes = []
        for i, section in enumerate(sections):
            nodes = self._collect_all_nodes(section)
            for node in nodes:
                all_nodes.append((node, i, data_content, framework.get("title", "")))

        print(f"   📊 总共需要生�?{len(all_nodes)} 个节点的内容")

        # 分批并行处理
        batch_size = min(10, len(all_nodes))  # 每批最�?0�?
        for i in range(0, len(all_nodes), batch_size):
            batch = all_nodes[i:i+batch_size]
            print(f"   🔄 处理�?{i//batch_size + 1} 批，�?{len(batch)} 个节�?)

            # 创建异步任务
            tasks = []
            for node, section_idx, data_content, topic in batch:
                task = self._generate_node_content_async(node, data_content, topic)
                tasks.append(task)

            # 并行执行
            await asyncio.gather(*tasks)

        framework["sections"] = sections
        return framework

    def _collect_all_nodes(self, section: Dict[str, Any]) -> List[Dict[str, Any]]:
        """收集所有节�?""
        nodes = [section]

        def collect_recursive(node):
            children = node.get("children", [])
            for child in children:
                nodes.append(child)
                collect_recursive(child)

        collect_recursive(section)
        return nodes

    def _generate_section_content_recursive(self, section: Dict[str, Any], data_content: str, topic: str) -> Dict[str, Any]:
        """递归生成章节内容"""
        # 生成当前节点内容
        section = self._generate_single_node_content(section, data_content, topic)

        # 递归处理子节�?        children = section.get("children", [])
        for i, child in enumerate(children):
            children[i] = self._generate_section_content_recursive(child, data_content, topic)

        section["children"] = children
        return section

    def _generate_single_node_content(self, node: Dict[str, Any], data_content: str, topic: str) -> Dict[str, Any]:
        """生成单个节点的内�?""
        title = node.get("title", "")
        level = node.get("level", 1)

        # 根据层级确定内容长度
        target_words = self._calculate_target_words_by_level(level)

        prompt = f"""
您是一位资深的产业研究专家，正在撰写关�?{topic}"的专业研究报告�?
当前需要撰写的章节：{title}（第{level}级标题）

参考数据：
{data_content[:5000] if data_content else "无特定数据，请基于专业知识撰�?}

要求�?1. 内容专业、准确、有深度
2. 目标字数：约{target_words}�?3. 包含具体的数据、案例和分析
4. 符合产业研究报告的专业标�?5. 逻辑清晰，论证充�?
请直接输出章节内容，不要包含标题�?"""

        try:
            content = self.call_executor_model(prompt)

            # 清理内容
            content = self.content_cleaner.clean_content_thoroughly(content)

            # 确保内容不为�?            if not content or len(content.strip()) < 50:
                content = self._generate_fallback_content_for_node(title, level, topic)

            node["content"] = content
            node["word_count"] = len(content)

        except Exception as e:
            print(f"⚠️ 生成节点内容失败 {title}: {str(e)}")
            node["content"] = self._generate_fallback_content_for_node(title, level, topic)
            node["word_count"] = len(node["content"])

        return node

    async def _generate_node_content_async(self, node: Dict[str, Any], data_content: str, topic: str) -> Dict[str, Any]:
        """异步生成单个节点的内�?""
        title = node.get("title", "")
        level = node.get("level", 1)

        # 根据层级确定内容长度
        target_words = self._calculate_target_words_by_level(level)

        prompt = f"""
您是一位资深的产业研究专家，正在撰写关�?{topic}"的专业研究报告�?
当前需要撰写的章节：{title}（第{level}级标题）

参考数据：
{data_content[:5000] if data_content else "无特定数据，请基于专业知识撰�?}

要求�?1. 内容专业、准确、有深度
2. 目标字数：约{target_words}�?3. 包含具体的数据、案例和分析
4. 符合产业研究报告的专业标�?5. 逻辑清晰，论证充�?
请直接输出章节内容，不要包含标题�?"""

        try:
            content = await self.call_executor_model_async(prompt)

            # 清理内容
            content = self.content_cleaner.clean_content_thoroughly(content)

            # 确保内容不为�?            if not content or len(content.strip()) < 50:
                content = self._generate_fallback_content_for_node(title, level, topic)

            node["content"] = content
            node["word_count"] = len(content)

        except Exception as e:
            print(f"⚠️ 异步生成节点内容失败 {title}: {str(e)}")
            node["content"] = self._generate_fallback_content_for_node(title, level, topic)
            node["word_count"] = len(node["content"])

        return node

    def _calculate_target_words_by_level(self, level: int) -> int:
        """根据层级计算目标字数"""
        base_words = {
            1: 2000,  # 一级标�?            2: 1000,  # 二级标题
            3: 600,   # 三级标题
            4: 400,   # 四级标题
            5: 300,   # 五级标题
            6: 200    # 六级标题
        }
        return base_words.get(level, 300)

    def _generate_fallback_content_for_node(self, title: str, level: int, topic: str) -> str:
        """为节点生成备用内�?""
        target_words = self._calculate_target_words_by_level(level)

        return f"""
## {title}

本章节将深入分析{topic}相关的{title}内容�?
### 主要内容要点

1. **现状分析**
   - 当前发展状况
   - 主要特征分析
   - 存在的问�?
2. **深入研究**
   - 核心要素分析
   - 关键影响因素
   - 发展规律总结

3. **趋势预测**
   - 未来发展方向
   - 潜在机遇
   - 面临挑战

### 详细分析

{title}作为{topic}的重要组成部分，在整个产业发展中发挥着关键作用。通过深入研究可以发现，该领域正处于快速发展阶段，呈现出以下特点：

**发展现状**：当前{title}领域发展迅速，市场规模不断扩大，技术水平持续提升，产业结构日趋完善�?
**核心要素**：影响{title}发展的核心要素包括技术创新、市场需求、政策支持、资本投入等多个方面�?
**发展趋势**：未来{title}将朝着更加专业化、智能化、绿色化的方向发展，为{topic}产业带来新的增长动力�?
### 结论与建�?
基于以上分析，建议相关企业和机构应当�?1. 加强技术创新投�?2. 优化产业布局
3. 完善服务体系
4. 提升竞争能力

（注：本内容为系统生成的框架性内容，实际报告中将包含更详细的数据分析和专业见解）
"""[:target_words]

    # ==================== 迭代优化系统 ====================

    def _iterative_optimization(self, framework: Dict[str, Any], data_sources: List[str], topic: str) -> Dict[str, Any]:
        """3轮迭代优化（同步版本�?""
        print("   🔧 开�?轮迭代优�?)

        for iteration in range(1, 4):
            print(f"\n   🔄 第{iteration}轮优�?)

            if iteration == 1:
                # 第一轮：内容平衡优化
                framework = self._balance_content_consistency(framework, topic)
            elif iteration == 2:
                # 第二轮：逻辑结构优化
                framework = self._optimize_logic_structure(framework, topic)
            elif iteration == 3:
                # 第三轮：质量提升优化
                framework = self.optimizer._enhance_content_quality(framework, topic)

            print(f"   �?第{iteration}轮优化完�?)

        return framework

    async def _iterative_optimization_async(self, framework: Dict[str, Any], data_sources: List[str], topic: str) -> Dict[str, Any]:
        """3轮迭代优化（异步版本�?""
        print("   🔧 异步开�?轮迭代优�?)

        for iteration in range(1, 4):
            print(f"\n   🔄 第{iteration}轮异步优�?)

            if iteration == 1:
                # 第一轮：内容平衡优化
                framework = await self._balance_content_consistency_async(framework, topic)
            elif iteration == 2:
                # 第二轮：逻辑结构优化
                framework = await self._optimize_logic_structure_async(framework, topic)
            elif iteration == 3:
                # 第三轮：质量提升优化
                framework = await self.optimizer._enhance_content_quality_async(framework, topic)

            print(f"   �?第{iteration}轮异步优化完�?)

        return framework

    def _balance_content_consistency(self, framework: Dict[str, Any], topic: str) -> Dict[str, Any]:
        """第一轮：内容平衡优化"""
        print("     📊 内容平衡优化")

        sections = framework.get("sections", [])

        # 分析全文内容
        analysis = self._analyze_content_balance(sections, topic)

        # 优化每个章节
        for i, section in enumerate(sections):
            print(f"     📝 优化第{i+1}个章�? {section.get('title', '')}")
            section = self._balance_section_content(section, analysis, topic)
            sections[i] = section

        framework["sections"] = sections
        return framework

    async def _balance_content_consistency_async(self, framework: Dict[str, Any], topic: str) -> Dict[str, Any]:
        """第一轮：异步内容平衡优化"""
        print("     📊 异步内容平衡优化")

        sections = framework.get("sections", [])

        # 分析全文内容
        analysis = self._analyze_content_balance(sections, topic)

        # 创建异步任务
        tasks = []
        for i, section in enumerate(sections):
            task = self._balance_section_content_async(section, analysis, topic, i+1)
            tasks.append(task)

        # 并行执行
        optimized_sections = await asyncio.gather(*tasks)

        framework["sections"] = optimized_sections
        return framework

    def _analyze_content_balance(self, sections: List[Dict[str, Any]], topic: str) -> Dict[str, Any]:
        """分析内容平衡�?""
        total_words = 0
        section_words = []

        for section in sections:
            words = self._count_section_words(section)
            section_words.append(words)
            total_words += words

        avg_words = total_words / len(sections) if sections else 0

        return {
            "total_words": total_words,
            "avg_words_per_section": avg_words,
            "section_words": section_words,
            "imbalance_threshold": avg_words * 0.3  # 30%的差异阈�?        }

    def _count_section_words(self, section: Dict[str, Any]) -> int:
        """统计章节字数"""
        words = len(section.get("content", ""))

        # 递归统计子章�?        for child in section.get("children", []):
            words += self._count_section_words(child)

        return words

    def _balance_section_content(self, section: Dict[str, Any], analysis: Dict[str, Any], topic: str) -> Dict[str, Any]:
        """平衡单个章节内容"""
        section_words = self._count_section_words(section)
        avg_words = analysis["avg_words_per_section"]
        threshold = analysis["imbalance_threshold"]

        if abs(section_words - avg_words) > threshold:
            # 需要调整内容长�?            if section_words < avg_words - threshold:
                # 内容太少，需要扩�?                section = self._expand_section_content(section, topic)
            elif section_words > avg_words + threshold:
                # 内容太多，需要精简
                section = self._compress_section_content(section, topic)

        # 递归处理子章�?        children = section.get("children", [])
        for i, child in enumerate(children):
            children[i] = self._balance_section_content(child, analysis, topic)

        section["children"] = children
        return section

    async def _balance_section_content_async(self, section: Dict[str, Any], analysis: Dict[str, Any], topic: str, section_num: int) -> Dict[str, Any]:
        """异步平衡单个章节内容"""
        print(f"     📝 异步优化第{section_num}个章�? {section.get('title', '')}")

        section_words = self._count_section_words(section)
        avg_words = analysis["avg_words_per_section"]
        threshold = analysis["imbalance_threshold"]

        if abs(section_words - avg_words) > threshold:
            # 需要调整内容长�?            if section_words < avg_words - threshold:
                # 内容太少，需要扩�?                section = await self._expand_section_content_async(section, topic)
            elif section_words > avg_words + threshold:
                # 内容太多，需要精简
                section = await self._compress_section_content_async(section, topic)

        # 递归处理子章�?        children = section.get("children", [])
        child_tasks = []
        for child in children:
            task = self._balance_section_content_async(child, analysis, topic, section_num)
            child_tasks.append(task)

        if child_tasks:
            optimized_children = await asyncio.gather(*child_tasks)
            section["children"] = optimized_children

        return section

    def _expand_section_content(self, section: Dict[str, Any], topic: str) -> Dict[str, Any]:
        """扩展章节内容"""
        title = section.get("title", "")
        current_content = section.get("content", "")

        prompt = f"""
您是一位资深的产业研究专家，需要扩�?{topic}"研究报告�?{title}"章节的内容�?
当前内容�?{current_content}

要求�?1. 在现有内容基础上进行扩�?2. 增加更多具体的数据、案例和分析
3. 保持内容的专业性和逻辑�?4. 扩展后的内容应该比原内容增加50%以上
5. 不要重复现有内容

请输出扩展后的完整内容：
"""

        try:
            expanded_content = self.call_orchestrator_model(prompt)
            if expanded_content and len(expanded_content) > len(current_content):
                section["content"] = self.content_cleaner.clean_content_thoroughly(expanded_content)
        except Exception as e:
            print(f"⚠️ 扩展内容失败 {title}: {str(e)}")

        return section

    async def _expand_section_content_async(self, section: Dict[str, Any], topic: str) -> Dict[str, Any]:
        """异步扩展章节内容"""
        title = section.get("title", "")
        current_content = section.get("content", "")

        prompt = f"""
您是一位资深的产业研究专家，需要扩�?{topic}"研究报告�?{title}"章节的内容�?
当前内容�?{current_content}

要求�?1. 在现有内容基础上进行扩�?2. 增加更多具体的数据、案例和分析
3. 保持内容的专业性和逻辑�?4. 扩展后的内容应该比原内容增加50%以上
5. 不要重复现有内容

请输出扩展后的完整内容：
"""

        try:
            expanded_content = await self.call_orchestrator_model_async(prompt)
            if expanded_content and len(expanded_content) > len(current_content):
                section["content"] = self.content_cleaner.clean_content_thoroughly(expanded_content)
        except Exception as e:
            print(f"⚠️ 异步扩展内容失败 {title}: {str(e)}")

        return section

    def _compress_section_content(self, section: Dict[str, Any], topic: str) -> Dict[str, Any]:
        """压缩章节内容"""
        title = section.get("title", "")
        current_content = section.get("content", "")

        prompt = f"""
您是一位资深的产业研究专家，需要精简"{topic}"研究报告�?{title}"章节的内容�?
当前内容�?{current_content}

要求�?1. 保留最核心、最重要的内�?2. 删除冗余和重复的表述
3. 保持内容的专业性和完整�?4. 精简后的内容应该比原内容减少30%左右
5. 确保逻辑清晰，重点突�?
请输出精简后的内容�?"""

        try:
            compressed_content = self.call_orchestrator_model(prompt)
            if compressed_content and len(compressed_content) < len(current_content):
                section["content"] = self.content_cleaner.clean_content_thoroughly(compressed_content)
        except Exception as e:
            print(f"⚠️ 压缩内容失败 {title}: {str(e)}")

        return section

    async def _compress_section_content_async(self, section: Dict[str, Any], topic: str) -> Dict[str, Any]:
        """异步压缩章节内容"""
        title = section.get("title", "")
        current_content = section.get("content", "")

        prompt = f"""
您是一位资深的产业研究专家，需要精简"{topic}"研究报告�?{title}"章节的内容�?
当前内容�?{current_content}

要求�?1. 保留最核心、最重要的内�?2. 删除冗余和重复的表述
3. 保持内容的专业性和完整�?4. 精简后的内容应该比原内容减少30%左右
5. 确保逻辑清晰，重点突�?
请输出精简后的内容�?"""

        try:
            compressed_content = await self.call_orchestrator_model_async(prompt)
            if compressed_content and len(compressed_content) < len(current_content):
                section["content"] = self.content_cleaner.clean_content_thoroughly(compressed_content)
        except Exception as e:
            print(f"⚠️ 异步压缩内容失败 {title}: {str(e)}")

        return section

    def _optimize_logic_structure(self, framework: Dict[str, Any], topic: str) -> Dict[str, Any]:
        """第二轮：逻辑结构优化"""
        print("     🔗 逻辑结构优化")

        sections = framework.get("sections", [])

        for i, section in enumerate(sections):
            print(f"     📝 优化第{i+1}个章节逻辑: {section.get('title', '')}")
            section = self._optimize_section_logic(section, topic, sections)
            sections[i] = section

        framework["sections"] = sections
        return framework

    async def _optimize_logic_structure_async(self, framework: Dict[str, Any], topic: str) -> Dict[str, Any]:
        """第二轮：异步逻辑结构优化"""
        print("     🔗 异步逻辑结构优化")

        sections = framework.get("sections", [])

        # 创建异步任务
        tasks = []
        for i, section in enumerate(sections):
            task = self._optimize_section_logic_async(section, topic, sections, i+1)
            tasks.append(task)

        # 并行执行
        optimized_sections = await asyncio.gather(*tasks)

        framework["sections"] = optimized_sections
        return framework

    def _optimize_section_logic(self, section: Dict[str, Any], topic: str, all_sections: List[Dict[str, Any]]) -> Dict[str, Any]:
        """优化单个章节的逻辑结构"""
        title = section.get("title", "")
        content = section.get("content", "")

        # 获取上下文信�?        context = self._get_section_context(section, all_sections)

        prompt = f"""
您是一位资深的产业研究专家，需要优�?{topic}"研究报告�?{title}"章节的逻辑结构�?
当前内容�?{content}

上下文信息：
{context}

要求�?1. 优化内容的逻辑结构和表述方�?2. 确保与前后章节的逻辑连贯�?3. 加强论证的严密性和说服�?4. 保持内容的专业性和准确�?5. 不改变核心观点和主要内容

请输出优化后的内容：
"""

        try:
            optimized_content = self.call_orchestrator_model(prompt)
            if optimized_content:
                section["content"] = self.content_cleaner.clean_content_thoroughly(optimized_content)
        except Exception as e:
            print(f"⚠️ 逻辑优化失败 {title}: {str(e)}")

        return section

    async def _optimize_section_logic_async(self, section: Dict[str, Any], topic: str, all_sections: List[Dict[str, Any]], section_num: int) -> Dict[str, Any]:
        """异步优化单个章节的逻辑结构"""
        print(f"     📝 异步优化第{section_num}个章节逻辑: {section.get('title', '')}")

        title = section.get("title", "")
        content = section.get("content", "")

        # 获取上下文信�?        context = self._get_section_context(section, all_sections)

        prompt = f"""
您是一位资深的产业研究专家，需要优�?{topic}"研究报告�?{title}"章节的逻辑结构�?
当前内容�?{content}

上下文信息：
{context}

要求�?1. 优化内容的逻辑结构和表述方�?2. 确保与前后章节的逻辑连贯�?3. 加强论证的严密性和说服�?4. 保持内容的专业性和准确�?5. 不改变核心观点和主要内容

请输出优化后的内容：
"""

        try:
            optimized_content = await self.call_orchestrator_model_async(prompt)
            if optimized_content:
                section["content"] = self.content_cleaner.clean_content_thoroughly(optimized_content)
        except Exception as e:
            print(f"⚠️ 异步逻辑优化失败 {title}: {str(e)}")

        return section

    def _get_section_context(self, current_section: Dict[str, Any], all_sections: List[Dict[str, Any]]) -> str:
        """获取章节上下文信�?""
        current_title = current_section.get("title", "")
        context_parts = []

        # 找到当前章节的位�?        current_index = -1
        for i, section in enumerate(all_sections):
            if section.get("title") == current_title:
                current_index = i
                break

        # 添加前一章节信息
        if current_index > 0:
            prev_section = all_sections[current_index - 1]
            context_parts.append(f"前一章节：{prev_section.get('title', '')}")

        # 添加后一章节信息
        if current_index < len(all_sections) - 1:
            next_section = all_sections[current_index + 1]
            context_parts.append(f"后一章节：{next_section.get('title', '')}")

        return "\n".join(context_parts) if context_parts else "无特定上下文"

    # ==================== 搜索增强系统 ====================

    def _enhance_with_search(self, framework: Dict[str, Any], topic: str) -> Dict[str, Any]:
        """使用搜索增强报告内容"""
        print("     🔍 开始搜索增�?)

        try:
            # 分析内容缺口
            content_gaps = self._analyze_all_content_gaps(framework, topic)

            if not content_gaps:
                print("     �?未发现内容缺口，跳过搜索增强")
                return framework

            print(f"     📊 发现 {len(content_gaps)} 个内容缺�?)

            # 执行搜索增强
            framework = self._execute_search_enhancement_for_framework(framework, content_gaps, topic)

            print("     �?搜索增强完成")
            return framework

        except Exception as e:
            print(f"⚠️ 搜索增强失败: {str(e)}")
            return framework

    def _analyze_all_content_gaps(self, framework: Dict[str, Any], topic: str) -> List[Dict[str, Any]]:
        """分析所有内容缺�?""
        all_gaps = []

        def analyze_section_gaps(section):
            content = section.get("content", "")
            title = section.get("title", "")

            if content:
                # 使用搜索触发器分析缺�?                gaps = self.search_trigger.analyze_content_gaps(content, topic)
                for gap in gaps:
                    gap["section_title"] = title
                    gap["section"] = section
                all_gaps.extend(gaps)

            # 递归处理子章�?            for child in section.get("children", []):
                analyze_section_gaps(child)

        for section in framework.get("sections", []):
            analyze_section_gaps(section)

        return all_gaps

    def _execute_search_enhancement_for_framework(self, framework: Dict[str, Any], content_gaps: List[Dict[str, Any]], topic: str) -> Dict[str, Any]:
        """为框架执行搜索增�?""
        # 按章节分组缺�?        gaps_by_section = {}
        for gap in content_gaps:
            section_title = gap.get("section_title", "")
            if section_title not in gaps_by_section:
                gaps_by_section[section_title] = []
            gaps_by_section[section_title].append(gap)

        # 为每个有缺口的章节执行搜索增�?        for section_title, gaps in gaps_by_section.items():
            print(f"     📝 增强章节: {section_title}")
            self._enhance_section_with_search(gaps, topic)

        return framework

    def _enhance_section_with_search(self, gaps: List[Dict[str, Any]], topic: str):
        """为章节执行搜索增�?""
        for gap in gaps:
            try:
                # 生成搜索查询
                queries = self.search_manager.generate_search_queries(topic, gap)

                # 执行搜索
                search_results = []
                for query in queries[:3]:  # 限制查询数量
                    results = self.search_manager.multi_source_search(query, num_results=3)
                    search_results.extend(results)

                if search_results:
                    # 验证和过滤结�?                    validated_results = []
                    for result in search_results:
                        if self.content_validator.validate_search_result(result, topic):
                            validated_results.append(result)

                    if validated_results:
                        # 整合搜索结果到原内容
                        section = gap.get("section")
                        if section:
                            enhanced_content = self.content_integrator.integrate_search_results(
                                section.get("content", ""), validated_results, topic, [gap]
                            )
                            section["content"] = enhanced_content
                            print(f"       �?章节增强完成，添加了 {len(validated_results)} 个搜索结�?)

            except Exception as e:
                print(f"       ⚠️ 章节搜索增强失败: {str(e)}")

    # ==================== 图片嵌入系统 ====================

    def _embed_all_images(self, framework: Dict[str, Any], image_data: Dict[str, Any]) -> Dict[str, Any]:
        """嵌入所有图�?""
        if not image_data:
            print("     🖼�?无图片数据，跳过图片嵌入")
            return framework

        print(f"     🖼�?开始嵌�?{len(image_data)} 张图�?)

        try:
            # 分析图片匹配
            image_matches = self._analyze_image_matches(framework, image_data)

            if image_matches:
                # 嵌入图片标记
                framework = self._embed_image_markers(framework, image_matches)
                print(f"     �?成功嵌入 {len(image_matches)} 张图�?)
            else:
                print("     ⚠️ 未找到合适的图片匹配")

            return framework

        except Exception as e:
            print(f"⚠️ 图片嵌入失败: {str(e)}")
            return framework

    def _analyze_image_matches(self, framework: Dict[str, Any], image_data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """分析图片匹配"""
        matches = []

        # 收集所有文本内�?        all_content = self._collect_all_content(framework)

        # 为每张图片寻找最佳匹配位�?        for image_id, image_info in image_data.items():
            match = self._find_best_match_for_image(image_info, all_content, framework)
            if match:
                match["image_id"] = image_id
                match["image_info"] = image_info
                matches.append(match)

        return matches

    def _collect_all_content(self, framework: Dict[str, Any]) -> str:
        """收集所有文本内�?""
        all_content = []

        def collect_recursive(node):
            content = node.get("content", "")
            if content:
                all_content.append(content)

            for child in node.get("children", []):
                collect_recursive(child)

        for section in framework.get("sections", []):
            collect_recursive(section)

        return "\n\n".join(all_content)

    def _find_best_match_for_image(self, image_info: Dict[str, Any], all_content: str, framework: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """为图片找到最佳匹配位�?""
        image_desc = image_info.get("description", "")

        # 使用Gemini分析最佳插入位�?        prompt = f"""
您是一位专业的报告编辑，需要为一张图片找到最佳的插入位置�?
图片描述：{image_desc}

报告内容�?{all_content[:5000]}  # 限制长度

要求�?1. 分析图片与报告内容的相关�?2. 找到最适合插入图片的段�?3. 给出插入的理�?4. 返回JSON格式

请返回以下JSON格式�?{{
    "best_match": true/false,
    "relevance_score": 0.0-1.0,
    "insert_position": "段落描述",
    "reason": "插入理由"
}}
"""

        try:
            response = self.call_orchestrator_model(prompt)
            match_info = self._parse_framework_response(response)

            if match_info and match_info.get("best_match") and match_info.get("relevance_score", 0) > 0.6:
                return match_info

        except Exception as e:
            print(f"⚠️ 图片匹配分析失败: {str(e)}")

        return None

    def _embed_image_markers(self, framework: Dict[str, Any], image_matches: List[Dict[str, Any]]) -> Dict[str, Any]:
        """嵌入图片标记"""
        def embed_in_section(section):
            content = section.get("content", "")

            # 为该章节找到相关的图�?            section_title = section.get("title", "")
            relevant_images = [match for match in image_matches
                             if section_title in match.get("insert_position", "")]

            if relevant_images:
                # 在内容中插入图片标记
                for match in relevant_images:
                    image_id = match["image_id"]
                    image_info = match["image_info"]

                    # 创建图片标记
                    image_marker = f"\n\n[IMAGE:{image_id}:{image_info.get('description', '')}]\n\n"

                    # 插入到内容中（简单实现：添加到末尾）
                    content += image_marker

                section["content"] = content

            # 递归处理子章�?            for child in section.get("children", []):
                embed_in_section(child)

        for section in framework.get("sections", []):
            embed_in_section(section)

        return framework

    # ==================== 文档生成系统 ====================

    def _generate_final_document(self, topic: str, framework: Dict[str, Any], processed_data: Dict[str, Any]) -> str:
        """生成最终文�?""
        print("     📄 生成最终文�?)

        try:
            # 创建输出目录
            output_dir = Path("output")
            output_dir.mkdir(exist_ok=True)

            # 生成文件�?            import time
            timestamp = int(time.time())
            safe_topic = "".join(c for c in topic if c.isalnum() or c in (' ', '-', '_')).rstrip()
            filename = f"{safe_topic}_{timestamp}"

            # 生成多种格式
            outputs = {}

            # 1. Markdown格式
            md_path = output_dir / f"{filename}.md"
            self._save_as_markdown(topic, framework, str(md_path))
            outputs["markdown"] = str(md_path)

            # 2. Word格式（如果可能）
            try:
                docx_path = output_dir / f"{filename}.docx"
                self._save_as_word(topic, framework, str(docx_path))
                outputs["word"] = str(docx_path)
            except Exception as e:
                print(f"⚠️ Word格式生成失败: {str(e)}")

            # 3. 文本格式
            txt_path = output_dir / f"{filename}.txt"
            self._save_as_text(topic, framework, str(txt_path))
            outputs["text"] = str(txt_path)

            # 返回主要输出路径
            main_output = outputs.get("word", outputs.get("markdown", outputs.get("text")))

            print(f"     �?文档生成完成:")
            for format_name, path in outputs.items():
                print(f"       {format_name}: {path}")

            return main_output

        except Exception as e:
            print(f"�?文档生成失败: {str(e)}")
            raise

    def _save_as_markdown(self, topic: str, framework: Dict[str, Any], output_path: str):
        """保存为Markdown格式"""
        md_content = []
        md_content.append(f"# {topic}\n")

        def add_section_to_md(section: Dict[str, Any], level: int = 1):
            title = section.get("title", "")
            content = section.get("content", "")

            # 添加标题
            md_content.append(f"{'#' * (level + 1)} {title}\n")

            # 添加内容
            if content:
                # 处理图片标记
                processed_content = self._process_image_markers_for_markdown(content)
                md_content.append(f"{processed_content}\n")

            # 递归处理子章�?            for child in section.get("children", []):
                add_section_to_md(child, level + 1)

        for section in framework.get("sections", []):
            add_section_to_md(section)

        # 写入文件
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write("\n".join(md_content))

    def _save_as_word(self, topic: str, framework: Dict[str, Any], output_path: str):
        """保存为Word格式"""
        try:
            from docx import Document
            from docx.shared import Inches

            doc = Document()

            # 添加标题
            doc.add_heading(topic, 0)

            def add_section_to_doc(section: Dict[str, Any], level: int = 1):
                title = section.get("title", "")
                content = section.get("content", "")

                # 添加标题
                doc.add_heading(title, level)

                # 添加内容
                if content:
                    # 处理图片标记
                    processed_content = self._process_image_markers_for_word(content, doc)
                    if processed_content:
                        doc.add_paragraph(processed_content)

                # 递归处理子章�?                for child in section.get("children", []):
                    add_section_to_doc(child, min(level + 1, 9))  # Word最多支�?级标�?
            for section in framework.get("sections", []):
                add_section_to_doc(section)

            # 保存文档
            doc.save(output_path)

        except ImportError:
            print("⚠️ python-docx未安装，无法生成Word文档")
            raise
        except Exception as e:
            print(f"⚠️ Word文档生成失败: {str(e)}")
            raise

    def _save_as_text(self, topic: str, framework: Dict[str, Any], output_path: str):
        """保存为文本格�?""
        text_content = []
        text_content.append(f"{topic}")
        text_content.append("=" * len(topic) + "\n")

        def add_section_to_text(section: Dict[str, Any], level: int = 1):
            title = section.get("title", "")
            content = section.get("content", "")

            # 添加标题
            indent = "  " * (level - 1)
            text_content.append(f"{indent}{level}. {title}")
            text_content.append(f"{indent}{'-' * len(title)}")

            # 添加内容
            if content:
                # 处理图片标记
                processed_content = self._process_image_markers_for_text(content)
                # 添加缩进
                indented_content = "\n".join(f"{indent}  {line}" for line in processed_content.split("\n"))
                text_content.append(indented_content)

            text_content.append("")  # 空行

            # 递归处理子章�?            for child in section.get("children", []):
                add_section_to_text(child, level + 1)

        for section in framework.get("sections", []):
            add_section_to_text(section)

        # 写入文件
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write("\n".join(text_content))

    def _process_image_markers_for_markdown(self, content: str) -> str:
        """处理Markdown格式的图片标�?""
        import re

        def replace_image_marker(match):
            image_id = match.group(1)
            description = match.group(2)
            return f"\n![{description}](images/{image_id})\n*图：{description}*\n"

        # 替换图片标记
        pattern = r'\[IMAGE:([^:]+):([^\]]+)\]'
        return re.sub(pattern, replace_image_marker, content)

    def _process_image_markers_for_word(self, content: str, doc) -> str:
        """处理Word格式的图片标�?""
        import re

        def replace_image_marker(match):
            image_id = match.group(1)
            description = match.group(2)

            # 在Word中，我们暂时用文本描述替�?            return f"\n[图片：{description}]\n"

        # 替换图片标记
        pattern = r'\[IMAGE:([^:]+):([^\]]+)\]'
        return re.sub(pattern, replace_image_marker, content)

    def _process_image_markers_for_text(self, content: str) -> str:
        """处理文本格式的图片标�?""
        import re

        def replace_image_marker(match):
            image_id = match.group(1)
            description = match.group(2)
            return f"\n[图片：{description}]\n"

        # 替换图片标记
        pattern = r'\[IMAGE:([^:]+):([^\]]+)\]'
        return re.sub(pattern, replace_image_marker, content)

    # ==================== 主要生成接口 ====================

    def generate_report_with_features(self,
                       topic: str,
                       data_sources: List[str] = None,
                       target_words: int = 30000,
                       max_depth: int = 5,
                       enable_search: bool = True,
                       enable_images: bool = True,
                       framework_file: str = None,
                       reference_report: str = None) -> str:
        """
        带功能选项的报告生成接口（重命名避免冲突）

        Args:
            topic: 报告主题
            data_sources: 数据源列�?            target_words: 目标字数
            max_depth: 最大层级深�?            enable_search: 是否启用搜索增强
            enable_images: 是否启用图片嵌入
            framework_file: 框架文件路径
            reference_report: 参考报告路�?
        Returns:
            生成的报告文件路�?        """
        print(f"🚀 开始生成报�? {topic}")

        # 设置默认数据�?        if data_sources is None:
            data_sources = ["data/"]

        # 调用完整的生成流�?        return self.generate_complete_report_with_all_features(
            topic=topic,
            data_sources=data_sources,
            framework_file_path=framework_file,
            target_words=target_words,
            max_depth=max_depth,
            enable_search=enable_search,
            enable_images=enable_images,
            enable_reference_optimization=bool(reference_report),
            reference_report_path=reference_report
        )

    def _get_default_framework(self) -> Dict[str, Any]:
        """获取默认框架（兼容性方法）"""
        return self._get_enhanced_default_framework("默认主题", 5)

    # ==================== 委托给优化器的方�?====================

    def _optimize_with_reference_report(self, framework: Dict[str, Any], reference_path: str, topic: str) -> Dict[str, Any]:
        """委托给优化器的参考报告优�?""
        return self.optimizer._optimize_with_reference_report(framework, reference_path, topic)

    def _control_final_word_count(self, framework: Dict[str, Any], target_words: int, topic: str) -> Dict[str, Any]:
        """委托给优化器的字数控�?""
        return self.optimizer._control_final_word_count(framework, target_words, topic)

    async def _control_final_word_count_async(self, framework: Dict[str, Any], target_words: int, topic: str) -> Dict[str, Any]:
        """委托给优化器的异步字数控�?""
        return await self.optimizer._control_final_word_count_async(framework, target_words, topic)

    # ==================== 原代码中的重要方法补�?====================

    def generate_framework(self, topic: str, framework_content: str) -> Dict[str, Any]:
        """第一步：统筹模型生成报告框架（同步版本，支持预定义框架）"""

        # 检查是否有预定义框�?        predefined_framework = self.report_config.get("predefined_framework")

        if predefined_framework:
            print("📋 使用预定义框�?)
            print("�?严格按照预定义框架执行，禁止更改框架结构")

            # 验证预定义框�?            if self._validate_predefined_framework(predefined_framework, topic):
                self._print_framework_summary(predefined_framework)
                return predefined_framework
            else:
                print("�?预定义框架验证失败，使用AI生成框架")

        # 使用AI生成框架（原逻辑�?        print("🎯 第一步：统筹模型读取框架文件并生成报告框�?)

        # 获取动态配�?        primary_sections = self.report_config.get("primary_sections", 8)
        max_depth = self.report_config.get("max_depth", 6)

        print(f"   📊 使用动态配�? {primary_sections}个一级标题，最大{max_depth}级深�?)

        prompt = f"""
作为报告统筹模型，请为主�?{topic}"设计一份详细的研究报告框架�?
{f"请参考以下现有框架：\\n{framework_content}\\n" if framework_content else ""}

要求�?1. 必须包含恰好{primary_sections}个一级标题（对应{primary_sections}个数据源文件夹）
2. 每个一级标题下必须完整扩展到{max_depth}级子标题
3. 标题层级必须连贯，不能跳�?4. 每个标题都应该有明确的title字段和level字段

请以JSON格式返回完整的{max_depth}级结构，示例�?{{
    "sections": [
        {{
            "title": "一级标�?",
            "level": 1,
            "children": [
                {{
                    "title": "二级标题1.1",
                    "level": 2,
                    "children": [
                        {{
                            "title": "三级标题1.1.1",
                            "level": 3,
                            "children": [
                                {{
                                    "title": "四级标题*******",
                                    "level": 4,
                                    "children": []
                                }}
                            ]
                        }}
                    ]
                }}
            ]
        }}
    ]
}}

确保返回的是有效的JSON格式，包含完整的{max_depth}级标题结构�?"""

        response = self.call_orchestrator_model(prompt)

        try:
            # 尝试解析JSON
            framework = None

            if "```json" in response:
                start = response.find("```json") + 7
                end = response.find("```", start)
                if end != -1:
                    json_str = response[start:end].strip()
                    framework = json.loads(json_str)

            elif response.strip().startswith("{"):
                framework = json.loads(response.strip())

            # 验证框架结构
            if framework:
                validated_framework = self._validate_and_fix_framework(framework)
                if validated_framework:
                    return validated_framework

            print("⚠️ 无法解析框架JSON或结构不完整，使用默认框�?)
            return self._get_default_framework()

        except Exception as e:
            print(f"解析框架失败: {str(e)}")
            return self._get_default_framework()

    def _validate_predefined_framework(self, framework: Dict[str, Any], topic: str) -> bool:
        """验证预定义框�?""
        try:
            if not framework or "sections" not in framework:
                return False

            sections = framework["sections"]
            if not isinstance(sections, list) or len(sections) == 0:
                return False

            # 检查基本结�?            for section in sections:
                if not isinstance(section, dict) or "title" not in section:
                    return False

            return True
        except Exception:
            return False

    def _print_framework_summary(self, framework: Dict[str, Any]):
        """打印框架摘要"""
        try:
            sections = framework.get("sections", [])
            print(f"📋 框架摘要: {len(sections)} 个主要章�?)

            for i, section in enumerate(sections, 1):
                title = section.get("title", f"章节{i}")
                print(f"   {i}. {title}")
        except Exception as e:
            print(f"打印框架摘要失败: {str(e)}")

    def _validate_and_fix_framework(self, framework: Dict[str, Any]) -> Dict[str, Any]:
        """验证并修复框架结构，确保包含完整的指定级别标�?""
        if not framework or "sections" not in framework:
            print("�?框架结构无效，缺少sections字段")
            return None

        sections = framework["sections"]
        primary_sections = self.report_config.get("primary_sections", 8)
        max_depth = self.report_config.get("max_depth", 6)

        if not isinstance(sections, list) or len(sections) != primary_sections:
            print(f"�?框架结构无效，sections应该包含{primary_sections}个一级标题，实际: {len(sections) if isinstance(sections, list) else 'not list'}")
            return None

        # 检查每个section的深�?        def get_max_depth(node, current_depth=1):
            max_depth = current_depth
            if "children" in node and isinstance(node["children"], list):
                for child in node["children"]:
                    child_depth = get_max_depth(child, current_depth + 1)
                    max_depth = max(max_depth, child_depth)
            return max_depth

        # 检查是否所有section都有足够的深�?        valid_sections = 0
        for section in sections:
            depth = get_max_depth(section)
            if depth >= max_depth:
                valid_sections += 1

        if valid_sections < len(sections) // 2:  # 至少一半的section要有足够深度
            print(f"�?框架深度不足，需要{max_depth}级，但大部分section深度不够")
            return None

        # 添加level字段
        def add_levels(node, level=1):
            node["level"] = level
            if "children" in node and isinstance(node["children"], list):
                for child in node["children"]:
                    add_levels(child, level + 1)

        for section in sections:
            add_levels(section)

        print(f"�?框架验证通过: {len(sections)}个一级标题，最大深度{max_depth}�?)
        return framework

    def _get_default_framework(self) -> Dict[str, Any]:
        """获取默认框架（增强版�?""
        primary_sections = self.report_config.get("primary_sections", 8)
        max_depth = self.report_config.get("max_depth", 6)

        # 根据配置生成默认框架
        default_sections = [
            "行业概述与现状分�?,
            "市场分析与竞争格局",
            "技术发展与创新趋势",
            "政策环境与法规标�?,
            "投资与融资分�?,
            "发展趋势与前景预�?,
            "风险分析与应对策�?,
            "结论与建�?
        ]

        # 确保有足够的默认标题
        while len(default_sections) < primary_sections:
            default_sections.append(f"专题分析{len(default_sections) - 7}")

        # 只取需要的数量
        default_sections = default_sections[:primary_sections]

        sections = []
        for title in default_sections:
            section = {
                "title": title,
                "level": 1,
                "children": self._get_default_subsection_structure()
            }
            sections.append(section)

        return {"sections": sections}

    def _get_default_subsection_structure(self) -> List[Dict[str, Any]]:
        """获取默认的子标题结构"""
        return [
            {
                "title": "概述与定�?,
                "level": 2,
                "children": [
                    {"title": "基本概念", "level": 3, "children": []},
                    {"title": "发展历程", "level": 3, "children": []}
                ]
            },
            {
                "title": "现状分析",
                "level": 2,
                "children": [
                    {"title": "市场规模", "level": 3, "children": []},
                    {"title": "技术水�?, "level": 3, "children": []}
                ]
            },
            {
                "title": "发展趋势",
                "level": 2,
                "children": [
                    {"title": "技术趋�?, "level": 3, "children": []},
                    {"title": "市场趋势", "level": 3, "children": []}
                ]
            }
        ]

    # ==================== 更多原代码功能补�?====================

    async def generate_framework_async(self, topic: str, framework_content: str) -> Dict[str, Any]:
        """第一步：统筹模型生成报告框架（异步版本）"""
        print("🎯 第一步：统筹模型异步读取框架文件并生成报告框�?)

        # 获取动态配�?        primary_sections = self.report_config.get("primary_sections", 8)
        max_depth = self.report_config.get("max_depth", 6)

        print(f"   📊 使用动态配�? {primary_sections}个一级标题，最大{max_depth}级深�?)

        prompt = f"""
作为报告统筹模型，请为主�?{topic}"设计一份详细的研究报告框架�?
{f"请参考以下现有框架：\\n{framework_content}\\n" if framework_content else ""}

要求�?1. 必须包含恰好{primary_sections}个一级标题（对应{primary_sections}个数据源文件夹）
2. 每个一级标题下必须完整扩展到{max_depth}级子标题
3. 标题层级必须连贯，不能跳�?4. 每个标题都应该有明确的title字段、level字段和children字段
5. 必须生成完整的{max_depth}级标题结构，不能只生成一级标�?
请以JSON格式返回完整的{max_depth}级结构，示例�?{{
    "sections": [
        {{
            "title": "一级标�?",
            "level": 1,
            "children": [
                {{
                    "title": "二级标题1.1",
                    "level": 2,
                    "children": [
                        {{
                            "title": "三级标题1.1.1",
                            "level": 3,
                            "children": [
                                {{
                                    "title": "四级标题*******",
                                    "level": 4,
                                    "children": []
                                }}
                            ]
                        }}
                    ]
                }}
            ]
        }}
    ]
}}

重要：必须生成完整的{max_depth}级标题结构，确保返回的是有效的JSON格式�?"""

        response = await self.call_orchestrator_model_async(prompt)

        try:
            # 尝试解析JSON
            framework = None

            if "```json" in response:
                start = response.find("```json") + 7
                end = response.find("```", start)
                if end != -1:
                    json_str = response[start:end].strip()
                    framework = json.loads(json_str)

            elif response.strip().startswith("{"):
                framework = json.loads(response.strip())

            # 验证框架结构
            if framework:
                validated_framework = self._validate_and_fix_framework(framework)
                if validated_framework:
                    return validated_framework

            print("⚠️ 无法解析框架JSON或结构不完整，使用默认框�?)
            return self._get_default_framework()

        except Exception as e:
            print(f"解析框架失败: {str(e)}")
            return self._get_default_framework()

    def read_data_source(self, data_path: str) -> str:
        """读取数据源（支持多种文件格式，带缓存机制�?""
        try:
            from utils.complete_file_reader import CompleteFileReader
            file_reader = CompleteFileReader()
            return file_reader.read_data_source(data_path)
        except Exception as e:
            print(f"读取数据源失�? {str(e)}")
            return ""

    def read_framework_file_content(self, framework_file_path: str) -> str:
        """读取框架文件内容（支持多种格式）"""
        try:
            from utils.complete_file_reader import CompleteFileReader
            file_reader = CompleteFileReader()
            return file_reader.read_file(framework_file_path)
        except Exception as e:
            print(f"读取框架文件失败: {str(e)}")
            return ""

    def _generate_complete_substructure_with_progress(self, sections: List[Dict[str, Any]], topic: str):
        """统筹模型为每个一级标题生成完整的子标题结构（2-5级）带进度条"""
        max_depth = self.report_config.get("max_depth", 5)

        # 创建子结构生成进度条
        try:
            from tqdm import tqdm
            substructure_pbar = tqdm(total=len(sections), desc="🎯 生成子结�?, unit="章节", leave=False)
        except ImportError:
            substructure_pbar = None

        for i, section in enumerate(sections, 1):
            section_title = section.get("title", f"第{i}�?)

            if substructure_pbar:
                substructure_pbar.set_description(f"🎯 {section_title}")

            # 构建统筹模型的prompt
            prompt = f"""
作为报告统筹模型，请为一级标�?{section_title}"设计完整的子标题结构�?
主题：{topic}
当前一级标题：{section_title}
要求层级深度：最多{max_depth}�?
请设计详细的子标题结构，包括�?1. 二级标题�?-4个）
2. 三级标题（每个二级标题下2-3个）
3. 四级标题（重要的三级标题�?-2个）
4. 五级标题（如需要，关键四级标题�?个）

请以JSON格式返回，结构如下：
{{
    "title": "{section_title}",
    "children": [
        {{
            "title": "二级标题1",
            "children": [
                {{
                    "title": "三级标题1.1",
                    "children": [
                        {{
                            "title": "四级标题1.1.1",
                            "children": [
                                {{"title": "五级标题*******"}}
                            ]
                        }}
                    ]
                }}
            ]
        }}
    ]
}}

要求�?1. 标题要专业、准确、有逻辑�?2. 层级结构要清晰合�?3. 覆盖该章节的核心内容
4. 确保JSON格式正确
"""

            try:
                # 调用统筹模型生成子标题结�?                response = self.call_orchestrator_model(prompt)

                # 解析JSON响应
                import json
                import re

                # 提取JSON部分
                json_match = re.search(r'\{.*\}', response, re.DOTALL)
                if json_match:
                    json_str = json_match.group()
                    substructure = json.loads(json_str)

                    # 更新section的children
                    if "children" in substructure:
                        section["children"] = substructure["children"]
                    else:
                        section["children"] = self._get_default_subsection_structure()
                else:
                    section["children"] = self._get_default_subsection_structure()

            except Exception as e:
                section["children"] = self._get_default_subsection_structure()

            if substructure_pbar:
                substructure_pbar.update(1)

        if substructure_pbar:
            substructure_pbar.close()

        print(f"�?完整框架结构生成完成，包含完整的{max_depth}级标题体�?)

    # ==================== 原代码中的核心生成方�?====================

    def generate_complete_report_with_all_features(self,
                                                  topic: str,
                                                  data_sources: List[str],
                                                  framework_file_path: Optional[str] = None,
                                                  target_words: int = 50000,
                                                  max_depth: int = 6,
                                                  enable_search: bool = True,
                                                  enable_images: bool = True,
                                                  enable_reference_optimization: bool = True,
                                                  reference_report_path: Optional[str] = None) -> str:
        """
        完整的报告生成流程（包含所有原代码功能�?        """
        start_time = time.time()
        print(f"🚀 开始完整报告生�? {topic}")
        print(f"📊 配置参数:")
        print(f"   目标字数: {target_words:,}")
        print(f"   最大层�? {max_depth}")
        print(f"   搜索增强: {'启用' if enable_search else '禁用'}")
        print(f"   图片嵌入: {'启用' if enable_images else '禁用'}")
        print(f"   参考优�? {'启用' if enable_reference_optimization else '禁用'}")

        try:
            # 更新配置
            self.report_config.update({
                "target_words": target_words,
                "max_depth": max_depth,
                "enable_search": enable_search,
                "enable_images": enable_images,
                "enable_reference_optimization": enable_reference_optimization
            })

            # 第一步：数据预处�?            print("\n📚 第一步：数据预处�?)
            processed_data = self._preprocess_all_data_sources(data_sources)

            # 保存checkpoint
            self.create_checkpoint("data_preprocessed", {
                "processed_data": processed_data,
                "topic": topic,
                "config": self.report_config
            })

            # 第二步：读取框架文件
            print("\n📋 第二步：读取框架文件")
            framework_content = ""
            if framework_file_path:
                framework_content = self.read_framework_file_content(framework_file_path)
                print(f"   框架文件: {framework_file_path}")
                print(f"   内容长度: {len(framework_content)} 字符")

            # 第三步：生成报告框架
            print("\n🏗�?第三步：生成报告框架")
            framework = self.generate_comprehensive_framework(topic, framework_content, max_depth)

            # 保存checkpoint
            self.create_checkpoint("framework_generated", {
                "framework": framework,
                "processed_data": processed_data
            })

            # 第四步：生成完整子结�?            print("\n🌳 第四步：生成完整子结�?)
            if self.use_async:
                framework = asyncio.run(self._generate_complete_substructure_async(framework, topic))
            else:
                framework = self._generate_complete_substructure(framework, topic)

            # 第五步：生成所有内�?            print("\n✍️ 第五步：生成所有内�?)
            if self.use_async:
                framework = asyncio.run(self._generate_all_content_with_data_async(framework, processed_data))
            else:
                framework = self._generate_all_content_with_data(framework, processed_data)

            # 第六步：搜索增强（如果启用）
            if enable_search:
                print("\n🔍 第六步：搜索增强")
                framework = self._enhance_with_search(framework, topic)

            # 第七步：图片处理（如果启用）
            if enable_images:
                print("\n🖼�?第七步：图片处理")
                framework = self._process_images(framework, processed_data)

            # 第八步：参考报告优化（如果启用�?            if enable_reference_optimization and reference_report_path:
                print("\n📖 第八步：参考报告优�?)
                framework = self._optimize_with_reference(framework, reference_report_path)

            # 第九步：迭代优化
            print("\n🔧 第九步：3轮迭代优�?)
            if self.use_async:
                framework = asyncio.run(self._iterative_optimization_async(framework, processed_data, topic))
            else:
                framework = self._iterative_optimization(framework, processed_data, topic)

            # 第十步：字数控制
            print("\n📊 第十步：最终字数控�?)
            if self.use_async:
                framework = asyncio.run(self._control_final_word_count_async(framework, target_words, topic))
            else:
                framework = self._control_final_word_count(framework, target_words, topic)

            # 第十一步：生成最终文�?            print("\n📄 第十一步：生成最终文�?)
            output_path = self._generate_final_document(topic, framework, processed_data)

            # 计算总耗时
            total_time = time.time() - start_time
            print(f"\n�?完整报告生成完成�?)
            print(f"⏱️ 总耗时: {total_time:.1f}�?)
            print(f"📄 输出文件: {output_path}")
            print(f"💾 最终checkpoint: {self.current_checkpoint_id}")

            return output_path

        except Exception as e:
            print(f"\n�?报告生成失败: {str(e)}")
            if self.current_checkpoint_id:
                print(f"💾 可从checkpoint恢复: {self.current_checkpoint_id}")
            import traceback
            traceback.print_exc()
            raise

    def generate_comprehensive_framework(self, topic: str, framework_content: str = "", max_depth: int = 6) -> Dict[str, Any]:
        """生成全面的报告框�?""
        prompt = f"""
您是一位资深的产业研究专家，需要为"{topic}"生成一个专业、全面的研究报告框架�?
{f"请参考以下现有框架：\\n{framework_content}\\n" if framework_content else ""}

要求�?1. 必须包含恰好8个一级标题（对应8个数据源文件夹）
2. 每个一级标题下必须完整扩展到{max_depth}级子标题
3. 标题层级必须连贯，不能跳�?4. 每个标题都应该有明确的title字段和level字段
5. 必须生成完整的{max_depth}级标题结�?
请以JSON格式返回完整的{max_depth}级结构，示例�?{{
    "sections": [
        {{
            "title": "行业概述与现状分�?,
            "level": 1,
            "children": [
                {{
                    "title": "行业定义与分�?,
                    "level": 2,
                    "children": [
                        {{
                            "title": "基本概念界定",
                            "level": 3,
                            "children": [
                                {{
                                    "title": "核心技术要�?,
                                    "level": 4,
                                    "children": [
                                        {{
                                            "title": "技术标准体�?,
                                            "level": 5,
                                            "children": [
                                                {{
                                                    "title": "国际标准对比",
                                                    "level": 6,
                                                    "children": []
                                                }}
                                            ]
                                        }}
                                    ]
                                }}
                            ]
                        }}
                    ]
                }}
            ]
        }}
    ]
}}

确保返回的是有效的JSON格式，包含完整的{max_depth}级标题结构�?"""

        response = self.call_orchestrator_model(prompt)

        try:
            # 尝试解析JSON
            framework = None

            if "```json" in response:
                start = response.find("```json") + 7
                end = response.find("```", start)
                if end != -1:
                    json_str = response[start:end].strip()
                    framework = json.loads(json_str)

            elif response.strip().startswith("{"):
                framework = json.loads(response.strip())

            # 验证框架结构
            if framework:
                validated_framework = self._validate_and_fix_framework(framework)
                if validated_framework:
                    return validated_framework

            print("⚠️ 无法解析框架JSON或结构不完整，使用默认框�?)
            return self._get_default_framework()

        except Exception as e:
            print(f"解析框架失败: {str(e)}")
            return self._get_default_framework()

    def _preprocess_all_data_sources(self, data_sources: List[str]) -> Dict[str, Any]:
        """预处理所有数据源（完全按原代码）"""
        print("   📊 开始预处理所有数据源")

        processed_data = {
            "sources": {},
            "total_files": 0,
            "total_size": 0,
            "file_types": {},
            "processing_time": 0
        }

        start_time = time.time()

        for i, data_path in enumerate(data_sources, 1):
            print(f"   📁 处理数据�?{i}/{len(data_sources)}: {data_path}")

            try:
                # 读取数据�?                content = self.read_data_source(data_path)

                if content:
                    processed_data["sources"][data_path] = {
                        "content": content,
                        "size": len(content),
                        "file_count": self._count_files_in_source(data_path),
                        "processed_time": time.time()
                    }

                    processed_data["total_size"] += len(content)
                    processed_data["total_files"] += processed_data["sources"][data_path]["file_count"]

                    print(f"   �?成功处理: {len(content):,} 字符")
                else:
                    print(f"   ⚠️ 数据源为空或无法读取")

            except Exception as e:
                print(f"   �?处理失败: {str(e)}")
                continue

        processed_data["processing_time"] = time.time() - start_time

        print(f"   📊 预处理完�?")
        print(f"      总文件数: {processed_data['total_files']}")
        print(f"      总大�? {processed_data['total_size']:,} 字符")
        print(f"      处理时间: {processed_data['processing_time']:.1f}�?)

        return processed_data

    def _count_files_in_source(self, data_path: str) -> int:
        """统计数据源中的文件数�?""
        try:
            from pathlib import Path
            path = Path(data_path)

            if path.is_file():
                return 1
            elif path.is_dir():
                return len(list(path.rglob("*.*")))
            else:
                return 0
        except Exception:
            return 0

    def _generate_all_content_with_data(self, framework: Dict[str, Any], processed_data: Dict[str, Any]) -> Dict[str, Any]:
        """为所有节点生成内容（使用预处理的数据�?""
        print("   ✍️ 开始为所有节点生成内�?)

        sections = framework.get("sections", [])
        total_nodes = self._count_all_nodes(sections)

        print(f"   📊 总节点数: {total_nodes}")

        # 创建进度�?        try:
            from tqdm import tqdm
            pbar = tqdm(total=total_nodes, desc="生成内容", unit="节点")
        except ImportError:
            pbar = None

        # 为每个节点生成内�?        for section in sections:
            self._generate_node_content_recursive(section, processed_data, pbar)

        if pbar:
            pbar.close()

        print("   �?所有节点内容生成完�?)
        return framework

    def _generate_node_content_recursive(self, node: Dict[str, Any], processed_data: Dict[str, Any], pbar=None):
        """递归为节点生成内�?""
        try:
            # 为当前节点生成内�?            if "content" not in node or not node["content"]:
                node["content"] = self._generate_single_node_content(node, processed_data)

            if pbar:
                pbar.update(1)

            # 递归处理子节�?            for child in node.get("children", []):
                self._generate_node_content_recursive(child, processed_data, pbar)

        except Exception as e:
            print(f"   �?生成节点内容失败: {str(e)}")
            node["content"] = f"内容生成失败: {str(e)}"
            if pbar:
                pbar.update(1)

    def _generate_single_node_content(self, node: Dict[str, Any], processed_data: Dict[str, Any]) -> str:
        """为单个节点生成内�?""
        title = node.get("title", "未知标题")
        level = node.get("level", 1)

        # 构建数据上下�?        data_context = self._build_data_context(processed_data)

        prompt = f"""
请为第{level}级标�?{title}"生成详细的专业内容�?
数据上下文：
{data_context}

要求�?1. 内容要专业、准确、有深度
2. 字数控制�?00-1200�?3. 结构清晰，逻辑严密
4. 充分利用提供的数据信�?5. 避免空洞的表述，要有具体的数据和分析

请直接输出内容，不要包含标题�?"""

        try:
            response = self.call_executor_model(prompt)
            return self._clean_model_response(response)
        except Exception as e:
            print(f"   �?生成内容失败: {str(e)}")
            return f"内容生成遇到技术问题，请稍后重试。错误信�? {str(e)}"

    def _build_data_context(self, processed_data: Dict[str, Any]) -> str:
        """构建数据上下�?""
        context_parts = []

        sources = processed_data.get("sources", {})
        for source_path, source_data in sources.items():
            content = source_data.get("content", "")
            if content:
                # 截取�?000字符作为上下�?                truncated_content = content[:2000] + "..." if len(content) > 2000 else content
                context_parts.append(f"数据�?{source_path}:\n{truncated_content}")

        if not context_parts:
            return "暂无可用数据�?

        return "\n\n".join(context_parts)

    async def _generate_all_content_with_data_async(self, framework: Dict[str, Any], processed_data: Dict[str, Any]) -> Dict[str, Any]:
        """异步为所有节点生成内�?""
        print("   �?异步并行生成所有节点内�?)

        sections = framework.get("sections", [])
        total_nodes = self._count_all_nodes(sections)

        print(f"   📊 总节点数: {total_nodes}")

        # 收集所有需要生成内容的节点
        all_nodes = []
        self._collect_all_nodes(sections, all_nodes)

        # 创建异步任务
        tasks = []
        for node in all_nodes:
            if "content" not in node or not node["content"]:
                task = self._generate_single_node_content_async(node, processed_data)
                tasks.append((node, task))

        # 并行执行所有任�?        if tasks:
            print(f"   🚀 启动 {len(tasks)} 个并行任�?)

            # 使用进度�?            try:
                from tqdm.asyncio import tqdm
                results = await tqdm.gather(*[task for _, task in tasks], desc="异步生成内容")
            except ImportError:
                results = await asyncio.gather(*[task for _, task in tasks])

            # 将结果分配给对应的节�?            for i, (node, _) in enumerate(tasks):
                node["content"] = results[i]

        print("   �?异步内容生成完成")
        return framework

    def _collect_all_nodes(self, sections: List[Dict[str, Any]], all_nodes: List[Dict[str, Any]]):
        """收集所有节�?""
        for section in sections:
            all_nodes.append(section)
            if "children" in section:
                self._collect_all_nodes(section["children"], all_nodes)

    async def _generate_single_node_content_async(self, node: Dict[str, Any], processed_data: Dict[str, Any]) -> str:
        """异步为单个节点生成内�?""
        title = node.get("title", "未知标题")
        level = node.get("level", 1)

        # 构建数据上下�?        data_context = self._build_data_context(processed_data)

        prompt = f"""
请为第{level}级标�?{title}"生成详细的专业内容�?
数据上下文：
{data_context}

要求�?1. 内容要专业、准确、有深度
2. 字数控制�?00-1200�?3. 结构清晰，逻辑严密
4. 充分利用提供的数据信�?5. 避免空洞的表述，要有具体的数据和分析

请直接输出内容，不要包含标题�?"""

        try:
            response = await self.call_executor_model_async(prompt)
            return self._clean_model_response(response)
        except Exception as e:
            print(f"   �?异步生成内容失败: {str(e)}")
            return f"内容生成遇到技术问题，请稍后重试。错误信�? {str(e)}"

    def _enhance_with_search(self, framework: Dict[str, Any], topic: str) -> Dict[str, Any]:
        """使用搜索增强内容"""
        print("   🔍 开始搜索增�?)

        try:
            from search.search_trigger import SearchTrigger
            from search.search_manager import SearchManager
            from search.content_integrator import ContentIntegrator

            # 初始化搜索组�?            search_trigger = SearchTrigger(self)
            search_manager = SearchManager(self)
            content_integrator = ContentIntegrator(self)

            # 分析内容缺口
            current_content = self._extract_all_content(framework)
            gaps = search_trigger.analyze_content_gaps(current_content, topic)

            if gaps:
                print(f"   📊 发现 {len(gaps)} 个内容缺�?)

                # 执行搜索
                for gap in gaps:
                    print(f"   🔍 搜索: {gap.get('reason', '未知原因')}")

                    # 生成搜索查询
                    queries = search_manager.generate_search_queries(topic, gap)

                    # 执行多源搜索
                    search_results = []
                    for query in queries[:2]:  # 限制查询数量
                        results = search_manager.multi_source_search(query, num_results=3)
                        search_results.extend(results)

                    # 整合搜索结果
                    if search_results:
                        enhanced_content = content_integrator.integrate_search_results(
                            current_content, search_results, gap
                        )

                        # 更新框架内容
                        self._update_framework_with_enhanced_content(framework, enhanced_content, gap)

                print("   �?搜索增强完成")
            else:
                print("   ℹ️ 未发现需要搜索增强的内容缺口")

        except Exception as e:
            print(f"   �?搜索增强失败: {str(e)}")

        return framework

    def _extract_all_content(self, framework: Dict[str, Any]) -> str:
        """提取框架中的所有内�?""
        content_parts = []

        def extract_recursive(node):
            if "content" in node and node["content"]:
                content_parts.append(node["content"])

            for child in node.get("children", []):
                extract_recursive(child)

        for section in framework.get("sections", []):
            extract_recursive(section)

        return "\n\n".join(content_parts)

    def _update_framework_with_enhanced_content(self, framework: Dict[str, Any], enhanced_content: str, gap: Dict[str, Any]):
        """用增强内容更新框�?""
        # 简化实现：将增强内容添加到相关章节
        gap_type = gap.get("type", "general")

        sections = framework.get("sections", [])
        if sections and enhanced_content:
            # 根据缺口类型选择合适的章节
            target_section_index = 0
            if gap_type == "market_data":
                target_section_index = min(1, len(sections) - 1)
            elif gap_type == "technology":
                target_section_index = min(2, len(sections) - 1)
            elif gap_type == "policy":
                target_section_index = min(3, len(sections) - 1)

            # 将增强内容添加到目标章节
            if target_section_index < len(sections):
                current_content = sections[target_section_index].get("content", "")
                sections[target_section_index]["content"] = current_content + "\n\n" + enhanced_content

    def _process_images(self, framework: Dict[str, Any], processed_data: Dict[str, Any]) -> Dict[str, Any]:
        """处理图片内容"""
        print("   🖼�?开始图片处�?)

        try:
            from image.image_processor import ImageProcessor

            image_processor = ImageProcessor(self)

            # 查找图片文件
            image_files = self._find_image_files(processed_data)

            if image_files:
                print(f"   📊 发现 {len(image_files)} 个图片文�?)

                # 处理图片
                for image_file in image_files:
                    try:
                        # 提取图片内容
                        image_content = image_processor.process_image(image_file)

                        if image_content:
                            # 将图片内容整合到框架�?                            self._integrate_image_content(framework, image_content, image_file)
                            print(f"   �?处理图片: {image_file}")

                    except Exception as e:
                        print(f"   �?处理图片失败 {image_file}: {str(e)}")
                        continue

                print("   �?图片处理完成")
            else:
                print("   ℹ️ 未发现图片文�?)

        except Exception as e:
            print(f"   �?图片处理失败: {str(e)}")

        return framework

    def _find_image_files(self, processed_data: Dict[str, Any]) -> List[str]:
        """查找图片文件"""
        image_files = []
        image_extensions = {'.png', '.jpg', '.jpeg', '.gif', '.bmp', '.svg', '.webp', '.tiff'}

        try:
            from pathlib import Path

            sources = processed_data.get("sources", {})
            for source_path in sources.keys():
                path = Path(source_path)

                if path.is_dir():
                    for file_path in path.rglob("*"):
                        if file_path.suffix.lower() in image_extensions:
                            image_files.append(str(file_path))
                elif path.suffix.lower() in image_extensions:
                    image_files.append(str(path))

        except Exception as e:
            print(f"   �?查找图片文件失败: {str(e)}")

        return image_files

    def _integrate_image_content(self, framework: Dict[str, Any], image_content: str, image_file: str):
        """将图片内容整合到框架�?""
        # 简化实现：将图片内容添加到第一个章�?        sections = framework.get("sections", [])
        if sections and image_content:
            current_content = sections[0].get("content", "")
            sections[0]["content"] = current_content + f"\n\n图片内容 ({image_file}):\n{image_content}"

    def _optimize_with_reference(self, framework: Dict[str, Any], reference_report_path: str) -> Dict[str, Any]:
        """使用参考报告优�?""
        print("   📖 开始参考报告优�?)

        try:
            # 读取参考报�?            reference_content = self.read_framework_file_content(reference_report_path)

            if reference_content:
                print(f"   📊 参考报告长�? {len(reference_content)} 字符")

                # 分析参考报告结�?                reference_structure = self._analyze_reference_structure(reference_content)

                # 优化当前框架
                framework = self._apply_reference_optimization(framework, reference_structure)

                print("   �?参考报告优化完�?)
            else:
                print("   ⚠️ 无法读取参考报�?)

        except Exception as e:
            print(f"   �?参考报告优化失�? {str(e)}")

        return framework

    def _analyze_reference_structure(self, reference_content: str) -> Dict[str, Any]:
        """分析参考报告结�?""
        # 简化实现：提取关键信息
        structure = {
            "key_topics": [],
            "writing_style": "professional",
            "content_depth": "detailed"
        }

        # 提取关键主题（简化版�?        lines = reference_content.split('\n')
        for line in lines[:50]:  # 只分析前50�?            if any(keyword in line for keyword in ['市场', '技�?, '发展', '分析', '趋势']):
                structure["key_topics"].append(line.strip())

        return structure

    def _apply_reference_optimization(self, framework: Dict[str, Any], reference_structure: Dict[str, Any]) -> Dict[str, Any]:
        """应用参考报告优�?""
        # 简化实现：根据参考结构调整内容风�?        key_topics = reference_structure.get("key_topics", [])

        if key_topics:
            # 为每个章节添加参考信�?            sections = framework.get("sections", [])
            for section in sections:
                current_content = section.get("content", "")
                if current_content:
                    # 添加参考优化标�?                    section["content"] = current_content + "\n\n[参考报告优化已应用]"

        return framework

    def _iterative_optimization(self, framework: Dict[str, Any], processed_data: Dict[str, Any], topic: str) -> Dict[str, Any]:
        """3轮迭代优化（同步版本�?""
        print("   🔧 开�?轮迭代优�?)

        for iteration in range(1, 4):
            print(f"\n   🔄 �?{iteration} 轮优�?)

            # 章节级优�?            sections = framework.get("sections", [])
            for i, section in enumerate(sections, 1):
                print(f"      📝 优化章节 {i}/{len(sections)}: {section.get('title', '未知')}")

                try:
                    # 审核章节
                    review_result = self._review_section(section, topic, iteration)

                    # 优化章节
                    if review_result.get("needs_optimization", True):
                        optimized_section = self._optimize_section(section, review_result, processed_data)
                        sections[i-1] = optimized_section

                except Exception as e:
                    print(f"      �?章节优化失败: {str(e)}")
                    continue

            # 整体优化
            print(f"      🔍 整体文档优化")
            try:
                framework = self._optimize_overall_structure(framework, topic, iteration)
            except Exception as e:
                print(f"      �?整体优化失败: {str(e)}")

            print(f"   �?�?{iteration} 轮优化完�?)

        print("   �?3轮迭代优化完�?)
        return framework

    def _review_section(self, section: Dict[str, Any], topic: str, iteration: int) -> Dict[str, Any]:
        """审核章节内容"""
        title = section.get("title", "未知标题")
        content = section.get("content", "")

        prompt = f"""
作为资深编辑，请审核以下章节内容的质量：

主题：{topic}
章节标题：{title}
当前轮次：第{iteration}轮优�?
章节内容�?{content[:2000]}...

请从以下维度评估�?1. 内容完整性（是否充分覆盖主题�?2. 逻辑结构（是否条理清晰）
3. 专业深度（是否有足够的专业性）
4. 数据支撑（是否有具体数据和案例）
5. 语言表达（是否准确流畅）

请以JSON格式返回评估结果�?{{
    "needs_optimization": true/false,
    "issues": ["问题1", "问题2"],
    "suggestions": ["建议1", "建议2"],
    "score": 85
}}
"""

        try:
            response = self.call_orchestrator_model(prompt)

            # 解析JSON响应
            if "```json" in response:
                start = response.find("```json") + 7
                end = response.find("```", start)
                if end != -1:
                    json_str = response[start:end].strip()
                    return json.loads(json_str)

            # 如果解析失败，返回默认结�?            return {
                "needs_optimization": True,
                "issues": ["需要进一步优�?],
                "suggestions": ["增强内容深度和专业�?],
                "score": 70
            }

        except Exception as e:
            print(f"      �?章节审核失败: {str(e)}")
            return {"needs_optimization": True, "issues": [], "suggestions": [], "score": 60}

    def _optimize_section(self, section: Dict[str, Any], review_result: Dict[str, Any], processed_data: Dict[str, Any]) -> Dict[str, Any]:
        """优化章节内容"""
        title = section.get("title", "未知标题")
        content = section.get("content", "")
        issues = review_result.get("issues", [])
        suggestions = review_result.get("suggestions", [])

        # 构建数据上下�?        data_context = self._build_data_context(processed_data)

        prompt = f"""
请根据审核意见优化以下章节内容：

章节标题：{title}

当前内容�?{content}

发现的问题：
{chr(10).join(f"- {issue}" for issue in issues)}

优化建议�?{chr(10).join(f"- {suggestion}" for suggestion in suggestions)}

可用数据�?{data_context[:1000]}...

请输出优化后的章节内容，要求�?1. 解决所有提到的问题
2. 采纳合理的优化建�?3. 保持原有结构的基础上增强内容质�?4. 充分利用提供的数据信�?5. 确保内容专业、准确、有深度

请直接输出优化后的内容：
"""

        try:
            optimized_content = self.call_executor_model(prompt)
            section["content"] = self._clean_model_response(optimized_content)
            return section
        except Exception as e:
            print(f"      �?章节优化失败: {str(e)}")
            return section

    def _optimize_overall_structure(self, framework: Dict[str, Any], topic: str, iteration: int) -> Dict[str, Any]:
        """优化整体结构"""
        sections = framework.get("sections", [])

        # 提取所有章节标�?        section_titles = [section.get("title", "") for section in sections]

        prompt = f"""
作为资深编辑，请审核整体报告结构的合理性：

主题：{topic}
当前轮次：第{iteration}轮优�?
章节结构�?{chr(10).join(f"{i+1}. {title}" for i, title in enumerate(section_titles))}

请评估：
1. 章节顺序是否合理
2. 章节间逻辑关系是否清晰
3. 是否有重复或遗漏的内�?4. 整体结构是否完整

如果需要调整，请提供具体建议。如果结构合理，请确认�?
请以简洁的文字回复�?"""

        try:
            response = self.call_orchestrator_model(prompt)

            # 简化处理：如果响应中包含具体建议，记录但不做结构性调�?            if "建议" in response or "调整" in response:
                print(f"      📝 结构优化建议: {response[:200]}...")

            return framework

        except Exception as e:
            print(f"      �?整体结构优化失败: {str(e)}")
            return framework

    async def _iterative_optimization_async(self, framework: Dict[str, Any], processed_data: Dict[str, Any], topic: str) -> Dict[str, Any]:
        """3轮迭代优化（异步版本�?""
        print("   �?异步3轮迭代优�?)

        for iteration in range(1, 4):
            print(f"\n   🔄 �?{iteration} 轮异步优�?)

            # 并行优化所有章�?            sections = framework.get("sections", [])

            # 创建章节优化任务
            section_tasks = []
            for section in sections:
                task = self._optimize_section_async(section, topic, iteration, processed_data)
                section_tasks.append(task)

            # 并行执行章节优化
            if section_tasks:
                try:
                    from tqdm.asyncio import tqdm
                    optimized_sections = await tqdm.gather(*section_tasks, desc=f"第{iteration}轮优�?)
                except ImportError:
                    optimized_sections = await asyncio.gather(*section_tasks)

                # 更新章节
                framework["sections"] = optimized_sections

            # 整体优化
            framework = await self._optimize_overall_structure_async(framework, topic, iteration)

            print(f"   �?�?{iteration} 轮异步优化完�?)

        print("   �?异步3轮迭代优化完�?)
        return framework

    async def _optimize_section_async(self, section: Dict[str, Any], topic: str, iteration: int, processed_data: Dict[str, Any]) -> Dict[str, Any]:
        """异步优化章节"""
        try:
            # 异步审核
            review_result = await self._review_section_async(section, topic, iteration)

            # 如果需要优�?            if review_result.get("needs_optimization", True):
                return await self._apply_section_optimization_async(section, review_result, processed_data)
            else:
                return section

        except Exception as e:
            print(f"      �?异步章节优化失败: {str(e)}")
            return section

    async def _review_section_async(self, section: Dict[str, Any], topic: str, iteration: int) -> Dict[str, Any]:
        """异步审核章节"""
        title = section.get("title", "未知标题")
        content = section.get("content", "")

        prompt = f"""
作为资深编辑，请审核以下章节内容的质量：

主题：{topic}
章节标题：{title}
当前轮次：第{iteration}轮优�?
章节内容�?{content[:2000]}...

请从以下维度评估�?1. 内容完整性（是否充分覆盖主题�?2. 逻辑结构（是否条理清晰）
3. 专业深度（是否有足够的专业性）
4. 数据支撑（是否有具体数据和案例）
5. 语言表达（是否准确流畅）

请以JSON格式返回评估结果�?{{
    "needs_optimization": true/false,
    "issues": ["问题1", "问题2"],
    "suggestions": ["建议1", "建议2"],
    "score": 85
}}
"""

        try:
            response = await self.call_orchestrator_model_async(prompt)

            # 解析JSON响应
            if "```json" in response:
                start = response.find("```json") + 7
                end = response.find("```", start)
                if end != -1:
                    json_str = response[start:end].strip()
                    return json.loads(json_str)

            return {
                "needs_optimization": True,
                "issues": ["需要进一步优�?],
                "suggestions": ["增强内容深度和专业�?],
                "score": 70
            }

        except Exception as e:
            return {"needs_optimization": True, "issues": [], "suggestions": [], "score": 60}

    async def _apply_section_optimization_async(self, section: Dict[str, Any], review_result: Dict[str, Any], processed_data: Dict[str, Any]) -> Dict[str, Any]:
        """异步应用章节优化"""
        title = section.get("title", "未知标题")
        content = section.get("content", "")
        issues = review_result.get("issues", [])
        suggestions = review_result.get("suggestions", [])

        data_context = self._build_data_context(processed_data)

        prompt = f"""
请根据审核意见优化以下章节内容：

章节标题：{title}

当前内容�?{content}

发现的问题：
{chr(10).join(f"- {issue}" for issue in issues)}

优化建议�?{chr(10).join(f"- {suggestion}" for suggestion in suggestions)}

可用数据�?{data_context[:1000]}...

请输出优化后的章节内容，要求�?1. 解决所有提到的问题
2. 采纳合理的优化建�?3. 保持原有结构的基础上增强内容质�?4. 充分利用提供的数据信�?5. 确保内容专业、准确、有深度

请直接输出优化后的内容：
"""

        try:
            optimized_content = await self.call_executor_model_async(prompt)
            section["content"] = self._clean_model_response(optimized_content)
            return section
        except Exception as e:
            return section

    async def _optimize_overall_structure_async(self, framework: Dict[str, Any], topic: str, iteration: int) -> Dict[str, Any]:
        """异步优化整体结构"""
        sections = framework.get("sections", [])
        section_titles = [section.get("title", "") for section in sections]

        prompt = f"""
作为资深编辑，请审核整体报告结构的合理性：

主题：{topic}
当前轮次：第{iteration}轮优�?
章节结构�?{chr(10).join(f"{i+1}. {title}" for i, title in enumerate(section_titles))}

请评估：
1. 章节顺序是否合理
2. 章节间逻辑关系是否清晰
3. 是否有重复或遗漏的内�?4. 整体结构是否完整

如果需要调整，请提供具体建议。如果结构合理，请确认�?
请以简洁的文字回复�?"""

        try:
            response = await self.call_orchestrator_model_async(prompt)

            if "建议" in response or "调整" in response:
                print(f"      📝 异步结构优化建议: {response[:200]}...")

            return framework

        except Exception as e:
            return framework

    def _generate_final_document(self, topic: str, framework: Dict[str, Any], processed_data: Dict[str, Any]) -> str:
        """生成最终文�?""
        print("   📄 开始生成最终文�?)

        try:
            # 创建输出目录
            output_dir = Path(self.report_config.get("output_dir", "output"))
            output_dir.mkdir(exist_ok=True)

            # 生成文件�?            import time
            timestamp = time.strftime("%Y%m%d_%H%M%S")
            filename = f"{topic}_{timestamp}.md"
            output_path = output_dir / filename

            # 生成文档内容
            document_content = self._build_document_content(topic, framework, processed_data)

            # 保存文档
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write(document_content)

            # 统计信息
            word_count = len(document_content)
            print(f"   �?文档生成完成")
            print(f"      文件路径: {output_path}")
            print(f"      文档字数: {word_count:,} 字符")

            # 同时生成Word文档
            word_path = self._generate_word_document(topic, framework)
            if word_path:
                print(f"      Word文档: {word_path}")

            return str(output_path)

        except Exception as e:
            print(f"   �?文档生成失败: {str(e)}")
            return ""

    def _build_document_content(self, topic: str, framework: Dict[str, Any], processed_data: Dict[str, Any]) -> str:
        """构建文档内容"""
        content_parts = []

        # 文档标题
        content_parts.append(f"# {topic}")
        content_parts.append("")

        # 生成时间
        import time
        content_parts.append(f"**生成时间**: {time.strftime('%Y�?m�?d�?%H:%M:%S')}")
        content_parts.append("")

        # 目录
        content_parts.append("## 目录")
        content_parts.append("")
        toc = self._generate_table_of_contents(framework)
        content_parts.append(toc)
        content_parts.append("")

        # 正文内容
        sections = framework.get("sections", [])
        for i, section in enumerate(sections, 1):
            section_content = self._build_section_content(section, i)
            content_parts.append(section_content)
            content_parts.append("")

        # 附录
        appendix = self._build_appendix(processed_data)
        if appendix:
            content_parts.append(appendix)

        return "\n".join(content_parts)

    def _generate_table_of_contents(self, framework: Dict[str, Any]) -> str:
        """生成目录"""
        toc_lines = []

        def build_toc_recursive(sections, level=1):
            for i, section in enumerate(sections, 1):
                title = section.get("title", f"章节{i}")
                indent = "  " * (level - 1)

                if level == 1:
                    toc_lines.append(f"{indent}{i}. {title}")
                else:
                    toc_lines.append(f"{indent}- {title}")

                # 递归处理子章�?                children = section.get("children", [])
                if children:
                    build_toc_recursive(children, level + 1)

        sections = framework.get("sections", [])
        build_toc_recursive(sections)

        return "\n".join(toc_lines)

    def _build_section_content(self, section: Dict[str, Any], section_number: int) -> str:
        """构建章节内容"""
        content_parts = []

        def build_content_recursive(node, numbers):
            title = node.get("title", "未知标题")
            level = node.get("level", 1)
            content = node.get("content", "")

            # 生成标题
            header_prefix = "#" * (level + 1)  # +1 因为文档标题占用了一�?            number_str = ".".join(map(str, numbers))
            content_parts.append(f"{header_prefix} {number_str} {title}")
            content_parts.append("")

            # 添加内容
            if content:
                content_parts.append(content)
                content_parts.append("")

            # 递归处理子节�?            children = node.get("children", [])
            for i, child in enumerate(children, 1):
                child_numbers = numbers + [i]
                build_content_recursive(child, child_numbers)

        build_content_recursive(section, [section_number])

        return "\n".join(content_parts)

    def _build_appendix(self, processed_data: Dict[str, Any]) -> str:
        """构建附录"""
        appendix_parts = []

        appendix_parts.append("## 附录")
        appendix_parts.append("")

        # 数据源信�?        appendix_parts.append("### 数据源信�?)
        appendix_parts.append("")

        sources = processed_data.get("sources", {})
        if sources:
            appendix_parts.append("| 数据�?| 文件数量 | 内容大小 |")
            appendix_parts.append("|--------|----------|----------|")

            for source_path, source_data in sources.items():
                file_count = source_data.get("file_count", 0)
                size = source_data.get("size", 0)
                appendix_parts.append(f"| {source_path} | {file_count} | {size:,} 字符 |")
        else:
            appendix_parts.append("无数据源信息")

        appendix_parts.append("")

        # 生成统计
        total_files = processed_data.get("total_files", 0)
        total_size = processed_data.get("total_size", 0)
        processing_time = processed_data.get("processing_time", 0)

        appendix_parts.append("### 生成统计")
        appendix_parts.append("")
        appendix_parts.append(f"- 总文件数: {total_files}")
        appendix_parts.append(f"- 总数据量: {total_size:,} 字符")
        appendix_parts.append(f"- 处理时间: {processing_time:.1f} �?)

        return "\n".join(appendix_parts)

    def _generate_word_document(self, topic: str, framework: Dict[str, Any]) -> str:
        """生成Word文档"""
        try:
            from docx import Document
            from docx.shared import Inches

            # 创建文档
            doc = Document()

            # 添加标题
            title = doc.add_heading(topic, 0)

            # 添加生成时间
            import time
            doc.add_paragraph(f"生成时间: {time.strftime('%Y�?m�?d�?%H:%M:%S')}")

            # 添加目录
            doc.add_heading('目录', level=1)
            toc_paragraph = doc.add_paragraph()
            toc_content = self._generate_table_of_contents(framework)
            toc_paragraph.add_run(toc_content)

            # 添加正文
            sections = framework.get("sections", [])
            for i, section in enumerate(sections, 1):
                self._add_section_to_word(doc, section, i)

            # 保存文档
            output_dir = Path(self.report_config.get("output_dir", "output"))
            output_dir.mkdir(exist_ok=True)

            timestamp = time.strftime("%Y%m%d_%H%M%S")
            word_filename = f"{topic}_{timestamp}.docx"
            word_path = output_dir / word_filename

            doc.save(str(word_path))

            return str(word_path)

        except ImportError:
            print("   ⚠️ 需要安装python-docx库来生成Word文档")
            return ""
        except Exception as e:
            print(f"   �?Word文档生成失败: {str(e)}")
            return ""

    def _add_section_to_word(self, doc, section: Dict[str, Any], section_number: int):
        """向Word文档添加章节"""
        def add_content_recursive(node, numbers):
            title = node.get("title", "未知标题")
            level = node.get("level", 1)
            content = node.get("content", "")

            # 添加标题
            number_str = ".".join(map(str, numbers))
            full_title = f"{number_str} {title}"
            doc.add_heading(full_title, level=min(level, 9))  # Word最多支�?级标�?
            # 添加内容
            if content:
                # 将内容按段落分割
                paragraphs = content.split('\n\n')
                for para in paragraphs:
                    if para.strip():
                        doc.add_paragraph(para.strip())

            # 递归处理子节�?            children = node.get("children", [])
            for i, child in enumerate(children, 1):
                child_numbers = numbers + [i]
                add_content_recursive(child, child_numbers)

        add_content_recursive(section, [section_number])

    # ==================== 原代码中的Checkpoint恢复系统 ====================

    def _resume_from_checkpoint(self, checkpoint_id: str, topic: str, data_sources: List[str], framework_file_path: Optional[str] = None) -> str:
        """从checkpoint恢复报告生成（完全按原代码）"""
        try:
            print(f"🔄 从checkpoint恢复: {checkpoint_id}")

            # 加载checkpoint数据
            checkpoint_data = self.load_checkpoint(checkpoint_id)
            if not checkpoint_data:
                raise ValueError(f"无法加载checkpoint: {checkpoint_id}")

            stage = self.checkpoint_data.get("stage", "")
            print(f"📍 恢复阶段: {stage}")

            # 根据阶段恢复执行
            if stage == "framework_generated":
                return self._resume_from_framework_stage(checkpoint_data, topic, data_sources)
            elif stage == "content_generated":
                return self._resume_from_content_stage(checkpoint_data, topic, data_sources)
            elif stage.startswith("optimization_round_"):
                return self._resume_from_optimization_stage(checkpoint_data, topic, data_sources)
            elif stage == "word_count_controlled":
                return self._resume_from_word_count_stage(checkpoint_data, topic)
            elif stage == "report_completed":
                print(f"�?报告已完成，输出路径: {checkpoint_data.get('output_path', '未知')}")
                return checkpoint_data.get('output_path', '')
            else:
                raise ValueError(f"未知的checkpoint阶段: {stage}")

        except Exception as e:
            print(f"�?从checkpoint恢复失败: {str(e)}")
            raise

    def _resume_from_framework_stage(self, checkpoint_data: dict, topic: str, data_sources: List[str]) -> str:
        """从框架生成阶段恢�?""
        try:
            print("🔄 从框架生成阶段恢�?..")

            framework = checkpoint_data.get("framework", {})
            processed_data = checkpoint_data.get("processed_data", {})

            # 第二步：生成内容
            print("�?第二步：执行模型按框架生成具体内�?)
            if self.use_async:
                framework = asyncio.run(self._generate_all_content_with_data_async(framework, processed_data))
            else:
                framework = self._generate_all_content_with_data(framework, processed_data)

            # 保存内容生成checkpoint
            self.create_checkpoint("content_generated", {
                "topic": topic,
                "framework": framework,
                "processed_data": processed_data,
                "data_sources": data_sources
            })

            # 继续后续流程
            return self._continue_from_content_stage(framework, processed_data, data_sources, topic)

        except Exception as e:
            print(f"�?从框架阶段恢复失�? {str(e)}")
            raise

    def _resume_from_content_stage(self, checkpoint_data: dict, topic: str, data_sources: List[str]) -> str:
        """从内容生成阶段恢�?""
        try:
            print("🔄 从内容生成阶段恢�?..")

            framework = checkpoint_data.get("framework", {})
            processed_data = checkpoint_data.get("processed_data", {})

            return self._continue_from_content_stage(framework, processed_data, data_sources, topic)

        except Exception as e:
            print(f"�?从内容阶段恢复失�? {str(e)}")
            raise

    def _resume_from_optimization_stage(self, checkpoint_data: dict, topic: str, data_sources: List[str]) -> str:
        """从优化阶段恢�?""
        try:
            print("🔄 从优化阶段恢�?..")

            framework = checkpoint_data.get("framework", {})
            processed_data = checkpoint_data.get("processed_data", {})
            completed_iterations = checkpoint_data.get("completed_iterations", 0)

            # 继续剩余的优化轮�?            for iteration in range(completed_iterations + 1, 4):
                print(f"\n🔄 开始第 {iteration} 轮迭代优�?)

                if self.use_async:
                    framework = asyncio.run(self._iterative_optimization_async(framework, processed_data, topic))
                else:
                    framework = self._iterative_optimization(framework, processed_data, topic)

                # 保存每轮优化checkpoint
                self.create_checkpoint(f"optimization_round_{iteration}", {
                    "topic": topic,
                    "framework": framework,
                    "processed_data": processed_data,
                    "data_sources": data_sources,
                    "completed_iterations": iteration
                })

            return self._continue_from_optimization_stage(framework, processed_data, topic)

        except Exception as e:
            print(f"�?从优化阶段恢复失�? {str(e)}")
            raise

    def _resume_from_word_count_stage(self, checkpoint_data: dict, topic: str) -> str:
        """从字数控制阶段恢�?""
        try:
            print("🔄 从字数控制阶段恢�?..")

            framework = checkpoint_data.get("framework", {})
            processed_data = checkpoint_data.get("processed_data", {})

            # 第五步：生成文档
            print("📄 第五步：生成最终文�?)
            output_path = self._generate_final_document(topic, framework, processed_data)

            # 保存最终完成checkpoint
            self.create_checkpoint("report_completed", {
                "topic": topic,
                "framework": framework,
                "output_path": output_path
            })

            return output_path

        except Exception as e:
            print(f"�?从字数控制阶段恢复失�? {str(e)}")
            raise

    def _continue_from_content_stage(self, framework: dict, processed_data: dict, data_sources: List[str], topic: str) -> str:
        """从内容生成阶段继续执�?""
        try:
            # 第三步：严谨�?轮迭代优化流�?            print("🔄 第三步：严谨�?轮迭代优化流�?)
            for iteration in range(1, 4):
                print(f"\n🔄 开始第 {iteration} 轮迭代优�?)

                if self.use_async:
                    framework = asyncio.run(self._iterative_optimization_async(framework, processed_data, topic))
                else:
                    framework = self._iterative_optimization(framework, processed_data, topic)

                # 保存每轮优化checkpoint
                self.create_checkpoint(f"optimization_round_{iteration}", {
                    "topic": topic,
                    "framework": framework,
                    "processed_data": processed_data,
                    "data_sources": data_sources,
                    "completed_iterations": iteration
                })

            return self._continue_from_optimization_stage(framework, processed_data, topic)

        except Exception as e:
            print(f"�?从内容阶段继续执行失�? {str(e)}")
            raise

    def _continue_from_optimization_stage(self, framework: dict, processed_data: dict, topic: str) -> str:
        """从优化阶段继续执�?""
        try:
            # 第四步：字数控制优化
            print("📊 第四步：最终字数控�?)
            target_words = self.report_config.get("target_words", 50000)

            if self.use_async:
                framework = asyncio.run(self._control_final_word_count_async(framework, target_words, topic))
            else:
                framework = self._control_final_word_count(framework, target_words, topic)

            # 保存字数控制checkpoint
            self.create_checkpoint("word_count_controlled", {
                "topic": topic,
                "framework": framework,
                "processed_data": processed_data
            })

            # 第五步：生成文档
            print("📄 第五步：生成最终文�?)
            output_path = self._generate_final_document(topic, framework, processed_data)

            # 保存最终完成checkpoint
            self.create_checkpoint("report_completed", {
                "topic": topic,
                "framework": framework,
                "output_path": output_path
            })

            return output_path

        except Exception as e:
            print(f"�?从优化阶段继续执行失�? {str(e)}")
            raise

    def generate_report(self, 
                       topic: str,
                       data_sources: List[str],
                       framework_file_path: Optional[str] = None,
                       resume_checkpoint: str = None) -> str:
        """生成完整报告（主入口，支持checkpoint恢复�?""
        
        # 检查是否需要从checkpoint恢复
        if resume_checkpoint:
            print(f"🔄 从checkpoint恢复: {resume_checkpoint}")
            return self._resume_from_checkpoint(resume_checkpoint, topic, data_sources, framework_file_path)

        if self.use_async:
            # 使用异步版本
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            try:
                return loop.run_until_complete(
                    self.generate_report_async(topic, data_sources, framework_file_path)
                )
            finally:
                loop.close()
        else:
            # 使用同步版本
            return self.generate_report_sync(topic, data_sources, framework_file_path)

    def generate_report_sync(self,
                            topic: str,
                            data_sources: List[str],
                            framework_file_path: Optional[str] = None) -> str:
        """生成完整报告（同步版本，完全按源代码逻辑�?""
        start_time = time.time()
        print(f"🚀 开始生成报�? {topic}")

        # 创建总体进度条（完全按源代码�?        total_steps = 6  # 读取框架、生成框架、生成子结构、生成内容、优化、保�?        try:
            from tqdm import tqdm
            main_pbar = tqdm(total=total_steps, desc="📊 报告生成总进�?, unit="步骤")
        except ImportError:
            main_pbar = None

        try:
            # 第一步：读取框架文件
            if main_pbar:
                main_pbar.set_description("📖 读取框架文件")
            framework_content = ""
            if framework_file_path:
                framework_content = self.read_framework_file_content(framework_file_path)
            if main_pbar:
                main_pbar.update(1)

            # 第二步：生成一级标题框�?            if main_pbar:
                main_pbar.set_description("🏗�?生成一级框�?)
            framework = self.generate_framework(topic, framework_content)

            if not framework or "sections" not in framework:
                raise ValueError("框架生成失败")

            sections = framework["sections"]
            print(f"�?一级框架生成完成，包含 {len(sections)} 个一级章�?)

            # 保存一级框架checkpoint
            self.create_checkpoint("framework_level1_generated", {
                "topic": topic,
                "framework": framework,
                "framework_content": framework_content,
                "data_sources": data_sources,
                "framework_file_path": framework_file_path
            })
            if main_pbar:
                main_pbar.update(1)

            # 第三步：统筹模型为每个一级标题生成完整的子标题结�?            if main_pbar:
                main_pbar.set_description("🎯 生成完整子结�?)
            self._generate_complete_substructure_with_progress(sections, topic)

            # 保存完整框架checkpoint
            self.create_checkpoint("framework_complete_generated", {
                "topic": topic,
                "framework": framework,
                "sections": sections,
                "data_sources": data_sources,
                "framework_file_path": framework_file_path
            })
            if main_pbar:
                main_pbar.update(1)

            # 第四步：执行模型按完整框架生成具体内�?            if main_pbar:
                main_pbar.set_description("�?生成具体内容")
            processed_data = self._preprocess_all_data_sources(data_sources)
            framework = self._generate_all_content_with_data(framework, processed_data)
            if main_pbar:
                main_pbar.update(1)

            # 保存内容生成checkpoint
            self.create_checkpoint("content_generated", {
                "topic": topic,
                "framework": framework,
                "sections": sections,
                "data_sources": data_sources
            })

            # 第五步：严谨�?轮迭代优化流�?            if main_pbar:
                main_pbar.set_description("🔄 迭代优化")

            try:
                optimization_pbar = tqdm(total=3, desc="🔄 优化轮次", unit="�?, leave=False)
            except ImportError:
                optimization_pbar = None

            for iteration in range(1, 4):
                if optimization_pbar:
                    optimization_pbar.set_description(f"🔄 第{iteration}轮优�?)
                framework = self._iterative_optimization(framework, processed_data, topic)

                # 保存每轮优化checkpoint
                self.create_checkpoint(f"optimization_round_{iteration}", {
                    "topic": topic,
                    "framework": framework,
                    "sections": sections,
                    "data_sources": data_sources,
                    "completed_iterations": iteration
                })
                if optimization_pbar:
                    optimization_pbar.update(1)

            if optimization_pbar:
                optimization_pbar.close()
            if main_pbar:
                main_pbar.update(1)

            # 第六步：生成文档
            if main_pbar:
                main_pbar.set_description("📄 生成最终文�?)
            target_words = self.report_config.get("target_words", 50000)
            output_path = self._generate_final_document(topic, framework, processed_data)

            # 保存最终完成checkpoint
            self.create_checkpoint("report_completed", {
                "topic": topic,
                "framework": framework,
                "sections": sections,
                "output_path": output_path
            })
            if main_pbar:
                main_pbar.update(1)

            # 清理旧checkpoint
            self.cleanup_old_checkpoints(keep_count=5)

            # 完成进度条并显示总时�?            if main_pbar:
                main_pbar.close()
            total_time = time.time() - start_time
            print(f"\n🎉 报告生成完成！总耗时: {total_time:.1f}�?)
            print(f"📄 输出文件: {output_path}")

            # 检查是否启用图片嵌入功能（完全按源代码�?            enable_image_embedding = self.report_config.get("enable_image_embedding", True)

            if enable_image_embedding:
                print(f"\n🖼�?开始图片嵌入流�?..")
                try:
                    # 执行图片嵌入
                    enhanced_output_path = self.embed_images_in_report(
                        output_path, data_sources, topic, auto_confirm=False
                    )

                    if enhanced_output_path != output_path:
                        print(f"�?图片嵌入完成！增强版报告: {enhanced_output_path}")
                        current_output = enhanced_output_path
                    else:
                        print(f"📄 未进行图片嵌入，使用原始报告")
                        current_output = output_path

                except Exception as e:
                    print(f"⚠️ 图片嵌入失败: {str(e)}")
                    print(f"📄 使用原始报告")
                    current_output = output_path
            else:
                print(f"📄 图片嵌入功能已禁�?)
                current_output = output_path

            # 检查是否启用搜索增强功能（完全按源代码�?            enable_search_enhancement = self.report_config.get("enable_search_enhancement", True)

            if enable_search_enhancement:
                print(f"\n🔍 开始智能搜索增强流�?..")
                try:
                    # 优先使用工具调用方式进行搜索增强
                    final_output_path = self.enhance_report_with_tool_calling(
                        current_output, topic, user_confirm=True
                    )

                    if final_output_path != current_output:
                        print(f"�?智能搜索增强完成！最终报�? {final_output_path}")
                        return final_output_path
                    else:
                        print(f"📄 未进行搜索增强，返回当前报告")
                        return current_output

                except Exception as e:
                    print(f"⚠️ 智能搜索增强失败: {str(e)}")
                    print(f"📄 尝试使用传统搜索增强...")

                    # 备用方案：使用传统搜索增�?                    try:
                        fallback_output_path = self.enhance_report_with_search(
                            current_output, topic, user_confirm=False
                        )

                        if fallback_output_path != current_output:
                            print(f"�?传统搜索增强完成！最终报�? {fallback_output_path}")
                            return fallback_output_path
                        else:
                            print(f"📄 返回当前报告: {current_output}")
                            return current_output
                    except Exception as e2:
                        print(f"⚠️ 传统搜索增强也失�? {str(e2)}")
                        print(f"📄 返回当前报告: {current_output}")
                        return current_output
            else:
                print(f"📄 搜索增强功能已禁�?)
                return current_output

        except KeyboardInterrupt:
            if main_pbar:
                main_pbar.close()
            print(f"\n⚠️ 用户中断操作，进度已保存到checkpoint")
            print(f"   当前checkpoint: {self.current_checkpoint_id}")
            print(f"   恢复命令: 使用resume_checkpoint='{self.current_checkpoint_id}'")
            raise
        except Exception as e:
            if main_pbar:
                main_pbar.close()
            print(f"\n�?报告生成失败: {str(e)}")
            if self.current_checkpoint_id:
                print(f"   当前checkpoint: {self.current_checkpoint_id}")
                print(f"   恢复命令: 使用resume_checkpoint='{self.current_checkpoint_id}'")
            raise

    # ==================== 源代码中的后处理方法 ====================

    def embed_images_in_report(self, report_path: str, data_sources: List[str], topic: str, auto_confirm: bool = False) -> str:
        """在报告中嵌入图片（完全按源代码）"""
        try:
            print("🖼�?开始图片嵌入流�?..")

            # 查找图片目录
            image_dirs = []
            for source in data_sources:
                source_path = Path(source)
                if source_path.is_dir():
                    # 查找包含图片的子目录
                    for subdir in source_path.rglob("*"):
                        if subdir.is_dir() and any(subdir.glob("*.png")) or any(subdir.glob("*.jpg")):
                            image_dirs.append(str(subdir))

            if not image_dirs:
                print("📄 未找到图片目录，跳过图片嵌入")
                return report_path

            print(f"📊 找到 {len(image_dirs)} 个图片目�?)

            # 读取报告内容
            with open(report_path, 'r', encoding='utf-8') as f:
                report_content = f.read()

            # 使用图片处理器进行匹�?            from image.image_processor import ImageProcessor
            image_processor = ImageProcessor(self)

            # 分析并匹配图�?            all_matches = []
            for image_dir in image_dirs:
                matches = image_processor.ImageMatcher(self).analyze_report_and_match_images(
                    report_content, image_dir
                )
                all_matches.extend(matches)

            if not all_matches:
                print("📄 未找到合适的图片匹配，跳过图片嵌�?)
                return report_path

            # 生成带图片的报告
            enhanced_content = self._embed_matched_images(report_content, all_matches, image_dirs)

            # 保存增强版报�?            report_file = Path(report_path)
            enhanced_filename = f"{report_file.stem}_with_images{report_file.suffix}"
            enhanced_path = report_file.parent / enhanced_filename

            with open(enhanced_path, 'w', encoding='utf-8') as f:
                f.write(enhanced_content)

            print(f"�?图片嵌入完成: {enhanced_path}")
            return str(enhanced_path)

        except Exception as e:
            print(f"�?图片嵌入失败: {str(e)}")
            return report_path

    def enhance_report_with_tool_calling(self, report_path: str, topic: str, user_confirm: bool = True) -> str:
        """使用工具调用进行搜索增强（完全按源代码）"""
        try:
            print("🔍 开始工具调用搜索增�?..")

            # 读取报告内容
            with open(report_path, 'r', encoding='utf-8') as f:
                report_content = f.read()

            # 分析内容缺口
            from search.search_trigger import SearchTrigger
            search_trigger = SearchTrigger(self)
            gaps = search_trigger.analyze_content_gaps(report_content, topic)

            if not gaps:
                print("📄 未发现需要搜索增强的内容缺口")
                return report_path

            print(f"📊 发现 {len(gaps)} 个内容缺�?)

            # 执行搜索增强
            from search.search_manager import SearchManager
            from search.content_integrator import ContentIntegrator

            search_manager = SearchManager(self)
            content_integrator = ContentIntegrator(self)

            enhanced_content = report_content

            for gap in gaps[:3]:  # 限制处理�?个最重要的缺�?                print(f"🔍 处理缺口: {gap.get('reason', '未知')}")

                # 生成搜索查询
                queries = search_manager.generate_search_queries(topic, gap)

                # 执行搜索
                search_results = []
                for query in queries[:2]:  # 每个缺口最�?个查�?                    results = search_manager.multi_source_search(query, num_results=3)
                    search_results.extend(results)

                # 整合搜索结果
                if search_results:
                    enhanced_content = content_integrator.integrate_search_results(
                        enhanced_content, search_results, gap
                    )

            # 保存增强版报�?            report_file = Path(report_path)
            enhanced_filename = f"{report_file.stem}_enhanced{report_file.suffix}"
            enhanced_path = report_file.parent / enhanced_filename

            with open(enhanced_path, 'w', encoding='utf-8') as f:
                f.write(enhanced_content)

            print(f"�?工具调用搜索增强完成: {enhanced_path}")
            return str(enhanced_path)

        except Exception as e:
            print(f"�?工具调用搜索增强失败: {str(e)}")
            return report_path

    def enhance_report_with_search(self, report_path: str, topic: str, user_confirm: bool = False) -> str:
        """传统搜索增强（完全按源代码）"""
        try:
            print("🔍 开始传统搜索增�?..")

            # 简化的传统搜索增强
            # 这里可以实现基础的搜索功�?            print("📄 传统搜索增强功能开发中...")
            return report_path

        except Exception as e:
            print(f"�?传统搜索增强失败: {str(e)}")
            return report_path

    def _embed_matched_images(self, content: str, matches: List[Dict], image_dirs: List[str]) -> str:
        """将匹配的图片嵌入到内容中"""
        enhanced_content = content

        for match in matches:
            image_id = match.get('image_id', '')
            caption = match.get('caption_suggestion', f'图片: {image_id}')

            # 简单的图片嵌入标记
            image_marker = f"\n\n![{caption}]({image_id})\n\n"

            # 在适当位置插入图片
            # 这里可以根据match中的位置信息进行更精确的插入
            enhanced_content += image_marker

        return enhanced_content

    # ==================== 必要的辅助方�?====================

    def read_framework_file_content(self, framework_file_path: str) -> str:
        """读取框架文件内容"""
        try:
            framework_path = Path(framework_file_path)
            if framework_path.exists():
                with open(framework_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                print(f"�?成功读取框架文件: {framework_file_path}")
                return content
            else:
                print(f"⚠️ 框架文件不存�? {framework_file_path}")
                return ""
        except Exception as e:
            print(f"�?读取框架文件失败: {str(e)}")
            return ""

    def _generate_complete_substructure_with_progress(self, sections: List[Dict[str, Any]], topic: str):
        """生成完整子结构（带进度显示）"""
        for i, section in enumerate(sections):
            print(f"   🔄 生成�?{i+1} 章节子结�? {section.get('title', '未知')}")
            # 简化的子结构生�?            section["children"] = []
            section["content"] = ""

    def _preprocess_all_data_sources(self, data_sources: List[str]) -> str:
        """预处理所有数据源"""
        try:
            from utils.complete_file_reader import CompleteFileReader
            file_reader = CompleteFileReader()

            all_content = []
            for source in data_sources:
                content = file_reader.read_file_or_directory(source)
                if content:
                    all_content.append(content)

            return "\n\n".join(all_content)
        except Exception as e:
            print(f"⚠️ 数据源预处理失败: {str(e)}")
            return ""

    def _generate_all_content_with_data(self, framework: Dict[str, Any], processed_data: str) -> Dict[str, Any]:
        """使用处理后的数据生成所有内�?""
        sections = framework.get("sections", [])

        for i, section in enumerate(sections):
            print(f"   📝 生成�?{i+1} 章节内容: {section.get('title', '未知')}")

            # 生成内容
            prompt = f"""
为报告章�?{section.get('title', '')}"生成详细内容�?
参考数据：
{processed_data[:2000] if processed_data else "无特定数据，请基于专业知识生�?}

要求�?1. 内容专业、详�?2. 字数控制�?000-1500�?3. 包含具体分析和见�?4. 逻辑清晰，结构完�?
请生成该章节的详细内容：
"""

            content = self.call_executor_model(prompt)
            cleaned_content = self.content_cleaner.clean_model_response(content)
            section["content"] = cleaned_content

        return framework

    def _iterative_optimization(self, framework: Dict[str, Any], processed_data: str, topic: str) -> Dict[str, Any]:
        """3轮迭代优化（简化版�?""
        sections = framework.get("sections", [])

        for iteration in range(1, 4):
            print(f"   🔧 第{iteration}轮优�?)

            for section in sections:
                title = section.get("title", "")
                content = section.get("content", "")

                if content:
                    prompt = f"""
优化报告章节"{title}"的内容质量�?
当前内容�?{content}

主题：{topic}

优化要求�?1. 提升专业性和深度
2. 优化语言表达
3. 确保逻辑清晰
4. 保持内容完整�?
请提供优化后的内容：
"""

                    optimized_content = self.call_orchestrator_model(prompt)
                    cleaned_content = self.content_cleaner.clean_model_response(optimized_content)
                    section["content"] = cleaned_content

        return framework

    def _generate_final_document(self, topic: str, framework: Dict[str, Any], processed_data: str) -> str:
        """生成最终文�?""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_path = f"output/{topic}_{timestamp}.txt"

            # 确保输出目录存在
            Path("output").mkdir(exist_ok=True)

            with open(output_path, 'w', encoding='utf-8') as f:
                # 写入标题
                f.write(f"{topic}\n")
                f.write("=" * len(topic) + "\n\n")
                f.write(f"生成时间：{datetime.now().strftime('%Y�?m�?d�?)}\n\n")

                # 写入内容
                sections = framework.get("sections", [])
                for i, section in enumerate(sections, 1):
                    title = section.get("title", "")
                    content = section.get("content", "")

                    f.write(f"## {i}. {title}\n\n")
                    if content:
                        f.write(f"{content}\n\n")

            print(f"�?文档已生�? {output_path}")
            return output_path

        except Exception as e:
            print(f"�?文档生成失败: {str(e)}")
            return ""

    async def generate_report_async(self,
                                   topic: str,
                                   data_sources: List[str],
                                   framework_file_path: Optional[str] = None) -> str:
        """生成完整报告（异步版本，简化实现）"""
        print(f"🚀 开始异步生成报�? {topic}")

        # 异步版本暂时委托给同步版�?        # 未来可以实现真正的异步并行处�?        return self.generate_report_sync(topic, data_sources, framework_file_path)
            framework_path = Path(framework_file_path)
            if framework_path.exists():
                with open(framework_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                print(f"�?成功读取框架文件: {framework_file_path}")
                return content
            else:
                print(f"⚠️ 框架文件不存�? {framework_file_path}")
                return ""
        except Exception as e:
            print(f"�?读取框架文件失败: {str(e)}")
            return ""

    def generate_framework(self, topic: str, framework_content: str = "") -> Dict[str, Any]:
        """生成报告框架（同步版本）"""
        prompt = f"""
您是一位资深的产业研究专家，需要为"{topic}"生成一个专业的研究报告框架�?
参考框架内容：
{framework_content if framework_content else "无参考框架，请根据主题自行设�?}

要求�?1. 生成{self.config.max_depth}级标题结�?2. 确保逻辑清晰、层次分�?3. 符合产业研究报告的专业标�?4. 返回JSON格式

请返回以下JSON格式�?{{
    "title": "报告标题",
    "sections": [
        {{
            "title": "一级标�?,
            "level": 1,
            "children": []
        }}
    ]
}}
"""

        response = self.call_orchestrator_model(prompt)
        return self._parse_framework_response(response)

    async def generate_framework_async(self, topic: str, framework_content: str = "") -> Dict[str, Any]:
        """生成报告框架（异步版本）"""
        prompt = f"""
您是一位资深的产业研究专家，需要为"{topic}"生成一个专业的研究报告框架�?
参考框架内容：
{framework_content if framework_content else "无参考框架，请根据主题自行设�?}

要求�?1. 生成{self.config.max_depth}级标题结�?2. 确保逻辑清晰、层次分�?3. 符合产业研究报告的专业标�?4. 返回JSON格式

请返回以下JSON格式�?{{
    "title": "报告标题",
    "sections": [
        {{
            "title": "一级标�?,
            "level": 1,
            "children": []
        }}
    ]
}}
"""

        response = await self.call_orchestrator_model_async(prompt)
        return self._parse_framework_response(response)

    def _parse_framework_response(self, response: str) -> Dict[str, Any]:
        """解析框架响应"""
        try:
            import json

            # 尝试提取JSON
            if "```json" in response:
                start = response.find("```json") + 7
                end = response.find("```", start)
                if end != -1:
                    json_str = response[start:end].strip()
                    return json.loads(json_str)

            # 如果没有代码块，尝试直接解析
            if response.strip().startswith("{"):
                return json.loads(response.strip())

            # 如果解析失败，返回默认框�?            print("⚠️ 框架解析失败，使用默认框�?)
            return self._get_default_framework()

        except Exception as e:
            print(f"�?框架解析错误: {str(e)}")
            return self._get_default_framework()

    def _get_default_framework(self) -> Dict[str, Any]:
        """获取默认框架"""
        return {
            "title": "产业研究报告",
            "sections": [
                {"title": "行业概述", "level": 1, "children": []},
                {"title": "市场分析", "level": 1, "children": []},
                {"title": "竞争格局", "level": 1, "children": []},
                {"title": "技术发�?, "level": 1, "children": []},
                {"title": "发展趋势", "level": 1, "children": []},
                {"title": "投资建议", "level": 1, "children": []}
            ]
        }

    def _generate_complete_substructure(self, framework: Dict[str, Any], topic: str) -> Dict[str, Any]:
        """生成完整子结构（同步版本�?""
        sections = framework.get("sections", [])

        for i, section in enumerate(sections):
            print(f"   🔄 生成�?{i+1} 章节子结�? {section.get('title', '未知')}")

            # 为每个一级标题生成子标题
            children = self._generate_subsections(section, topic, 2)
            section["children"] = children

        return framework

    async def _generate_complete_substructure_async(self, framework: Dict[str, Any], topic: str) -> Dict[str, Any]:
        """生成完整子结构（异步版本�?""
        sections = framework.get("sections", [])

        # 异步并行生成所有章节的子结�?        tasks = []
        for section in sections:
            task = self._generate_subsections_async(section, topic, 2)
            tasks.append(task)

        results = await asyncio.gather(*tasks)

        for i, section in enumerate(sections):
            section["children"] = results[i]

        return framework

    def _generate_subsections(self, parent_section: Dict[str, Any], topic: str, level: int) -> List[Dict[str, Any]]:
        """生成子章节（同步版本�?""
        if level > self.config.max_depth:
            return []

        parent_title = parent_section.get("title", "")

        prompt = f"""
�?{topic}"报告中的"{parent_title}"章节生成{level}级子标题�?
要求�?1. 生成3-6个子标题
2. 逻辑清晰，层次分�?3. 符合产业研究报告标准
4. 返回JSON格式

请返回JSON格式�?[
    {{"title": "子标�?", "level": {level}}},
    {{"title": "子标�?", "level": {level}}}
]
"""

        response = self.call_orchestrator_model(prompt)
        return self._parse_subsections_response(response, level)

    async def _generate_subsections_async(self, parent_section: Dict[str, Any], topic: str, level: int) -> List[Dict[str, Any]]:
        """生成子章节（异步版本�?""
        if level > self.config.max_depth:
            return []

        parent_title = parent_section.get("title", "")

        prompt = f"""
�?{topic}"报告中的"{parent_title}"章节生成{level}级子标题�?
要求�?1. 生成3-6个子标题
2. 逻辑清晰，层次分�?3. 符合产业研究报告标准
4. 返回JSON格式

请返回JSON格式�?[
    {{"title": "子标�?", "level": {level}}},
    {{"title": "子标�?", "level": {level}}}
]
"""

        response = await self.call_orchestrator_model_async(prompt)
        return self._parse_subsections_response(response, level)

    def _parse_subsections_response(self, response: str, level: int) -> List[Dict[str, Any]]:
        """解析子章节响�?""
        try:
            import json

            # 尝试提取JSON
            if "```json" in response:
                start = response.find("```json") + 7
                end = response.find("```", start)
                if end != -1:
                    json_str = response[start:end].strip()
                    subsections = json.loads(json_str)
            elif response.strip().startswith("["):
                subsections = json.loads(response.strip())
            else:
                return []

            # 确保每个子章节都有正确的level和children
            for subsection in subsections:
                subsection["level"] = level
                subsection["children"] = []
                subsection["content"] = ""

            return subsections

        except Exception as e:
            print(f"�?子章节解析错�? {str(e)}")
            return []

    def _generate_all_content(self, framework: Dict[str, Any], data_sources: List[str]) -> Dict[str, Any]:
        """生成所有节点内容（同步版本�?""
        sections = framework.get("sections", [])

        # 计算总节点数
        total_nodes = self._count_all_nodes_in_framework(framework)
        print(f"   📊 总共需要生�?{total_nodes} 个节点的内容")

        # 逐级生成内容
        for level in range(1, self.config.max_depth + 1):
            level_nodes = self._collect_nodes_by_level(sections, level)
            if level_nodes:
                print(f"   🔄 生成�?{level} 级内�?({len(level_nodes)} 个节�?")
                for i, (node, section_idx) in enumerate(level_nodes):
                    self._generate_single_node_content(node, data_sources, section_idx)
                    print(f"      �?完成 {i+1}/{len(level_nodes)}: {node.get('title', '未知')}")

        return framework

    async def _generate_all_content_async(self, framework: Dict[str, Any], data_sources: List[str]) -> Dict[str, Any]:
        """生成所有节点内容（异步版本�?""
        sections = framework.get("sections", [])

        # 计算总节点数
        total_nodes = self._count_all_nodes_in_framework(framework)
        print(f"   📊 总共需要生�?{total_nodes} 个节点的内容")

        # 逐级异步生成内容
        for level in range(1, self.config.max_depth + 1):
            level_nodes = self._collect_nodes_by_level(sections, level)
            if level_nodes:
                print(f"   🔄 异步生成�?{level} 级内�?({len(level_nodes)} 个节�?")

                # 创建异步任务
                tasks = []
                for node, section_idx in level_nodes:
                    task = self._generate_single_node_content_async(node, data_sources, section_idx)
                    tasks.append(task)

                # 并行执行
                await asyncio.gather(*tasks)
                print(f"      �?完成�?{level} 级所有内容生�?)

        return framework

    def _count_all_nodes_in_framework(self, framework: Dict[str, Any]) -> int:
        """计算框架中的总节点数"""
        sections = framework.get("sections", [])
        return self._count_all_nodes(sections)

    def _count_all_nodes(self, sections: List[Dict[str, Any]]) -> int:
        """递归计算节点总数"""
        count = 0
        for section in sections:
            count += 1  # 当前节点
            if "children" in section and section["children"]:
                count += self._count_all_nodes(section["children"])
        return count

    def _collect_nodes_by_level(self, sections: List[Dict[str, Any]], target_level: int) -> List[tuple]:
        """收集指定层级的所有节�?""
        nodes = []

        def collect_recursive(node: Dict[str, Any], section_idx: int, current_level: int = 1):
            if current_level == target_level:
                nodes.append((node, section_idx))
            elif current_level < target_level and "children" in node:
                for child in node["children"]:
                    collect_recursive(child, section_idx, current_level + 1)

        for i, section in enumerate(sections):
            collect_recursive(section, i)

        return nodes

    def _generate_single_node_content(self, node: Dict[str, Any], data_sources: List[str], section_idx: int):
        """生成单个节点内容（同步版本）"""
        title = node.get("title", "")
        level = node.get("level", 1)

        # 读取相关数据�?        data_content = ""
        if section_idx < len(data_sources):
            data_content = self._read_data_source(data_sources[section_idx])

        # 生成内容的prompt
        prompt = self._create_content_generation_prompt(title, level, data_content)

        # 调用执行模型生成内容
        content = self.call_executor_model(prompt)

        # 清理并保存内�?        cleaned_content = self.content_cleaner.clean_model_response(content)
        node["content"] = cleaned_content

    async def _generate_single_node_content_async(self, node: Dict[str, Any], data_sources: List[str], section_idx: int):
        """生成单个节点内容（异步版本）"""
        title = node.get("title", "")
        level = node.get("level", 1)

        # 读取相关数据�?        data_content = ""
        if section_idx < len(data_sources):
            data_content = self._read_data_source(data_sources[section_idx])

        # 生成内容的prompt
        prompt = self._create_content_generation_prompt(title, level, data_content)

        # 调用执行模型生成内容
        content = await self.call_executor_model_async(prompt)

        # 清理并保存内容
        cleaned_content = self.content_cleaner.clean_model_response(content)
        node["content"] = cleaned_content

    def _read_data_source(self, data_source: str) -> str:
        """读取数据源内容"""
        try:
            from utils.complete_file_reader import CompleteFileReader
            file_reader = CompleteFileReader()

            data_path = Path(data_source)
            if data_path.is_file():
                return file_reader.read_file(str(data_path))
            elif data_path.is_dir():
                return file_reader.read_directory(str(data_path))
            else:
                return ""
        except Exception as e:
            print(f"⚠️ 读取数据源失败 {data_source}: {str(e)}")
            return ""

    def _create_content_generation_prompt(self, title: str, level: int, data_content: str) -> str:
        """创建内容生成的prompt"""
        prompt = f"""
您是一位专业的产业研究专家，需要为报告章节"{title}"生成详细内容。

章节级别：第{level}级标题
参考数据：
{data_content[:3000] if data_content else "无特定数据，请基于专业知识生成"}

要求：
1. 内容专业、详实，符合产业研究报告标准
2. 字数控制在800-1500字之间
3. 包含具体数据、分析和见解
4. 逻辑清晰，结构完整
5. 避免空洞的表述，提供有价值的信息

请生成该章节的详细内容：
"""
        return prompt

    # ==================== 主要生成方法（完全按源代码逻辑） ====================

    def generate_report(self,
                       topic: str,
                       data_sources: List[str],
                       framework_file_path: Optional[str] = None,
                       resume_checkpoint: str = None) -> str:
        """生成完整报告（主入口，支持checkpoint恢复）"""

        # 检查是否需要从checkpoint恢复
        if resume_checkpoint:
            print(f"🔄 从checkpoint恢复: {resume_checkpoint}")
            return self._resume_from_checkpoint(resume_checkpoint, topic, data_sources, framework_file_path)

        if self.use_async:
            # 使用异步版本
            import asyncio
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            try:
                return loop.run_until_complete(
                    self.generate_report_async(topic, data_sources, framework_file_path)
                )
            finally:
                loop.close()
        else:
            # 使用同步版本
            return self.generate_report_sync(topic, data_sources, framework_file_path)
