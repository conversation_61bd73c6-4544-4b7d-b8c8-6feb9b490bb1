"""
报告状态定义模块
定义LangGraph工作流中使用的状态结构
"""
from typing import Dict, List, Any, Optional, Union
from datetime import datetime
from enum import Enum
from pydantic import BaseModel, Field


class SeverityLevel(str, Enum):
    """问题严重程度"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


class QualityDimension(str, Enum):
    """质量检查维度"""
    COHERENCE = "coherence"  # 连贯性
    CONSISTENCY = "consistency"  # 一致性
    ACCURACY = "accuracy"  # 准确性
    COMPLETENESS = "completeness"  # 完整性
    COMPREHENSIVENESS = "comprehensiveness"  # 全面性
    RIGOR = "rigor"  # 严谨性


class IterationPhase(str, Enum):
    """迭代阶段"""
    GENERATION = "generation"
    QUALITY_CHECK = "quality_check"
    IMPROVEMENT = "improvement"
    COMPLETED = "completed"


class QualityIssue(BaseModel):
    """质量问题描述"""
    dimension: QualityDimension
    severity: SeverityLevel
    description: str
    location: str  # 问题位置（节点路径）
    suggestion: str  # 改进建议
    detected_at: datetime = Field(default_factory=datetime.now)


class ContentImprovement(BaseModel):
    """内容改进记录"""
    target_location: str  # 改进目标位置
    improvement_type: str  # 改进类型
    original_content: str  # 原始内容
    improved_content: str  # 改进后内容
    improvement_reason: str  # 改进原因
    applied_at: datetime = Field(default_factory=datetime.now)


class QualityMetrics(BaseModel):
    """质量评估指标"""
    coherence_score: float = 0.0
    consistency_score: float = 0.0
    accuracy_score: float = 0.0
    completeness_score: float = 0.0
    comprehensiveness_score: float = 0.0
    rigor_score: float = 0.0
    overall_score: float = 0.0

    def calculate_overall_score(self, weights: Optional[Dict[str, float]] = None) -> float:
        """计算总体质量分数"""
        if weights is None:
            weights = {
                "coherence": 0.2,
                "consistency": 0.2,
                "accuracy": 0.2,
                "completeness": 0.15,
                "comprehensiveness": 0.15,
                "rigor": 0.1
            }

        self.overall_score = (
            self.coherence_score * weights.get("coherence", 0.2) +
            self.consistency_score * weights.get("consistency", 0.2) +
            self.accuracy_score * weights.get("accuracy", 0.2) +
            self.completeness_score * weights.get("completeness", 0.15) +
            self.comprehensiveness_score * weights.get("comprehensiveness", 0.15) +
            self.rigor_score * weights.get("rigor", 0.1)
        )
        return self.overall_score


class ReportNode(BaseModel):
    """报告节点结构"""
    title: str
    level: int
    content: str = ""
    children: List['ReportNode'] = Field(default_factory=list)
    metadata: Dict[str, Any] = Field(default_factory=dict)

    # 多轮迭代相关字段
    quality_metrics: QualityMetrics = Field(default_factory=QualityMetrics)
    quality_issues: List[QualityIssue] = Field(default_factory=list)
    improvements: List[ContentImprovement] = Field(default_factory=list)
    iteration_history: List[str] = Field(default_factory=list)  # 每轮迭代的内容历史

    class Config:
        arbitrary_types_allowed = True

    def get_node_path(self) -> str:
        """获取节点路径标识"""
        return f"level_{self.level}_{self.title}"

    def add_quality_issue(self, issue: QualityIssue):
        """添加质量问题"""
        self.quality_issues.append(issue)

    def add_improvement(self, improvement: ContentImprovement):
        """添加改进记录"""
        self.improvements.append(improvement)
        # 保存当前内容到历史
        self.iteration_history.append(self.content)
        # 应用改进
        self.content = improvement.improved_content

    def get_quality_score(self) -> float:
        """获取节点质量分数"""
        return self.quality_metrics.overall_score


class GenerationPhaseResult(BaseModel):
    """生成阶段结果"""
    phase: IterationPhase
    iteration_number: int
    start_time: datetime = Field(default_factory=datetime.now)
    end_time: Optional[datetime] = None
    duration_seconds: float = 0.0
    nodes_processed: int = 0
    content_generated: Dict[str, str] = Field(default_factory=dict)  # node_path -> content
    success: bool = True
    error_message: Optional[str] = None

    def mark_completed(self):
        """标记阶段完成"""
        self.end_time = datetime.now()
        if self.start_time:
            self.duration_seconds = (self.end_time - self.start_time).total_seconds()


class QualityCheckPhaseResult(BaseModel):
    """质量检查阶段结果"""
    iteration_number: int
    start_time: datetime = Field(default_factory=datetime.now)
    end_time: Optional[datetime] = None
    duration_seconds: float = 0.0
    nodes_checked: int = 0
    total_issues_found: int = 0
    issues_by_severity: Dict[SeverityLevel, int] = Field(default_factory=dict)
    issues_by_dimension: Dict[QualityDimension, int] = Field(default_factory=dict)
    average_quality_score: float = 0.0
    quality_improved: bool = False
    success: bool = True
    error_message: Optional[str] = None

    def mark_completed(self):
        """标记阶段完成"""
        self.end_time = datetime.now()
        if self.start_time:
            self.duration_seconds = (self.end_time - self.start_time).total_seconds()


class IterationStepResult(BaseModel):
    """单步迭代结果"""
    iteration_number: int
    generation_result: Optional[GenerationPhaseResult] = None
    quality_check_result: Optional[QualityCheckPhaseResult] = None
    improvements_applied: int = 0
    quality_score_before: float = 0.0
    quality_score_after: float = 0.0
    quality_improvement: float = 0.0
    convergence_achieved: bool = False
    should_continue: bool = True

    def calculate_improvement(self):
        """计算质量改进"""
        self.quality_improvement = self.quality_score_after - self.quality_score_before
        # 如果改进很小，可能已经收敛
        if abs(self.quality_improvement) < 0.05:
            self.convergence_achieved = True
            self.should_continue = False


class IterationResult(BaseModel):
    """完整迭代结果"""
    total_iterations: int = 0
    iteration_steps: List[IterationStepResult] = Field(default_factory=list)
    final_quality_score: float = 0.0
    total_improvements: int = 0
    convergence_achieved: bool = False
    start_time: datetime = Field(default_factory=datetime.now)
    end_time: Optional[datetime] = None
    total_duration_seconds: float = 0.0

    def add_iteration_step(self, step: IterationStepResult):
        """添加迭代步骤"""
        self.iteration_steps.append(step)
        self.total_iterations = len(self.iteration_steps)
        self.total_improvements += step.improvements_applied
        if step.convergence_achieved:
            self.convergence_achieved = True

    def mark_completed(self):
        """标记迭代完成"""
        self.end_time = datetime.now()
        if self.start_time:
            self.total_duration_seconds = (self.end_time - self.start_time).total_seconds()


class ReportState(BaseModel):
    """
    报告生成工作流的状态
    """
    # 基本信息
    topic: str = ""
    framework_file: str = ""
    data_sources: List[str] = Field(default_factory=list)

    # 报告结构
    report_structure: Optional[Dict[str, Any]] = None
    report_nodes: List[ReportNode] = Field(default_factory=list)

    # 生成状态
    current_phase: str = "initialization"
    current_iteration: int = 0
    max_iterations: int = 3

    # 内容管理
    generated_content: Dict[str, str] = Field(default_factory=dict)
    source_citations: Dict[str, List[str]] = Field(default_factory=dict)

    # 多轮迭代状态
    iteration_result: IterationResult = Field(default_factory=IterationResult)
    current_iteration_step: Optional[IterationStepResult] = None
    quality_threshold: float = 0.8  # 质量阈值
    convergence_threshold: float = 0.05  # 收敛阈值

    # 错误和日志
    errors: List[Dict[str, Any]] = Field(default_factory=list)
    warnings: List[str] = Field(default_factory=list)

    # 时间戳
    created_at: datetime = Field(default_factory=datetime.now)
    updated_at: datetime = Field(default_factory=datetime.now)

    # 检查点信息
    last_checkpoint: Optional[str] = None
    checkpoint_enabled: bool = True
    
    class Config:
        arbitrary_types_allowed = True

    def update_phase(self, phase: str):
        """更新当前阶段"""
        self.current_phase = phase
        self.updated_at = datetime.now()

    def add_error(self, error: str, node: str = "", details: Any = None):
        """添加错误信息"""
        self.errors.append({
            "timestamp": datetime.now(),
            "node": node,
            "error": error,
            "details": details
        })

    def add_warning(self, warning: str):
        """添加警告信息"""
        self.warnings.append(warning)

    def increment_iteration(self):
        """增加迭代计数"""
        self.current_iteration += 1
        self.updated_at = datetime.now()

    def should_continue_iteration(self) -> bool:
        """判断是否应该继续迭代"""
        # 检查是否达到最大迭代次数
        if self.current_iteration >= self.max_iterations:
            return False

        # 检查是否已经收敛
        if self.iteration_result.convergence_achieved:
            return False

        # 检查当前质量是否已经达到阈值
        current_quality = self.get_current_quality_score()
        if current_quality >= self.quality_threshold:
            return False

        return True

    def start_new_iteration_step(self) -> IterationStepResult:
        """开始新的迭代步骤"""
        self.current_iteration_step = IterationStepResult(
            iteration_number=self.current_iteration + 1,
            quality_score_before=self.get_current_quality_score()
        )
        return self.current_iteration_step

    def complete_current_iteration_step(self):
        """完成当前迭代步骤"""
        if self.current_iteration_step:
            self.current_iteration_step.quality_score_after = self.get_current_quality_score()
            self.current_iteration_step.calculate_improvement()
            self.iteration_result.add_iteration_step(self.current_iteration_step)
            self.current_iteration_step = None

    def get_current_quality_score(self) -> float:
        """获取当前整体质量分数"""
        if not self.report_structure or "sections" not in self.report_structure:
            return 0.0

        total_score = 0.0
        node_count = 0

        def calculate_section_score(section: Dict[str, Any]) -> float:
            nonlocal total_score, node_count

            # 如果有质量指标，使用它
            if "quality_metrics" in section:
                metrics = section["quality_metrics"]
                if isinstance(metrics, dict) and "overall_score" in metrics:
                    total_score += metrics["overall_score"]
                    node_count += 1

            # 递归处理子节点
            if "children" in section:
                for child in section["children"]:
                    calculate_section_score(child)

        sections = self.report_structure.get("sections", [])
        for section in sections:
            calculate_section_score(section)

        return total_score / node_count if node_count > 0 else 0.0

    def get_total_quality_issues(self) -> int:
        """获取总质量问题数量"""
        total_issues = 0

        def count_section_issues(section: Dict[str, Any]):
            nonlocal total_issues
            if "quality_issues" in section:
                total_issues += len(section["quality_issues"])

            if "children" in section:
                for child in section["children"]:
                    count_section_issues(child)

        if self.report_structure and "sections" in self.report_structure:
            sections = self.report_structure.get("sections", [])
            for section in sections:
                count_section_issues(section)

        return total_issues
    
    def to_checkpoint_dict(self) -> Dict[str, Any]:
        """转换为可保存的检查点格式"""
        checkpoint_data = {
            "topic": self.topic,
            "framework_file": self.framework_file,
            "data_sources": self.data_sources,
            "report_structure": self.report_structure,
            "current_phase": self.current_phase,
            "current_iteration": self.current_iteration,
            "max_iterations": self.max_iterations,
            "generated_content": self.generated_content,
            "source_citations": self.source_citations,
            "quality_threshold": self.quality_threshold,
            "convergence_threshold": self.convergence_threshold,
            "created_at": self.created_at.isoformat(),
            "updated_at": self.updated_at.isoformat()
        }

        # 序列化迭代结果
        if self.iteration_result:
            checkpoint_data["iteration_result"] = self.iteration_result.dict()

        # 序列化当前迭代步骤
        if self.current_iteration_step:
            checkpoint_data["current_iteration_step"] = self.current_iteration_step.dict()

        return checkpoint_data

    @classmethod
    def from_checkpoint_dict(cls, data: Dict[str, Any]) -> 'ReportState':
        """从检查点数据恢复状态"""
        state = cls()
        state.topic = data.get("topic", "")
        state.framework_file = data.get("framework_file", "")
        state.data_sources = data.get("data_sources", [])
        state.report_structure = data.get("report_structure")
        state.current_phase = data.get("current_phase", "initialization")
        state.current_iteration = data.get("current_iteration", 0)
        state.max_iterations = data.get("max_iterations", 3)
        state.generated_content = data.get("generated_content", {})
        state.source_citations = data.get("source_citations", {})
        state.quality_threshold = data.get("quality_threshold", 0.8)
        state.convergence_threshold = data.get("convergence_threshold", 0.05)

        # 恢复时间戳
        if "created_at" in data:
            state.created_at = datetime.fromisoformat(data["created_at"])
        if "updated_at" in data:
            state.updated_at = datetime.fromisoformat(data["updated_at"])

        # 恢复迭代结果
        if "iteration_result" in data:
            state.iteration_result = IterationResult(**data["iteration_result"])

        # 恢复当前迭代步骤
        if "current_iteration_step" in data:
            state.current_iteration_step = IterationStepResult(**data["current_iteration_step"])

        return state
