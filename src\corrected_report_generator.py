"""
修正的报告生成器
严格按照用户需求实现：
1. 统筹模型读取框架文件并生成报告框架
2. 执行模型按框架生成具体内容
3. 从上到下生成+从下到上检查，重复3遍
"""
import time
import json
from typing import Dict, Any, List, Optional
from datetime import datetime
from pathlib import Path

from .state import ReportState
from .config import Config
from .logger import ProcessLogger, setup_logger, APICallLogger
from .enhanced_llm_client import EnhancedLLMClient, FrameworkReader
from .context_manager import ContextManager
from .word_generator import WordGenerator


class CorrectedReportGenerator:
    """
    修正的报告生成器
    严格按照用户需求实现完整流程
    """
    
    def __init__(self, config_path: str = "config.yaml"):
        """初始化报告生成器"""
        # 加载配置
        self.config = Config.from_yaml(config_path)
        self.config.ensure_directories()
        
        # 初始化日志系统
        self.logger = setup_logger(self.config.logging.dict())
        self.process_logger = ProcessLogger(self.logger)
        self.api_logger = APICallLogger(self.logger, self.config.logging.dict())
        
        # 初始化核心组件
        self.llm_client = EnhancedLLMClient(self.config, self.api_logger)
        self.framework_reader = FrameworkReader(self.process_logger.logger)
        self.context_manager = ContextManager(self.config, self.process_logger)
        self.word_generator = WordGenerator(self.config, self.process_logger)
        
        self.process_logger.logger.info("修正的报告生成器初始化完成")
    
    def generate_report(
        self,
        topic: str,
        data_sources: List[str],
        framework_file_path: Optional[str] = None
    ) -> str:
        """
        生成报告的主入口
        
        Args:
            topic: 报告主题
            data_sources: 8个数据源文件夹路径
            framework_file_path: 框架文件路径
            
        Returns:
            生成的Word文档路径
        """
        self.process_logger.logger.info("开始生成报告")
        self.process_logger.logger.info(f"报告主题: {topic}")
        self.process_logger.logger.info(f"数据源数量: {len(data_sources)}")
        self.process_logger.logger.info(f"框架文件: {framework_file_path}")
        
        start_time = time.time()
        
        try:
            # 创建报告状态
            state = ReportState(
                topic=topic,
                framework_file=framework_file_path or "",
                data_sources=data_sources,
                max_iterations=3
            )
            
            # 第一步：统筹模型读取框架文件并生成报告框架
            state = self._step1_generate_framework(state, framework_file_path)
            
            # 第二步：执行模型按框架生成具体内容
            state = self._step2_generate_content(state)
            
            # 第三步：3轮迭代优化（从上到下生成+从下到上检查）
            state = self._step3_iterative_optimization(state)
            
            # 第四步：生成最终文档
            output_path = self._step4_generate_document(state)
            
            duration = time.time() - start_time
            self.process_logger.logger.info(f"报告生成完成，耗时: {duration:.2f}秒")
            self.process_logger.logger.info(f"输出文件: {output_path}")
            
            return output_path
            
        except Exception as e:
            self.process_logger.logger.error(f"报告生成失败: {str(e)}")
            raise
    
    def _step1_generate_framework(self, state: ReportState, framework_file_path: Optional[str]) -> ReportState:
        """
        第一步：统筹模型读取框架文件并生成报告框架
        """
        self.process_logger.logger.info("🎯 第一步：统筹模型生成报告框架")
        
        # 读取框架文件（如果提供）
        framework_content = ""
        if framework_file_path:
            framework_content = self.framework_reader.read_framework_file(framework_file_path)
            if not self.framework_reader.validate_framework_content(framework_content):
                self.process_logger.logger.warning("框架文件内容无效，将使用默认框架")
                framework_content = ""
        
        # 构建统筹模型的提示词
        prompt = f"""
作为报告统筹模型，请为主题"{state.topic}"设计一份详细的研究报告框架。

{f"请参考以下现有框架：\\n{framework_content}\\n" if framework_content else ""}

要求：
1. 必须包含恰好8个一级标题（对应8个数据源文件夹）
2. 每个一级标题下必须完整扩展到六级子标题
3. 标题层级必须连贯，不能跳级
4. 每个标题都应该有明确的title字段和level字段
5. 框架应该逻辑清晰，层次分明

请以JSON格式返回，结构如下：
{{
    "sections": [
        {{
            "title": "一级标题1",
            "level": 1,
            "children": [
                {{
                    "title": "二级标题1.1",
                    "level": 2,
                    "children": [
                        {{
                            "title": "三级标题1.1.1",
                            "level": 3,
                            "children": [
                                {{
                                    "title": "四级标题1.1.1.1",
                                    "level": 4,
                                    "children": [
                                        {{
                                            "title": "五级标题1.1.1.1.1",
                                            "level": 5,
                                            "children": [
                                                {{
                                                    "title": "六级标题1.1.1.1.1.1",
                                                    "level": 6
                                                }}
                                            ]
                                        }}
                                    ]
                                }}
                            ]
                        }}
                    ]
                }}
            ]
        }}
    ]
}}

确保返回的是有效的JSON格式，包含完整的6级标题结构。
"""
        
        try:
            # 调用统筹模型生成框架
            response = self.llm_client.call_orchestrator_model(
                prompt=prompt,
                response_format="json"
            )
            
            # 验证框架结构
            if not self._validate_framework_structure(response):
                raise ValueError("生成的框架结构不符合要求")
            
            state.report_structure = response
            state.update_phase("framework_generated")
            
            self.process_logger.logger.info("✅ 报告框架生成完成")
            
        except Exception as e:
            self.process_logger.logger.error(f"框架生成失败: {str(e)}")
            raise
        
        return state
    
    def _step2_generate_content(self, state: ReportState) -> ReportState:
        """
        第二步：执行模型按框架生成具体内容
        """
        self.process_logger.logger.info("⚡ 第二步：执行模型生成具体内容")
        
        if not state.report_structure or "sections" not in state.report_structure:
            raise ValueError("报告框架未生成")
        
        sections = state.report_structure["sections"]
        
        # 按层级顺序生成内容（从一级到六级）
        for level in range(1, 7):  # 1到6级
            self.process_logger.logger.info(f"生成第 {level} 级内容")
            nodes_at_level = self._collect_nodes_at_level(sections, level)
            
            for node, section_index in nodes_at_level:
                self._generate_node_content_with_executor(node, section_index, state)
        
        state.update_phase("content_generated")
        self.process_logger.logger.info("✅ 内容生成完成")
        
        return state
    
    def _step3_iterative_optimization(self, state: ReportState) -> ReportState:
        """
        第三步：3轮迭代优化（从上到下生成+从下到上检查）
        """
        self.process_logger.logger.info("🔄 第三步：3轮迭代优化")
        
        for iteration in range(1, 4):  # 3轮迭代
            self.process_logger.logger.info(f"开始第 {iteration} 轮迭代")
            
            # 从上到下生成（1级→6级）
            self._top_down_generation(state, iteration)
            
            # 从下到上检查（6级→1级）
            self._bottom_up_check(state, iteration)
            
            self.process_logger.logger.info(f"第 {iteration} 轮迭代完成")
        
        state.update_phase("optimization_completed")
        self.process_logger.logger.info("✅ 迭代优化完成")
        
        return state
    
    def _step4_generate_document(self, state: ReportState) -> str:
        """
        第四步：生成最终文档
        """
        self.process_logger.logger.info("📄 第四步：生成最终文档")
        
        output_file = f"{state.topic}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.docx"
        output_path = Path(self.config.paths.output_dir) / output_file
        
        document_path = self.word_generator.generate_word_document(
            state, 
            str(output_path)
        )
        
        state.last_checkpoint = document_path
        state.update_phase("completed")
        
        return document_path
    
    def _generate_node_content_with_executor(
        self, 
        node: Dict[str, Any], 
        section_index: int, 
        state: ReportState
    ):
        """使用执行模型生成节点内容"""
        try:
            # 构建上下文
            context = self._build_node_context(node, section_index, state)
            
            # 构建执行模型的提示词
            level = node.get("level", 1)
            title = node.get("title", "")
            
            prompt = f"""
根据统筹模型的安排，请为"{title}"（第{level}级标题）生成详细内容。

要求：
1. 内容应该详细、专业、准确
2. 字数控制在{self._get_word_count_by_level(level)}字
3. 语言专业、逻辑清晰
4. 包含适当的数据引用，格式为[来源: 文件名]
5. 确保内容与标题高度相关

请生成内容：
"""
            
            # 调用执行模型
            content = self.llm_client.call_executor_model(
                prompt=prompt,
                context=context,
                response_format="text"
            )
            
            # 更新节点内容
            node["content"] = content.strip()
            
            # 记录到状态中
            node_key = self._get_node_key(node)
            state.generated_content[node_key] = content.strip()
            
            self.process_logger.logger.debug(f"生成节点内容: {title}")
            
        except Exception as e:
            error_msg = f"生成节点内容失败: {node.get('title', 'Unknown')} - {str(e)}"
            self.process_logger.logger.error(error_msg)
            node["content"] = "内容生成失败，请检查数据源和配置。"
    
    def _top_down_generation(self, state: ReportState, iteration: int):
        """从上到下生成（1级→6级）"""
        self.process_logger.logger.info(f"第{iteration}轮：从上到下生成内容")
        
        sections = state.report_structure["sections"]
        
        for level in range(1, 7):
            nodes_at_level = self._collect_nodes_at_level(sections, level)
            
            for node, section_index in nodes_at_level:
                # 使用执行模型改进内容
                self._improve_node_content_with_executor(node, section_index, state, iteration)
    
    def _bottom_up_check(self, state: ReportState, iteration: int):
        """从下到上检查（6级→1级）"""
        self.process_logger.logger.info(f"第{iteration}轮：从下到上质量检查")
        
        sections = state.report_structure["sections"]
        
        for level in range(6, 0, -1):  # 6级到1级
            nodes_at_level = self._collect_nodes_at_level(sections, level)
            
            for node, section_index in nodes_at_level:
                # 使用统筹模型进行质量检查
                self._check_node_quality_with_orchestrator(node, section_index, state, iteration)
    
    def _improve_node_content_with_executor(
        self, 
        node: Dict[str, Any], 
        section_index: int, 
        state: ReportState, 
        iteration: int
    ):
        """使用执行模型改进节点内容"""
        current_content = node.get("content", "")
        if not current_content:
            return
        
        try:
            context = self._build_node_context(node, section_index, state)
            
            prompt = f"""
这是第{iteration}轮迭代改进。请改进以下内容：

当前内容：
{current_content}

改进要求：
1. 提高内容的专业性和准确性
2. 增强逻辑连贯性
3. 补充必要的细节和数据
4. 确保引用格式正确
5. 保持内容长度适中

请返回改进后的内容：
"""
            
            improved_content = self.llm_client.call_executor_model(
                prompt=prompt,
                context=context,
                response_format="text"
            )
            
            if improved_content and len(improved_content.strip()) > len(current_content) * 0.8:
                node["content"] = improved_content.strip()
                
        except Exception as e:
            self.process_logger.logger.error(f"内容改进失败: {str(e)}")
    
    def _check_node_quality_with_orchestrator(
        self, 
        node: Dict[str, Any], 
        section_index: int, 
        state: ReportState, 
        iteration: int
    ):
        """使用统筹模型检查节点质量"""
        content = node.get("content", "")
        if not content:
            return
        
        try:
            prompt = f"""
作为统筹模型，请检查以下内容的质量：

标题：{node.get('title', '')}
内容：{content}

请从以下维度评估：
1. 连贯性：内容逻辑是否清晰
2. 准确性：信息是否准确可靠
3. 完整性：内容是否充实完整
4. 专业性：表述是否专业严谨

如果发现问题，请提出具体的改进建议。

请以JSON格式返回：
{{
    "quality_score": 8.5,
    "issues": ["问题1", "问题2"],
    "suggestions": ["建议1", "建议2"],
    "needs_improvement": true
}}
"""
            
            quality_check = self.llm_client.call_orchestrator_model(
                prompt=prompt,
                response_format="json"
            )
            
            # 记录质量检查结果
            if isinstance(quality_check, dict):
                node["quality_check"] = quality_check
                
        except Exception as e:
            self.process_logger.logger.error(f"质量检查失败: {str(e)}")
    
    def _build_node_context(self, node: Dict[str, Any], section_index: int, state: ReportState) -> Dict[str, Any]:
        """构建节点上下文"""
        context = {"parts": []}
        
        try:
            # 添加章节特定的数据源
            if section_index < len(state.data_sources):
                source_path = state.data_sources[section_index]
                chapter_context = self.context_manager.prepare_chapter_context(
                    chapter_title=node.get("title", ""),
                    source_path=source_path
                )
                if "parts" in chapter_context:
                    context["parts"].extend(chapter_context["parts"])
            
        except Exception as e:
            self.process_logger.logger.error(f"构建上下文失败: {str(e)}")
        
        return context
    
    def _collect_nodes_at_level(self, sections: List[Dict[str, Any]], target_level: int) -> List[tuple]:
        """收集指定层级的所有节点"""
        nodes = []
        
        def collect_recursive(node: Dict[str, Any], section_idx: int, current_level: int = 1):
            if current_level == target_level:
                nodes.append((node, section_idx))
            elif current_level < target_level and "children" in node:
                for child in node["children"]:
                    collect_recursive(child, section_idx, current_level + 1)
        
        for idx, section in enumerate(sections):
            collect_recursive(section, idx)
        
        return nodes
    
    def _validate_framework_structure(self, framework: Dict[str, Any]) -> bool:
        """验证框架结构"""
        if "sections" not in framework:
            return False
        
        sections = framework["sections"]
        if len(sections) != 8:  # 必须是8个一级章节
            return False
        
        # 检查每个章节是否有标题和层级
        for section in sections:
            if "title" not in section or "level" not in section:
                return False
            if section["level"] != 1:
                return False
        
        return True
    
    def _get_word_count_by_level(self, level: int) -> str:
        """根据层级获取建议字数"""
        word_counts = {
            1: "800-1200",
            2: "600-800", 
            3: "400-600",
            4: "300-500",
            5: "200-400",
            6: "150-300"
        }
        return word_counts.get(level, "300-500")
    
    def _get_node_key(self, node: Dict[str, Any]) -> str:
        """生成节点的唯一键"""
        level = node.get("level", 0)
        title = node.get("title", "")
        return f"level_{level}_{title}"
