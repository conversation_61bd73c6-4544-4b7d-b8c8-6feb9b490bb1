#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
联网搜索模块
独立的联网搜索功能，输入内容即可开启搜索
完全按照源代码中的设计工作流实现
"""

import os
import json
import time
import requests
from typing import List, Dict, Any, Optional
from pathlib import Path


class WebSearchModule:
    """
    联网搜索模块
    
    功能特点：
    1. 支持多个搜索引擎（Metaso、Google、Bing）
    2. 智能搜索策略（优先级、回退机制）
    3. 结果去重和排序
    4. 搜索结果验证和过滤
    5. 简单易用的接口
    """

    def __init__(self):
        """初始化搜索模块"""
        self.search_apis = {}
        self.search_history = []
        self.rate_limits = {
            'metaso': {'last_call': 0, 'min_interval': 1},
            'google': {'last_call': 0, 'min_interval': 1},
            'bing': {'last_call': 0, 'min_interval': 1}
        }
        
        # 初始化搜索API
        self._init_search_apis()
        
        print("🔍 联网搜索模块已初始化")
        self._print_api_status()

    def _init_search_apis(self):
        """初始化搜索API配置"""
        
        # Metaso Search API (优先使用，已提供API Key)
        try:
            metaso_api_key = os.getenv('METASO_API_KEY', 'mk-988A8E4DC50C53312E3D1A8729687F4C')
            if metaso_api_key:
                self.search_apis['metaso'] = {
                    'api_key': metaso_api_key,
                    'enabled': True,
                    'priority': 1
                }
                print("✅ Metaso Search API 已配置")
            else:
                print("⚠️ Metaso Search API 未配置")
        except Exception as e:
            print(f"⚠️ Metaso Search API 配置失败: {str(e)}")

        # Google Custom Search API
        try:
            google_api_key = os.getenv('GOOGLE_SEARCH_API_KEY')
            google_cx = os.getenv('GOOGLE_SEARCH_CX')
            if google_api_key and google_cx:
                self.search_apis['google'] = {
                    'api_key': google_api_key,
                    'cx': google_cx,
                    'enabled': True,
                    'priority': 2
                }
                print("✅ Google Search API 已配置")
            else:
                print("⚠️ Google Search API 未配置")
        except Exception as e:
            print(f"⚠️ Google Search API 配置失败: {str(e)}")

        # Bing Search API
        try:
            bing_api_key = os.getenv('BING_SEARCH_API_KEY')
            if bing_api_key:
                self.search_apis['bing'] = {
                    'api_key': bing_api_key,
                    'enabled': True,
                    'priority': 3
                }
                print("✅ Bing Search API 已配置")
            else:
                print("⚠️ Bing Search API 未配置")
        except Exception as e:
            print(f"⚠️ Bing Search API 配置失败: {str(e)}")

    def _print_api_status(self):
        """打印API状态"""
        available_apis = [name for name, config in self.search_apis.items() if config['enabled']]
        if available_apis:
            print(f"📊 可用搜索引擎: {', '.join(available_apis)}")
        else:
            print("❌ 没有可用的搜索引擎")

    # ==================== 主要搜索接口 ====================

    def search(self, query: str, num_results: int = 10, engine: str = "auto") -> List[Dict[str, Any]]:
        """
        执行搜索（主要接口）
        
        Args:
            query: 搜索查询内容
            num_results: 返回结果数量
            engine: 指定搜索引擎 ("auto", "metaso", "google", "bing")
            
        Returns:
            搜索结果列表
        """
        print(f"🔍 开始搜索: {query}")
        
        if not query.strip():
            print("❌ 搜索查询不能为空")
            return []

        # 记录搜索历史
        self.search_history.append({
            'query': query,
            'timestamp': time.time(),
            'engine': engine
        })

        if engine == "auto":
            # 自动选择最佳搜索引擎
            return self._search_with_auto_selection(query, num_results)
        else:
            # 使用指定搜索引擎
            return self._search_with_engine(query, num_results, engine)

    def multi_search(self, query: str, num_results_per_engine: int = 5) -> List[Dict[str, Any]]:
        """
        使用多个搜索引擎搜索并合并结果
        
        Args:
            query: 搜索查询
            num_results_per_engine: 每个引擎的结果数量
            
        Returns:
            合并后的搜索结果
        """
        print(f"🔍 多引擎搜索: {query}")
        
        all_results = []
        available_engines = [name for name, config in self.search_apis.items() if config['enabled']]
        
        for engine in available_engines:
            print(f"   🔍 使用 {engine} 搜索...")
            results = self._search_with_engine(query, num_results_per_engine, engine)
            all_results.extend(results)
            print(f"   ✅ {engine} 返回 {len(results)} 个结果")
        
        # 合并、去重和排序
        merged_results = self._merge_and_rank_results(all_results)
        print(f"🎉 多引擎搜索完成，合并后共 {len(merged_results)} 个结果")
        
        return merged_results

    def search_with_fallback(self, query: str, num_results: int = 10, preferred_engine: str = "metaso") -> List[Dict[str, Any]]:
        """
        带回退机制的搜索
        
        Args:
            query: 搜索查询
            num_results: 结果数量
            preferred_engine: 首选搜索引擎
            
        Returns:
            搜索结果列表
        """
        print(f"🔍 回退搜索: {query} (首选: {preferred_engine})")
        
        # 按优先级排序引擎
        engines = self._get_engines_by_priority(preferred_engine)
        
        for engine in engines:
            if engine not in self.search_apis or not self.search_apis[engine]['enabled']:
                continue
                
            print(f"   🔍 尝试使用 {engine} 搜索...")
            results = self._search_with_engine(query, num_results, engine)
            
            if results:
                print(f"   ✅ {engine} 搜索成功，返回 {len(results)} 个结果")
                return results
            else:
                print(f"   ⚠️ {engine} 搜索失败，尝试下一个引擎...")
        
        print("❌ 所有搜索引擎都失败了")
        return []

    # ==================== 内部搜索方法 ====================

    def _search_with_auto_selection(self, query: str, num_results: int) -> List[Dict[str, Any]]:
        """自动选择最佳搜索引擎"""
        # 优先使用Metaso（中文优化）
        if 'metaso' in self.search_apis and self.search_apis['metaso']['enabled']:
            return self._search_with_engine(query, num_results, 'metaso')
        
        # 回退到其他引擎
        return self.search_with_fallback(query, num_results, 'google')

    def _search_with_engine(self, query: str, num_results: int, engine: str) -> List[Dict[str, Any]]:
        """使用指定搜索引擎搜索"""
        if engine not in self.search_apis or not self.search_apis[engine]['enabled']:
            print(f"❌ 搜索引擎 {engine} 不可用")
            return []

        # 速率限制检查
        if not self._check_rate_limit(engine):
            print(f"⚠️ {engine} 搜索频率限制，请稍后再试")
            return []

        try:
            if engine == 'metaso':
                return self._search_metaso(query, num_results)
            elif engine == 'google':
                return self._search_google(query, num_results)
            elif engine == 'bing':
                return self._search_bing(query, num_results)
            else:
                print(f"❌ 不支持的搜索引擎: {engine}")
                return []
        except Exception as e:
            print(f"❌ {engine} 搜索失败: {str(e)}")
            return []

    def _search_metaso(self, query: str, num_results: int) -> List[Dict[str, Any]]:
        """使用Metaso API搜索"""
        api_key = self.search_apis['metaso']['api_key']
        
        url = "https://api.metaso.cn/v1/search"
        headers = {
            'Authorization': f'Bearer {api_key}',
            'Content-Type': 'application/json'
        }
        data = {
            'query': query,
            'count': min(num_results, 20)
        }

        response = requests.post(url, headers=headers, json=data, timeout=15)
        response.raise_for_status()

        result = response.json()
        results = []

        for item in result.get('results', []):
            results.append({
                'title': item.get('title', ''),
                'url': item.get('url', ''),
                'snippet': item.get('snippet', ''),
                'source': 'metaso',
                'date': item.get('date', ''),
                'score': item.get('score', 0)
            })

        return results

    def _search_google(self, query: str, num_results: int) -> List[Dict[str, Any]]:
        """使用Google Custom Search API搜索"""
        api_key = self.search_apis['google']['api_key']
        cx = self.search_apis['google']['cx']

        url = "https://www.googleapis.com/customsearch/v1"
        params = {
            'key': api_key,
            'cx': cx,
            'q': query,
            'num': min(num_results, 10),
            'dateRestrict': 'y1'  # 限制在一年内的结果
        }

        response = requests.get(url, params=params, timeout=15)
        response.raise_for_status()

        data = response.json()
        results = []

        for item in data.get('items', []):
            results.append({
                'title': item.get('title', ''),
                'url': item.get('link', ''),
                'snippet': item.get('snippet', ''),
                'source': 'google',
                'date': item.get('displayLink', ''),
                'score': 0
            })

        return results

    def _search_bing(self, query: str, num_results: int) -> List[Dict[str, Any]]:
        """使用Bing Search API搜索"""
        api_key = self.search_apis['bing']['api_key']

        url = "https://api.bing.microsoft.com/v7.0/search"
        headers = {
            'Ocp-Apim-Subscription-Key': api_key
        }
        params = {
            'q': query,
            'count': min(num_results, 10),
            'freshness': 'Year'  # 限制在一年内的结果
        }

        response = requests.get(url, headers=headers, params=params, timeout=15)
        response.raise_for_status()

        data = response.json()
        results = []

        for item in data.get('webPages', {}).get('value', []):
            results.append({
                'title': item.get('name', ''),
                'url': item.get('url', ''),
                'snippet': item.get('snippet', ''),
                'source': 'bing',
                'date': item.get('dateLastCrawled', ''),
                'score': 0
            })

        return results

    # ==================== 工具方法 ====================

    def _check_rate_limit(self, engine: str) -> bool:
        """检查速率限制"""
        current_time = time.time()
        last_call = self.rate_limits[engine]['last_call']
        min_interval = self.rate_limits[engine]['min_interval']
        
        if current_time - last_call >= min_interval:
            self.rate_limits[engine]['last_call'] = current_time
            return True
        return False

    def _get_engines_by_priority(self, preferred_engine: str) -> List[str]:
        """按优先级获取搜索引擎列表"""
        engines = []
        
        # 首选引擎
        if preferred_engine in self.search_apis:
            engines.append(preferred_engine)
        
        # 其他引擎按优先级排序
        other_engines = [(name, config['priority']) for name, config in self.search_apis.items() 
                        if name != preferred_engine and config['enabled']]
        other_engines.sort(key=lambda x: x[1])
        
        engines.extend([name for name, _ in other_engines])
        return engines

    def _merge_and_rank_results(self, results: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """合并和排序搜索结果"""
        # 去重（基于URL）
        seen_urls = set()
        unique_results = []

        for result in results:
            url = result.get('url', '')
            if url and url not in seen_urls:
                seen_urls.add(url)
                unique_results.append(result)

        # 按来源权重排序
        def sort_key(result):
            source = result.get('source', '')
            if source == 'metaso':
                weight = 3
            elif source == 'google':
                weight = 2
            elif source == 'bing':
                weight = 1
            else:
                weight = 0

            score = result.get('score', 0)
            return weight + score * 0.1

        unique_results.sort(key=sort_key, reverse=True)
        return unique_results

    # ==================== 便捷方法 ====================

    def quick_search(self, content: str) -> List[Dict[str, Any]]:
        """
        快速搜索（主要入口）
        输入内容即开启搜索
        
        Args:
            content: 要搜索的内容
            
        Returns:
            搜索结果列表
        """
        if not content.strip():
            print("❌ 搜索内容不能为空")
            return []
        
        print(f"⚡ 快速搜索: {content}")
        return self.search(content, num_results=10, engine="auto")

    def get_search_history(self) -> List[Dict[str, Any]]:
        """获取搜索历史"""
        return self.search_history.copy()

    def clear_search_history(self):
        """清空搜索历史"""
        self.search_history.clear()
        print("🗑️ 搜索历史已清空")

    def get_api_status(self) -> Dict[str, Any]:
        """获取API状态"""
        status = {}
        for name, config in self.search_apis.items():
            status[name] = {
                'enabled': config['enabled'],
                'priority': config.get('priority', 0),
                'has_api_key': bool(config.get('api_key'))
            }
        return status

    def test_all_apis(self) -> Dict[str, bool]:
        """测试所有API"""
        print("🧪 测试所有搜索API...")
        results = {}
        
        test_query = "人工智能"
        
        for engine in self.search_apis:
            if self.search_apis[engine]['enabled']:
                print(f"   🔍 测试 {engine}...")
                try:
                    test_results = self._search_with_engine(test_query, 1, engine)
                    results[engine] = len(test_results) > 0
                    print(f"   {'✅' if results[engine] else '❌'} {engine}: {'成功' if results[engine] else '失败'}")
                except Exception as e:
                    results[engine] = False
                    print(f"   ❌ {engine}: 失败 - {str(e)}")
            else:
                results[engine] = False
                print(f"   ⚠️ {engine}: 未配置")
        
        return results
