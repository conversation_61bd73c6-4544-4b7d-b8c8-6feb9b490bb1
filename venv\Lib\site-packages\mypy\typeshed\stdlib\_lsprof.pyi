import sys
from _typeshed import structseq
from collections.abc import Callable
from types import CodeType
from typing import Any, Final, final

class Profiler:
    def __init__(
        self, timer: Callable[[], float] | None = None, timeunit: float = 0.0, subcalls: bool = True, builtins: bool = True
    ) -> None: ...
    def getstats(self) -> list[profiler_entry]: ...
    def enable(self, subcalls: bool = True, builtins: bool = True) -> None: ...
    def disable(self) -> None: ...
    def clear(self) -> None: ...

@final
class profiler_entry(structseq[Any], tuple[CodeType | str, int, int, float, float, list[profiler_subentry]]):
    if sys.version_info >= (3, 10):
        __match_args__: Final = ("code", "callcount", "reccallcount", "totaltime", "inlinetime", "calls")
    code: CodeType | str
    callcount: int
    reccallcount: int
    totaltime: float
    inlinetime: float
    calls: list[profiler_subentry]

@final
class profiler_subentry(structseq[Any], tuple[CodeType | str, int, int, float, float]):
    if sys.version_info >= (3, 10):
        __match_args__: Final = ("code", "callcount", "reccallcount", "totaltime", "inlinetime")
    code: CodeType | str
    callcount: int
    reccallcount: int
    totaltime: float
    inlinetime: float
