{"node": "setup_and_prepare_sources", "event": "\u8282\u70b9\u5f00\u59cb\u6267\u884c", "logger": "src.logger", "level": "info", "timestamp": "2025-07-18T07:40:55.836500Z"}
{"node": "setup_and_prepare_sources", "duration_seconds": 0.0, "event": "\u8282\u70b9\u6267\u884c\u5b8c\u6210", "logger": "src.logger", "level": "info", "timestamp": "2025-07-18T07:40:55.837864Z"}
{"node": "design_framework", "event": "\u8282\u70b9\u5f00\u59cb\u6267\u884c", "logger": "src.logger", "level": "info", "timestamp": "2025-07-18T07:40:55.842587Z"}
{"model": "gemini-2.5-pro", "operation": "orchestrator_json", "success": true, "duration_seconds": 440.355669260025, "input_tokens": 196, "output_tokens": 9436, "total_tokens": 9632, "estimated_cost_usd": 0.047425, "event": "API\u8c03\u7528\u6210\u529f", "logger": "src.logger", "level": "info", "timestamp": "2025-07-18T07:48:16.198256Z"}
{"node": "design_framework", "duration_seconds": 440.3640160560608, "event": "\u8282\u70b9\u6267\u884c\u5b8c\u6210", "logger": "src.logger", "level": "info", "timestamp": "2025-07-18T07:48:16.206603Z"}
{"node": "generate_content_top_down", "event": "\u8282\u70b9\u5f00\u59cb\u6267\u884c", "logger": "src.logger", "level": "info", "timestamp": "2025-07-18T07:48:16.212358Z"}
{"node": "build_context", "source_path": "data/1_market_overview", "event": "\u8282\u70b9\u5f00\u59cb\u6267\u884c", "logger": "src.logger", "level": "info", "timestamp": "2025-07-18T07:48:16.212358Z"}
{"file_path": "data/1_market_overview\\2022\u5e74\u4e2d\u56fd\u56fa\u6001\u7535\u6c60\u6280\u672f\u53d1\u5c55\u77ed\u62a5\u544a\u2014\u2014\u7535\u6c60\u6280\u672f\u7684\u9769\u547d\u6027\u7a81\u7834\u6458\u8981\u7248.pdf", "file_type": ".pdf", "success": true, "snippets_count": 10, "event": "\u6587\u4ef6\u5904\u7406", "logger": "src.logger", "level": "info", "timestamp": "2025-07-18T07:48:16.394141Z"}
{"file_path": "data/1_market_overview\\2022\u5e74\u4e2d\u56fd\u56fa\u6001\u7535\u6c60\u884c\u4e1a\u5e02\u573a\u524d\u666f\u53ca\u6295\u8d44\u7814\u7a76\u62a5\u544a-\u4eca\u65e5\u5934\u6761.pdf", "file_type": ".pdf", "success": true, "snippets_count": 150, "event": "\u6587\u4ef6\u5904\u7406", "logger": "src.logger", "level": "info", "timestamp": "2025-07-18T07:48:22.285165Z"}
{"file_path": "data/1_market_overview\\2024-05-06_\u6d59\u5546\u8bc1\u5238_\u7535\u529b\u8bbe\u5907_\u56fa\u6001\u7535\u6c60\u6df1\u5ea6\uff1a\u6027\u80fd\u5168\u9762\u9769\u65b0\uff0c\u6750\u6599\u4f53\u7cfb\u8fce\u6765\u589e\u91cf.pdf", "file_type": ".pdf", "success": true, "snippets_count": 43, "event": "\u6587\u4ef6\u5904\u7406", "logger": "src.logger", "level": "info", "timestamp": "2025-07-18T07:48:23.991595Z"}
{"file_path": "data/1_market_overview\\2024-07-31_\u4e2d\u91d1\u516c\u53f8_\u57fa\u7840\u5316\u5de5_\u65b0\u6750\u65992024\u4e0b\u534a\u5e74\u5c55\u671b\uff1a\u805a\u7126\u56fd\u4ea7\u5316\uff0c\u89d2\u9010\u65b0\u8d5b\u9053.pdf", "file_type": ".pdf", "success": true, "snippets_count": 89, "event": "\u6587\u4ef6\u5904\u7406", "logger": "src.logger", "level": "info", "timestamp": "2025-07-18T07:48:27.572964Z"}
Cannot set gray non-stroke color because /'P14' is an invalid float value
{"file_path": "data/1_market_overview\\2024-11-14_\u4ebf\u6b27\u667a\u5e93_\u7535\u529b\u8bbe\u5907_2024\u4e2d\u56fd\u5168\u56fa\u6001\u7535\u6c60\u4ea7\u4e1a\u7814\u7a76\uff1a\u5168\u56fa\u6001\u7535\u6c60\u5373\u5c06\u8fce\u6765\u91cf\u4ea7\u5143\u5e74.pdf", "file_type": ".pdf", "success": true, "snippets_count": 3999, "event": "\u6587\u4ef6\u5904\u7406", "logger": "src.logger", "level": "info", "timestamp": "2025-07-18T07:48:29.765876Z"}
{"file_path": "data/1_market_overview\\AFM\u9ad8\u80fd\u91cf\u5bc6\u5ea6\u7845\u78b3\u8d1f\u6781\u6750\u6599\u7efc\u8ff0.pdf", "file_type": ".pdf", "success": true, "snippets_count": 40, "event": "\u6587\u4ef6\u5904\u7406", "logger": "src.logger", "level": "info", "timestamp": "2025-07-18T07:48:43.475896Z"}
{"file_path": "data/1_market_overview\\LiPON\u56fa\u6001\u7535\u89e3\u8d28\u4e0e\u5168\u56fa...\u819c\u9502\u79bb\u5b50\u7535\u6c60\u5236\u5907\u53ca\u7279\u6027\u7814\u7a76_\u674e\u6797.pdf", "file_type": ".pdf", "success": true, "snippets_count": 415, "event": "\u6587\u4ef6\u5904\u7406", "logger": "src.logger", "level": "info", "timestamp": "2025-07-18T07:48:49.226871Z"}
Multiple definitions in dictionary at byte 0x1b73b6 for key /MediaBox
{"file_path": "data/1_market_overview\\NASICON\u578b\u94a0\u79bb\u5b50\u5bfc\u4f53\u56fa\u6001\u7535\u89e3\u8d28\u7814\u7a76\u8fdb\u5c55_\u82cf\u4f73\u96ef.pdf", "file_type": ".pdf", "success": true, "snippets_count": 15, "event": "\u6587\u4ef6\u5904\u7406", "logger": "src.logger", "level": "info", "timestamp": "2025-07-18T07:48:53.497453Z"}
{"file_path": "data/1_market_overview\\PEO\u57fa\u56fa\u6001\u805a\u5408\u7269\u7535\u89e3\u8d28\u7684\u5236\u5907\u53ca\u7535\u89e3\u8d28 \u6b63\u6781\u754c\u9762\u7814\u7a76.pdf", "file_type": ".pdf", "success": true, "snippets_count": 97, "event": "\u6587\u4ef6\u5904\u7406", "logger": "src.logger", "level": "info", "timestamp": "2025-07-18T07:48:59.129234Z"}
{"file_path": "data/1_market_overview\\sample_data.txt", "file_type": ".txt", "success": true, "snippets_count": 1, "event": "\u6587\u4ef6\u5904\u7406", "logger": "src.logger", "level": "info", "timestamp": "2025-07-18T07:48:59.151523Z"}
{"file_path": "data/1_market_overview\\Solid-State Battery Roadmap 2035+.pdf", "file_type": ".pdf", "success": true, "snippets_count": 2, "event": "\u6587\u4ef6\u5904\u7406", "logger": "src.logger", "level": "info", "timestamp": "2025-07-18T07:49:06.863071Z"}
{"file_path": "data/1_market_overview\\xi2020.pdf", "file_type": ".pdf", "success": true, "snippets_count": 22, "event": "\u6587\u4ef6\u5904\u7406", "logger": "src.logger", "level": "info", "timestamp": "2025-07-18T07:49:15.845350Z"}
{"file_path": "data/1_market_overview\\\u4ea7\u4e1a\u5316\u63d0\u901f\uff0c\u672a\u6765\u53ef\u671f.pdf", "file_type": ".pdf", "success": true, "snippets_count": 43, "event": "\u6587\u4ef6\u5904\u7406", "logger": "src.logger", "level": "info", "timestamp": "2025-07-18T07:49:17.637860Z"}
Could get FontBBox from font descriptor because None cannot be parsed as 4 floats
Cannot set gray stroke color because /'P4' is an invalid float value
Cannot set gray non-stroke color because /'P4' is an invalid float value
{"file_path": "data/1_market_overview\\\u534a\u56fa\u6001\u4e0e\u56fa\u6001\u7535\u6c60\u4e4b\u7535\u6c60\u6280\u672f\u5347\u7ea7\u00a01.1.\u00a0\u5b9a\u4e49\u56fa\u6001\u7535\u6c60\u6307\u4f7f\u7528\u56fa\u6001\u7535\u89e3\u8d28\u4ee3\u66ff\u7535\u89e3\u6db2\u7684\u9502\u7535\u6c60\u3002\u6839\u636e\u56fa\u6001\u7535\u89e3\u8d28\u7528\u91cf\u7684\u5173\u7cfb\uff0c\u53ef\u4ee5\u5c06\u5176\u7ec6\u5206\u4e3a\u534a\u56fa\u6001\u7535\u6c60\u548c\u5168\u56fa\u6001\u7535\u6c60...\u00a0-\u00a0\u96ea\u7403.pdf", "file_type": ".pdf", "success": true, "snippets_count": 61, "event": "\u6587\u4ef6\u5904\u7406", "logger": "src.logger", "level": "info", "timestamp": "2025-07-18T07:49:19.904138Z"}
Cannot set gray stroke color because /'P5' is an invalid float value
Cannot set gray non-stroke color because /'P5' is an invalid float value
{"file_path": "data/1_market_overview\\\u5355\u79bb\u5b50\u5bfc\u4f53\u805a\u5408\u7269\u7535\u89e3\u8d28\u7814\u7a76\u8fdb\u5c55.pdf", "file_type": ".pdf", "success": true, "snippets_count": 62, "event": "\u6587\u4ef6\u5904\u7406", "logger": "src.logger", "level": "info", "timestamp": "2025-07-18T07:49:22.900419Z"}
{"file_path": "data/1_market_overview\\\u5364\u5316\u7269.pdf", "file_type": ".pdf", "success": true, "snippets_count": 89, "event": "\u6587\u4ef6\u5904\u7406", "logger": "src.logger", "level": "info", "timestamp": "2025-07-18T07:49:25.147204Z"}
startxref on same line as offset
Multiple definitions in dictionary at byte 0x1cd9b8 for key /MediaBox
{"file_path": "data/1_market_overview\\\u5364\u5316\u7269\u56fa\u6001\u7535\u89e3\u8d28\u7814\u7a76\u8fdb\u5c55_\u9648\u5e05.pdf", "file_type": ".pdf", "success": true, "snippets_count": 16, "event": "\u6587\u4ef6\u5904\u7406", "logger": "src.logger", "level": "info", "timestamp": "2025-07-18T07:49:30.038162Z"}
{"file_path": "data/1_market_overview\\\u56fa\u6001\u7535\u6c60\u4ea7\u4e1a\u94fe\u6700\u65b0\u8fdb\u5c55\uff082023_2_8\uff09.pdf", "file_type": ".pdf", "success": true, "snippets_count": 8, "event": "\u6587\u4ef6\u5904\u7406", "logger": "src.logger", "level": "info", "timestamp": "2025-07-18T07:49:31.706928Z"}
Could get FontBBox from font descriptor because None cannot be parsed as 4 floats
Cannot set gray stroke color because /'P4' is an invalid float value
Cannot set gray non-stroke color because /'P4' is an invalid float value
{"file_path": "data/1_market_overview\\\u56fa\u6001\u7535\u6c60\u4ea7\u4e1a\u94fe\u6df1\u5ea6\u62c6\u89e32\uff1a\u5168\u7f51\u6700\u5168\u72ec\u5bb6\u00a0\u56fa\u6001\u7535\u6c60\u4ea7\u4e1a\u94fe\u6df1\u5ea6\u62c6\u89e3\uff1a\u5168\u7f51\u6700\u5168\u72ec\u5bb6#\u56fa\u6001\u7535\u6c60\u4ea7\u4e1a\u94fe\u5173\u6ce8\u5ea6\u9661\u589e#\u00a0#A\u80a1\u8fce\u6765\u65b0\u4e00\u8f6e\u79d1\u6280\u80a1\u5927\u725b\u5e02\uff1f#\u00a0#\u4eca\u65e5\u8bdd\u9898#\u00a0\u672c\u6587\u4e3b...\u00a0-\u00a0\u96ea\u7403.pdf", "file_type": ".pdf", "success": true, "snippets_count": 59, "event": "\u6587\u4ef6\u5904\u7406", "logger": "src.logger", "level": "info", "timestamp": "2025-07-18T07:49:34.427906Z"}
Could get FontBBox from font descriptor because None cannot be parsed as 4 floats
Cannot set gray stroke color because /'P4' is an invalid float value
Cannot set gray non-stroke color because /'P4' is an invalid float value
{"file_path": "data/1_market_overview\\\u56fa\u6001\u7535\u6c60\u4ea7\u4e1a\u94fe\u6df1\u5ea6\u62c6\u89e3\uff1a\u5168\u7f51\u6700\u5168\u72ec\u5bb6\u00a0\u56fa\u6001\u7535\u6c60\u4ea7\u4e1a\u94fe\u6df1\u5ea6\u62c6\u89e3\uff1a\u5168\u7f51\u6700\u5168\u72ec\u5bb6#\u4eca\u65e5\u8bdd\u9898#\u00a0#\u56fa\u6001\u7535\u6c60#\u00a0#\u4e70\u4e70\u4e70\uff01\u5317\u5411\u8d44\u91d11\u6708\u51c0\u4e70\u5165\u5df2\u8d85\u53bb\u5e74\u5168\u5e74#\u00a0\u8fd1\u671f\u63a8\u8350\u6587\u7ae0...\u00a0-\u00a0\u96ea\u7403.pdf", "file_type": ".pdf", "success": true, "snippets_count": 53, "event": "\u6587\u4ef6\u5904\u7406", "logger": "src.logger", "level": "info", "timestamp": "2025-07-18T07:49:36.348370Z"}
Multiple definitions in dictionary at byte 0x344a61 for key /MediaBox
{"file_path": "data/1_market_overview\\\u56fa\u6001\u7535\u6c60\u5173\u952e\u5236\u9020\u5de5\u827a\u7efc\u8ff0_\u8d75\u5b87\u9f99.pdf", "file_type": ".pdf", "success": true, "snippets_count": 25, "event": "\u6587\u4ef6\u5904\u7406", "logger": "src.logger", "level": "info", "timestamp": "2025-07-18T07:49:41.522487Z"}
{"file_path": "data/1_market_overview\\\u56fa\u6001\u7535\u6c60\u6df1\u5ea6\u62a5\u544a\u7fa4\u96c4\u9010\u9e7f.pdf", "file_type": ".pdf", "success": true, "snippets_count": 190, "event": "\u6587\u4ef6\u5904\u7406", "logger": "src.logger", "level": "info", "timestamp": "2025-07-18T07:49:53.047981Z"}
{"file_path": "data/1_market_overview\\\u56fa\u6001\u7535\u6c60\u751f\u4ea7\u5de5\u827a\u89e3\u6790-\u9762\u5305\u677f\u793e\u533a.pdf", "file_type": ".pdf", "success": true, "snippets_count": 25, "event": "\u6587\u4ef6\u5904\u7406", "logger": "src.logger", "level": "info", "timestamp": "2025-07-18T07:49:55.237011Z"}
Multiple definitions in dictionary at byte 0x1fe226 for key /MediaBox
{"file_path": "data/1_market_overview\\\u56fa\u6001\u7535\u6c60\u7814\u7a76\u53ca\u53d1\u5c55\u73b0\u72b6_\u6d2a\u6708\u743c.pdf", "file_type": ".pdf", "success": true, "snippets_count": 15, "event": "\u6587\u4ef6\u5904\u7406", "logger": "src.logger", "level": "info", "timestamp": "2025-07-18T07:50:02.162780Z"}
{"file_path": "data/1_market_overview\\\u56fa\u6001\u7535\u6c60\u884c\u4e1a\u6df1\u5ea6\u4ea7\u4e1a\u5316\u6309\u4e0b\u52a0\u901f\u952e\u6280\u672f\u7a81\u7834.pdf", "file_type": ".pdf", "success": true, "snippets_count": 83, "event": "\u6587\u4ef6\u5904\u7406", "logger": "src.logger", "level": "info", "timestamp": "2025-07-18T07:50:04.976188Z"}
invalid pdf header: b'HTTP/'
incorrect startxref pointer(1)
parsing for Object Streams
{"file_path": "data/1_market_overview\\\u56fa\u6001\u7535\u6c60\u884c\u4e1a\u6df1\u5ea6\uff1a\u5343\u547c\u4e07\u5524\u59cb\u51fa\u6765,\u72b9\u62b1\u7435\u7436\u534a\u906e\u9762.pdf", "file_type": ".pdf", "success": true, "snippets_count": 184, "event": "\u6587\u4ef6\u5904\u7406", "logger": "src.logger", "level": "info", "timestamp": "2025-07-18T07:50:07.942347Z"}
{"file_path": "data/1_market_overview\\\u56fa\u6001\u7535\u6c60\u884c\u4e1a\u6df1\u5ea6\uff1a\u73b0\u72b6\u53ca\u8d8b\u52bf\u3001\u56fd\u5185\u5916\u653f\u7b56\u3001\u4ea7\u4e1a\u94fe\u53ca\u76f8\u5173\u516c\u53f8\u6df1\u5ea6\u68b3\u7406\u3010\u6167\u535a\u51fa\u54c1\u3011 - \u77e5\u4e4e.pdf", "file_type": ".pdf", "success": true, "snippets_count": 33, "event": "\u6587\u4ef6\u5904\u7406", "logger": "src.logger", "level": "info", "timestamp": "2025-07-18T07:50:11.258488Z"}
{"file_path": "data/1_market_overview\\\u56fa\u6001\u7535\u6c60\uff1a\u52a8\u529b\u7535\u6c60\u884c\u4e1a\u53d8\u9769\u7684\u65b0\u5f15\u64ce\u65b0\u8d5b\u9053_\u7ecf\u6d4e\u5b66\u4eba - \u524d\u77bb\u7f51.pdf", "file_type": ".pdf", "success": true, "snippets_count": 102, "event": "\u6587\u4ef6\u5904\u7406", "logger": "src.logger", "level": "info", "timestamp": "2025-07-18T07:50:14.938123Z"}
{"file_path": "data/1_market_overview\\\u56fa\u6001\u7535\u89e3\u8d28\u4e2d\u9502\u79bb\u5b50\u4f20\u8f93\u673a\u7406\u7814\u7a76\u8fdb\u5c55.pdf", "file_type": ".pdf", "success": true, "snippets_count": 7, "event": "\u6587\u4ef6\u5904\u7406", "logger": "src.logger", "level": "info", "timestamp": "2025-07-18T07:50:17.123866Z"}
Multiple definitions in dictionary at byte 0xa6dee4 for key /MediaBox
{"file_path": "data/1_market_overview\\\u56fa\u6001\u7535\u89e3\u8d28\u819c\u7684\u5236\u5907\u53ca\u5176\u5728\u56fa\u6001\u7535\u6c60\u4e2d\u7684\u5e94\u7528_\u8bb8\u6d01\u8339.pdf", "file_type": ".pdf", "success": true, "snippets_count": 44, "event": "\u6587\u4ef6\u5904\u7406", "logger": "src.logger", "level": "info", "timestamp": "2025-07-18T07:50:22.443612Z"}
Multiple definitions in dictionary at byte 0x28774 for key /Ascent
Multiple definitions in dictionary at byte 0x32314 for key /Ascent
Multiple definitions in dictionary at byte 0x3a3bd for key /Ascent
Multiple definitions in dictionary at byte 0x475f2 for key /Ascent
Multiple definitions in dictionary at byte 0x4e49c for key /Ascent
Multiple definitions in dictionary at byte 0x582c4 for key /Ascent
Multiple definitions in dictionary at byte 0x5fb8c for key /Ascent
Multiple definitions in dictionary at byte 0x666d0 for key /Ascent
Multiple definitions in dictionary at byte 0x6ea28 for key /Ascent
Multiple definitions in dictionary at byte 0x75232 for key /Ascent
Multiple definitions in dictionary at byte 0x7ad07 for key /Ascent
Multiple definitions in dictionary at byte 0xa0d2d for key /Ascent
{"file_path": "data/1_market_overview\\\u56fa\u6001\u7535\u89e3\u8d28\u9502\u79bb\u5b50\u8f93\u8fd0\u673a\u5236\u7814\u7a76\u8fdb\u5c55.pdf", "file_type": ".pdf", "success": true, "snippets_count": 21, "event": "\u6587\u4ef6\u5904\u7406", "logger": "src.logger", "level": "info", "timestamp": "2025-07-18T07:50:28.202449Z"}
invalid pdf header: b'HTTP/'
incorrect startxref pointer(3)
parsing for Object Streams
{"file_path": "data/1_market_overview\\\u56fa\u6001\u9502\u7535\uff0c\u5171\u540c\u671f\u5f85.pdf", "file_type": ".pdf", "success": true, "snippets_count": 228, "event": "\u6587\u4ef6\u5904\u7406", "logger": "src.logger", "level": "info", "timestamp": "2025-07-18T07:50:33.836884Z"}
{"file_path": "data/1_market_overview\\\u56fa\u6001\u9502\u91d1\u5c5e\u7535\u6c60\u4e2d\u7684\u5916\u538b-\u7535\u5316\u5b66\u8026\u5408.pdf", "file_type": ".pdf", "success": true, "snippets_count": 15, "event": "\u6587\u4ef6\u5904\u7406", "logger": "src.logger", "level": "info", "timestamp": "2025-07-18T07:50:36.034389Z"}
{"file_path": "data/1_market_overview\\\u56fa\u6001\u9502\u91d1\u5c5e\u7535\u6c60\u4e2d\u805a\u6c27\u5316\u4e59\u70ef\u57fa\u805a\u5408\u7269\u7535\u89e3\u8d28\u7684\u6539\u6027\u7814\u7a76_\u5434\u5c0f\u96ea.pdf", "file_type": ".pdf", "success": true, "snippets_count": 102, "event": "\u6587\u4ef6\u5904\u7406", "logger": "src.logger", "level": "info", "timestamp": "2025-07-18T07:50:42.521176Z"}
{"file_path": "data/1_market_overview\\\u57fa\u4e8e\u7b2c\u56db\u526f\u65cf\u91d1\u5c5e\u5364\u5316\u7269\u7684\u5168\u56fa\u6001\u9502\u7535\u6c60_\u738b\u51ef.pdf", "file_type": ".pdf", "success": true, "snippets_count": 251, "event": "\u6587\u4ef6\u5904\u7406", "logger": "src.logger", "level": "info", "timestamp": "2025-07-18T07:51:17.102890Z"}
{"file_path": "data/1_market_overview\\\u5b89\u4fe1\u8bc1\u5238-\u7535\u529b\u8bbe\u5907\u884c\u4e1a\u6df1\u5ea6\u5206\u6790\uff1a\u56fa\u6001\u9502\u7535\u4ea7\u4e1a\u5316\u6df1\u5ea6\u89e3\u6790-210111.pdf", "file_type": ".pdf", "success": true, "snippets_count": 36, "event": "\u6587\u4ef6\u5904\u7406", "logger": "src.logger", "level": "info", "timestamp": "2025-07-18T07:51:19.659754Z"}
{"file_path": "data/1_market_overview\\\u5e72\u6cd5\u7535\u6781\uff1a\u7814\u7a76\u56fa\u6001\u7535\u6c60\u5fc5\u987b\u638c\u63e1\u7684\u5173\u952e\u6280\u672f\uff1f - \u4e2d\u56fd\u7c89\u4f53\u7f51.pdf", "file_type": ".pdf", "success": true, "snippets_count": 40, "event": "\u6587\u4ef6\u5904\u7406", "logger": "src.logger", "level": "info", "timestamp": "2025-07-18T07:51:22.402114Z"}
Multiple definitions in dictionary at byte 0x15e842 for key /MediaBox
Cannot set gray non-stroke color because /'P0' is an invalid float value
{"file_path": "data/1_market_overview\\\u786b\u5316\u7269\u56fa\u6001\u7535\u89e3\u8d28\u5728\u5168\u56fa\u6001\u7535\u6c60\u4e2d\u7684\u5e94\u7528\u7814\u7a76\u8fdb\u5c55_\u79e6\u5fd7\u5149.pdf", "file_type": ".pdf", "success": true, "snippets_count": 9, "event": "\u6587\u4ef6\u5904\u7406", "logger": "src.logger", "level": "info", "timestamp": "2025-07-18T07:51:24.207952Z"}
{"file_path": "data/1_market_overview\\\u786b\u5316\u7269\u56fa\u6001\u8d1f\u6781Li - Li metal anode interface in sulfide\u2010based all\u2010solid\u2010state Li batteries.pdf", "file_type": ".pdf", "success": true, "snippets_count": 55, "event": "\u6587\u4ef6\u5904\u7406", "logger": "src.logger", "level": "info", "timestamp": "2025-07-18T07:51:26.627600Z"}
{"file_path": "data/1_market_overview\\\u805a\u73af\u6c27\u4e59\u70f7\u57fa\u56fa\u4f53\u7535\u89e3\u8d28\u4e0e\u56fa\u6001\u7535\u6c60\u7684\u7814\u7a76_\u90b1\u7eaa\u4eae.pdf", "file_type": ".pdf", "success": true, "snippets_count": 64, "event": "\u6587\u4ef6\u5904\u7406", "logger": "src.logger", "level": "info", "timestamp": "2025-07-18T07:51:35.219335Z"}
{"file_path": "data/1_market_overview\\\u8d85\u5168\u9762 _ \u56fa\u6001\u7535\u6c60\u7684\u539f\u7406\u3001\u53d1\u5c55\u5386\u7a0b\u4ee5\u53ca\u4ea7\u4e1a\u73b0\u72b6_\u9502\u7535\u65b0\u80fd\u6e90_\u6a21\u5207\u793e\u533a_\u6a21\u5207\u6613\u5f97\u901a.pdf", "file_type": ".pdf", "success": true, "snippets_count": 113, "event": "\u6587\u4ef6\u5904\u7406", "logger": "src.logger", "level": "info", "timestamp": "2025-07-18T07:51:37.666817Z"}
{"file_path": "data/1_market_overview\\\u8f66\u8f7d\u5168\u56fa\u6001\u7535\u6c60\u63a2\u8ba8\uff1a\u6838\u5fc3\u5de5\u827a\u3001\u4e3b\u673a\u5382\u7684\u5e03\u5c40-\u534e\u590fEV\u7f51.pdf", "file_type": ".pdf", "success": true, "snippets_count": 34, "event": "\u6587\u4ef6\u5904\u7406", "logger": "src.logger", "level": "info", "timestamp": "2025-07-18T07:51:39.920973Z"}
Multiple definitions in dictionary at byte 0x3278bb for key /MediaBox
{"file_path": "data/1_market_overview\\\u9502\u786b\u94f6\u9517\u77ff\u56fa\u6001\u7535\u89e3\u8d28\u7814\u7a76\u8fdb\u5c55_\u5f6d\u6797\u5cf0.pdf", "file_type": ".pdf", "success": true, "snippets_count": 25, "event": "\u6587\u4ef6\u5904\u7406", "logger": "src.logger", "level": "info", "timestamp": "2025-07-18T07:51:49.696717Z"}
Multiple definitions in dictionary at byte 0x2d3284 for key /MediaBox
{"file_path": "data/1_market_overview\\\u9502\u79bb\u5b50\u7535\u6c60\u6c27\u5316\u7269\u56fa\u6001\u7535\u89e3\u8d28\u7814\u7a76\u8fdb\u5c55_\u59da\u5fe0\u5189.pdf", "file_type": ".pdf", "success": true, "snippets_count": 703, "event": "\u6587\u4ef6\u5904\u7406", "logger": "src.logger", "level": "info", "timestamp": "2025-07-18T07:51:53.424139Z"}
Multiple definitions in dictionary at byte 0x143f14 for key /MediaBox
{"file_path": "data/1_market_overview\\\u9502\u79bb\u5b50\u7535\u6c60\u7528\u65e0\u673a\u56fa\u6001\u7535\u89e3\u8d28\u7814\u7a76\u8fdb\u5c55_\u5f20\u7389\u5764.pdf", "file_type": ".pdf", "success": true, "snippets_count": 4, "event": "\u6587\u4ef6\u5904\u7406", "logger": "src.logger", "level": "info", "timestamp": "2025-07-18T07:51:55.434034Z"}
Multiple definitions in dictionary at byte 0x3265e5 for key /MediaBox
{"file_path": "data/1_market_overview\\\u9502\u79bb\u5b50\u7535\u6c60\u7528\u805a\u5408\u7269_\u65e0\u673a\u590d\u5408\u56fa\u6001\u7535\u89e3\u8d28\u7814\u7a76\u8fdb\u5c55_\u4e25\u660e\u4fdd.pdf", "file_type": ".pdf", "success": true, "snippets_count": 13, "event": "\u6587\u4ef6\u5904\u7406", "logger": "src.logger", "level": "info", "timestamp": "2025-07-18T07:51:59.923530Z"}
Multiple definitions in dictionary at byte 0x870b3e for key /MediaBox
{"file_path": "data/1_market_overview\\\u9502\u9567\u949b\u6c27\u56fa\u4f53\u7535\u89e3\u8d28\u7684\u5236\u5907\u53ca\u63ba\u6742\u6539\u6027\u7814\u7a76_\u5468\u70ab\u5149.pdf", "file_type": ".pdf", "success": true, "snippets_count": 62, "event": "\u6587\u4ef6\u5904\u7406", "logger": "src.logger", "level": "info", "timestamp": "2025-07-18T07:52:02.587324Z"}
Multiple definitions in dictionary at byte 0x8b910e for key /MediaBox
{"file_path": "data/1_market_overview\\\u9ad8\u80fd\u91cf\u5bc6\u5ea6\u5168\u7535\u5316\u5b66\u6d3b\u6027\u786b\u57fa\u5168\u56fa\u6001\u9502\u7535\u6c60\u7814\u7a76_\u674e\u7f8e\u83b9.pdf", "file_type": ".pdf", "success": true, "snippets_count": 164, "event": "\u6587\u4ef6\u5904\u7406", "logger": "src.logger", "level": "info", "timestamp": "2025-07-18T07:52:07.456545Z"}
{"node": "build_context", "duration_seconds": 0, "snippets_count": 7921, "event": "\u8282\u70b9\u6267\u884c\u5b8c\u6210", "logger": "src.logger", "level": "info", "timestamp": "2025-07-18T07:52:07.458118Z"}
{"model": "gemini-2.5-flash", "operation": "executor_text", "success": false, "duration_seconds": 604.9572143554688, "input_tokens": 0, "output_tokens": 0, "total_tokens": 0, "estimated_cost_usd": 0.0, "error": "504 Deadline Exceeded", "event": "API\u8c03\u7528\u5931\u8d25", "logger": "src.logger", "level": "error", "timestamp": "2025-07-18T08:02:12.452686Z"}
{"model": "gemini-2.5-flash", "operation": "executor_text", "success": false, "duration_seconds": 236.3775773048401, "input_tokens": 0, "output_tokens": 0, "total_tokens": 0, "estimated_cost_usd": 0.0, "error": "429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {\n  quota_metric: \"generativelanguage.googleapis.com/generate_content_free_tier_input_token_count\"\n  quota_id: \"GenerateContentInputTokensPerModelPerMinute-FreeTier\"\n  quota_dimensions {\n    key: \"model\"\n    value: \"gemini-2.5-flash\"\n  }\n  quota_dimensions {\n    key: \"location\"\n    value: \"global\"\n  }\n  quota_value: 250000\n}\n, links {\n  description: \"Learn more about Gemini API quotas\"\n  url: \"https://ai.google.dev/gemini-api/docs/rate-limits\"\n}\n, retry_delay {\n  seconds: 19\n}\n]", "event": "API\u8c03\u7528\u5931\u8d25", "logger": "src.logger", "level": "error", "timestamp": "2025-07-18T08:06:10.833276Z"}
{"model": "gemini-2.5-flash", "operation": "executor_text", "success": false, "duration_seconds": 509.9893455505371, "input_tokens": 0, "output_tokens": 0, "total_tokens": 0, "estimated_cost_usd": 0.0, "error": "429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {\n  quota_metric: \"generativelanguage.googleapis.com/generate_content_free_tier_input_token_count\"\n  quota_id: \"GenerateContentInputTokensPerModelPerMinute-FreeTier\"\n  quota_dimensions {\n    key: \"model\"\n    value: \"gemini-2.5-flash\"\n  }\n  quota_dimensions {\n    key: \"location\"\n    value: \"global\"\n  }\n  quota_value: 250000\n}\n, links {\n  description: \"Learn more about Gemini API quotas\"\n  url: \"https://ai.google.dev/gemini-api/docs/rate-limits\"\n}\n, retry_delay {\n  seconds: 48\n}\n]", "event": "API\u8c03\u7528\u5931\u8d25", "logger": "src.logger", "level": "error", "timestamp": "2025-07-18T08:14:44.824434Z"}
{"node": "generate_content_top_down", "error": "RetryError[<Future at 0x20f59d23b90 state=finished raised ResourceExhausted>]", "event": "\u8282\u70b9\u6267\u884c\u9519\u8bef", "logger": "src.logger", "level": "error", "timestamp": "2025-07-18T08:14:44.825445Z"}
{"checkpoint_path": "Error checkpoint saved: checkpoints\\error_checkpoint_20250718_161444_generate_content_top_down.json", "event": "\u72b6\u6001\u68c0\u67e5\u70b9\u5df2\u4fdd\u5b58", "logger": "src.logger", "level": "info", "timestamp": "2025-07-18T08:14:44.833188Z"}
{"event": "Checkpoint saved due to error in generate_content_top_down: checkpoints\\error_checkpoint_20250718_161444_generate_content_top_down.json", "logger": "src.logger", "level": "info", "timestamp": "2025-07-18T08:14:44.833188Z"}
