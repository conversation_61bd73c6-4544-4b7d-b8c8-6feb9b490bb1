# AI报告生成器 - 扩展文件格式支持

## 概述

AI报告生成器现在支持读取多种文件格式，从原来仅支持 `.md` 和 `.txt` 文件，扩展到支持几十种常见的文件格式。这大大增强了数据源的灵活性和实用性。

## 支持的文件格式

### 📄 文本文件
- `.txt` - 纯文本文件
- `.md` - Markdown文档
- `.rst` - reStructuredText文档
- `.log` - 日志文件
- `.rtf` - 富文本格式

### 📊 Office文档
- `.docx` - Word文档（新版）
- `.doc` - Word文档（旧版）
- `.xlsx` - Excel表格（新版）
- `.xls` - Excel表格（旧版）
- `.csv` - 逗号分隔值文件
- `.tsv` - 制表符分隔值文件
- `.pptx` - PowerPoint演示文稿（新版）
- `.ppt` - PowerPoint演示文稿（旧版）

### 📑 PDF文件
- `.pdf` - PDF文档（支持文本提取和OCR）

### 🔧 数据文件
- `.json` - JSON数据文件
- `.xml` - XML数据文件
- `.yaml` / `.yml` - YAML配置文件

### 💻 代码文件
- `.py` - Python代码
- `.js` - JavaScript代码
- `.html` - HTML网页
- `.css` - CSS样式表
- `.sql` - SQL脚本
- `.java` - Java代码
- `.cpp` / `.c` / `.h` - C/C++代码
- `.php` - PHP代码
- `.rb` - Ruby代码
- `.go` - Go代码
- `.sh` / `.bat` - 脚本文件

### ⚙️ 配置文件
- `.ini` - INI配置文件
- `.cfg` / `.conf` - 配置文件
- `.properties` - Java属性文件
- `.toml` - TOML配置文件

### 📚 其他格式
- `.odt` - OpenDocument文本
- `.ods` - OpenDocument电子表格
- `.epub` - EPUB电子书

## 安装依赖

### 快速安装
运行自动安装脚本：
```bash
python install_extended_dependencies.py
```

### 手动安装
如果自动安装失败，可以手动安装所需的库：

#### 基础库
```bash
pip install PyPDF2 pdfplumber pandas openpyxl xlrd python-docx python-pptx
```

#### 扩展格式支持
```bash
pip install striprtf docx2txt PyYAML odfpy ebooklib beautifulsoup4 lxml
```

#### OCR支持（可选）
```bash
pip install pytesseract Pillow pdf2image PyMuPDF
```

**注意**: OCR功能还需要安装 Tesseract OCR 引擎：
1. 访问: https://github.com/UB-Mannheim/tesseract/wiki
2. 下载Windows版本的Tesseract
3. 安装到默认路径: `C:\Program Files\Tesseract-OCR`

## 使用方法

### 基本用法
```python
from complete_report_generator import CompleteReportGenerator

# 初始化生成器
generator = CompleteReportGenerator()

# 读取数据源（自动识别所有支持的文件格式）
content = generator.read_data_source("your_data_directory")

# 生成完整报告
generator.generate_complete_report(
    topic="您的报告主题",
    data_source="your_data_directory"
)
```

### 读取单个文件
```python
from pathlib import Path

# 读取单个文件
file_path = Path("example.json")
content = generator._read_single_framework_file(file_path)
print(content)
```

### 框架文件支持
框架文件现在也支持多种格式：
```python
# 读取框架文件（支持多种格式）
framework_content = generator.read_framework_file("framework_directory")
```

## 功能特点

### 🔍 自动格式识别
- 根据文件扩展名自动选择合适的读取方法
- 无需手动指定文件类型

### 🌐 编码兼容
- 支持UTF-8、GBK、GB2312等多种编码
- 自动尝试不同编码以确保正确读取

### 🛡️ 错误处理
- 单个文件读取失败不影响整体处理
- 提供详细的错误信息和建议

### 📋 内容整合
- 多个文件内容自动合并
- 每个文件内容都有清晰的标记
- 保持原始文件的结构信息

### 🔧 扩展性强
- 易于添加新的文件格式支持
- 模块化的读取方法设计

## 测试和验证

### 运行测试
```bash
# 测试扩展文件格式支持
python test_extended_formats.py

# 运行使用示例
python example_extended_formats.py
```

### 测试内容
- 创建各种格式的测试文件
- 验证读取功能是否正常
- 检查内容提取的准确性
- 测试目录批量读取功能

## 性能优化

### 大文件处理
- PDF文件支持分页处理
- 限制单个文件的最大处理页数
- 自动跳过损坏或无法读取的文件

### 内存管理
- 流式读取大文件
- 及时释放不需要的资源
- 避免同时加载过多文件到内存

## 故障排除

### 常见问题

1. **依赖库缺失**
   ```
   ImportError: No module named 'xxx'
   ```
   解决方案：运行 `python install_extended_dependencies.py`

2. **编码问题**
   ```
   UnicodeDecodeError: 'utf-8' codec can't decode
   ```
   解决方案：系统会自动尝试多种编码，通常能自动解决

3. **PDF读取失败**
   ```
   读取PDF文件失败
   ```
   解决方案：
   - 检查PDF是否加密
   - 安装OCR相关库
   - 确保Tesseract正确安装

4. **Office文档读取失败**
   ```
   需要安装python-docx库
   ```
   解决方案：`pip install python-docx python-pptx openpyxl`

### 获取帮助
如果遇到问题：
1. 检查错误信息中的建议
2. 确认所需的依赖库已安装
3. 运行测试脚本验证功能
4. 查看具体的错误日志

## 更新日志

### v2.0 - 扩展文件格式支持
- ✅ 新增支持20+种文件格式
- ✅ 自动格式识别和读取
- ✅ 改进的错误处理机制
- ✅ 完整的测试和示例代码
- ✅ 自动依赖安装脚本

### v1.0 - 基础版本
- ✅ 支持 `.md` 和 `.txt` 文件
- ✅ 基本的报告生成功能

## 贡献

欢迎贡献新的文件格式支持！添加新格式的步骤：

1. 在 `read_data_source` 方法中添加新的文件模式
2. 实现对应的 `_read_xxx_file` 方法
3. 更新 `_read_single_framework_file` 方法
4. 添加相应的测试用例
5. 更新文档说明

## 许可证

本项目遵循原有的许可证条款。
