#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
搜索触发器模块
分析内容缺口，确定搜索需求
"""

from typing import List, Dict, Any


class SearchTrigger:
    """搜索需求识别系统"""

    def __init__(self, generator):
        self.generator = generator

    def analyze_content_gaps(self, generated_content: str, topic: str) -> List[Dict[str, Any]]:
        """分析内容缺口，确定搜索需求"""
        gaps = []

        # 时效性检查
        if self.needs_latest_data(generated_content, topic):
            gaps.append({
                'type': 'latest_data',
                'query': f'{topic} 最新数据 2024 2025',
                'priority': 'high',
                'reason': '内容可能缺乏最新的市场数据和发展动态'
            })

        # 市场数据检查
        if self.lacks_market_data(generated_content, topic):
            gaps.append({
                'type': 'market_data',
                'query': f'{topic} 市场规模 竞争格局 行业分析',
                'priority': 'medium',
                'reason': '缺少详细的市场分析和竞争格局信息'
            })

        # 技术发展检查
        if self.needs_tech_updates(generated_content, topic):
            gaps.append({
                'type': 'technology',
                'query': f'{topic} 技术发展 创新 突破',
                'priority': 'medium',
                'reason': '需要补充最新的技术发展和创新信息'
            })

        # 政策法规检查
        if self.needs_policy_info(generated_content, topic):
            gaps.append({
                'type': 'policy',
                'query': f'{topic} 政策 法规 标准 监管',
                'priority': 'medium',
                'reason': '缺少相关政策法规和行业标准信息'
            })

        # 案例研究检查
        if self.needs_case_studies(generated_content, topic):
            gaps.append({
                'type': 'cases',
                'query': f'{topic} 案例 项目 应用 实践',
                'priority': 'low',
                'reason': '需要补充实际案例和应用实践'
            })

        return gaps

    def needs_latest_data(self, content: str, topic: str) -> bool:
        """检查是否需要最新数据"""
        # 检查内容中是否缺少2024年的数据
        current_year_mentions = content.count('2024') + content.count('2025')
        data_keywords = ['数据', '统计', '报告', '调研', '市场规模']
        data_mentions = sum(content.count(keyword) for keyword in data_keywords)

        # 如果数据关键词多但缺少最新年份，则需要最新数据
        return data_mentions > 5 and current_year_mentions < 3

    def lacks_market_data(self, content: str, topic: str) -> bool:
        """检查是否缺少市场数据"""
        market_keywords = ['市场', '竞争', '份额', '规模', '增长率', '预测']
        market_score = sum(content.count(keyword) for keyword in market_keywords)

        # 如果市场关键词提及较少，可能需要补充
        return market_score < 10

    def needs_tech_updates(self, content: str, topic: str) -> bool:
        """检查是否需要技术更新"""
        tech_keywords = ['技术', '创新', '突破', '发展', '趋势', '前沿']
        tech_score = sum(content.count(keyword) for keyword in tech_keywords)

        # 技术类主题通常需要最新的技术信息
        tech_topics = ['AI', '人工智能', '区块链', '物联网', '5G', '云计算', '大数据']
        is_tech_topic = any(keyword in topic for keyword in tech_topics)

        return is_tech_topic and tech_score < 15

    def needs_policy_info(self, content: str, topic: str) -> bool:
        """检查是否需要政策信息"""
        policy_keywords = ['政策', '法规', '标准', '监管', '规范', '指导']
        policy_score = sum(content.count(keyword) for keyword in policy_keywords)

        # 如果政策关键词较少，可能需要补充
        return policy_score < 5

    def needs_case_studies(self, content: str, topic: str) -> bool:
        """检查是否需要案例研究"""
        case_keywords = ['案例', '项目', '应用', '实践', '示例', '成功']
        case_score = sum(content.count(keyword) for keyword in case_keywords)

        # 如果案例关键词较少，可能需要补充
        return case_score < 8
