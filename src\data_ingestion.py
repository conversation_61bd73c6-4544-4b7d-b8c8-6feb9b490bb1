"""
数据摄入与预处理模块
解析支持的文件格式，提取文本、图像和表格。
"""
import os
import base64
from typing import List, Dict, Any, Optional
from pathlib import Path
import fitz  # PyMuPDF
import camelot
from PIL import Image
import pandas as pd
from docx import Document
from pptx import Presentation
import markdown
import json

from .config import Config
from .logger import ProcessLogger


class ContentSnippet:
    """内容片段类，封装从源文档中提取的结构化信息单元"""
    
    def __init__(self, content: Any, content_type: str, source_file: str, source_path: str, **kwargs):
        self.content = content
        self.content_type = content_type  # 'text', 'image', 'table'
        self.metadata = {
            'source_file': Path(source_file).name,
            'source_path': source_path,
            'content_type': content_type,
            **kwargs
        }
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'content': self.content,
            'metadata': self.metadata
        }


class DataIngestion:
    """
    数据摄入与预处理类
    """

    def __init__(self, config: Config, logger: ProcessLogger):
        self.config = config
        self.logger = logger

    def process_pdf(self, file_path: str) -> Dict[str, Any]:
        """处理PDF文件，提取文本、图像和表格"""
        try:
            # 尝试打开PDF文件
            doc = fitz.open(file_path)
            
            # 检查是否加密
            if doc.is_encrypted:
                # 尝试使用空密码打开
                if not doc.authenticate(""):
                    self.logger.log_file_processing(
                        file_path=file_path,
                        file_type='pdf',
                        success=False,
                        error="PDF文件已加密且无法使用空密码打开"
                    )
                    doc.close()
                    return {}
            
            content = {
                'text': self.extract_text(doc),
                'images': self.extract_images(doc, file_path),
                'tables': self.extract_tables(file_path)
            }
            doc.close()
            return content
            
        except Exception as e:
            self.logger.log_file_processing(
                file_path=file_path,
                file_type='pdf',
                success=False,
                error=str(e)
            )
            return {}

    def extract_text(self, doc: fitz.Document) -> str:
        """提取PDF文本内容"""
        texts = []
        for page in doc:
            texts.append(page.get_text())
        return "\n".join(texts)

    def extract_images(self, doc: fitz.Document, file_path: str) -> List[Dict[str, Any]]:
        """提取PDF图像，并返回Base64编码的图像数据"""
        images = []
        
        if not self.config.document_processing.pdf.extract_images:
            return images
            
        for page_number in range(len(doc)):
            for img_index, img in enumerate(doc.get_page_images(page_number)):
                try:
                    xref = img[0]
                    base_image = doc.extract_image(xref)
                    image_data = base_image['image']
                    image_ext = base_image['ext']
                    
                    # 转换为Base64
                    image_base64 = base64.b64encode(image_data).decode('utf-8')
                    
                    images.append({
                        'data': image_base64,
                        'format': image_ext,
                        'page': page_number,
                        'index': img_index
                    })
                except Exception as e:
                    self.logger.log_file_processing(
                        file_path=file_path,
                        file_type='pdf_image',
                        success=False,
                        error=str(e)
                    )
        return images

    def extract_tables(self, file_path: str) -> List[str]:
        """提取PDF中的表格并转换为Markdown"""
        if not self.config.document_processing.pdf.extract_tables:
            return []
            
        try:
            tables = camelot.read_pdf(
                file_path, 
                flavor=self.config.document_processing.pdf.table_extraction_method
            )
            return [table.df.to_markdown(index=False) for table in tables]
        except Exception as e:
            self.logger.log_file_processing(
                file_path=file_path,
                file_type='pdf_table',
                success=False,
                error=str(e)
            )
            return []

    def process_docx(self, file_path: str) -> Dict[str, Any]:
        """处理Word文件，提取文本内容"""
        doc = Document(file_path)
        text = "\n".join(paragraph.text for paragraph in doc.paragraphs)
        return {'text': text}

    def process_txt(self, file_path: str) -> Dict[str, Any]:
        """处理文本文件，读取所有内容"""
        with open(file_path, "r", encoding="utf-8") as f:
            text = f.read()
        return {'text': text}

    def process_md(self, file_path: str) -> Dict[str, Any]:
        """处理Markdown文件，提取文本内容"""
        with open(file_path, "r", encoding="utf-8") as f:
            md_content = f.read()
        # 保持原始Markdown格式
        return {'text': md_content}

    def process_xlsx(self, file_path: str) -> Dict[str, Any]:
        """处理Excel文件，提取所有工作表并转换为Markdown"""
        xls = pd.ExcelFile(file_path)
        sheets = {sheet_name: xls.parse(sheet_name).to_markdown(index=False) for sheet_name in xls.sheet_names}
        return {'tables': sheets}

    def process_pptx(self, file_path: str) -> Dict[str, Any]:
        """处理PowerPoint文件，提取幻灯片文本"""
        presentation = Presentation(file_path)
        slides_text = []
        for slide in presentation.slides:
            slide_text = "\n".join(shape.text for shape in slide.shapes if hasattr(shape, "text"))
            slides_text.append(slide_text)
        return {'text': "\n".join(slides_text)}

    def process_document(self, file_path: str) -> Dict[str, Any]:
        """根据文件类型选择合适的处理方法"""
        ext = os.path.splitext(file_path)[1].lower()
        
        try:
            if ext == '.pdf':
                return self.process_pdf(file_path)
            elif ext == '.docx':
                return self.process_docx(file_path)
            elif ext == '.txt':
                return self.process_txt(file_path)
            elif ext == '.md':
                return self.process_md(file_path)
            elif ext == '.xlsx':
                return self.process_xlsx(file_path)
            elif ext == '.pptx':
                return self.process_pptx(file_path)
            else:
                self.logger.log_file_processing(
                    file_path=file_path,
                    file_type=ext,
                    success=False,
                    error=f"不支持的文件格式: {ext}"
                )
                return {}
        except Exception as e:
            self.logger.log_file_processing(
                file_path=file_path,
                file_type=ext,
                success=False,
                error=str(e)
            )
            return {}

    def create_content_snippets(self, file_path: str, content: Dict[str, Any], source_dir: str) -> List[ContentSnippet]:
        """将文件内容转换为内容片段列表"""
        snippets = []
        
        # 处理文本内容
        if 'text' in content and content['text']:
            snippet = ContentSnippet(
                content=content['text'],
                content_type='text',
                source_file=file_path,
                source_path=source_dir
            )
            snippets.append(snippet)
        
        # 处理图像内容
        if 'images' in content:
            for image in content['images']:
                snippet = ContentSnippet(
                    content=image,
                    content_type='image',
                    source_file=file_path,
                    source_path=source_dir,
                    page=image.get('page'),
                    index=image.get('index')
                )
                snippets.append(snippet)
        
        # 处理表格内容
        if 'tables' in content:
            if isinstance(content['tables'], list):
                # PDF表格
                for idx, table in enumerate(content['tables']):
                    snippet = ContentSnippet(
                        content=table,
                        content_type='table',
                        source_file=file_path,
                        source_path=source_dir,
                        table_index=idx
                    )
                    snippets.append(snippet)
            elif isinstance(content['tables'], dict):
                # Excel工作表
                for sheet_name, table_content in content['tables'].items():
                    snippet = ContentSnippet(
                        content=table_content,
                        content_type='table',
                        source_file=file_path,
                        source_path=source_dir,
                        sheet_name=sheet_name
                    )
                    snippets.append(snippet)
        
        return snippets

    def load_documents_as_snippets(self, source_dir: str) -> List[ContentSnippet]:
        """加载指定目录下的所有支持格式的文件，并返回内容片段列表"""
        all_snippets = []
        supported_formats = self.config.document_processing.supported_formats
        
        if not os.path.exists(source_dir):
            self.logger.log_file_processing(
                file_path=source_dir,
                file_type='directory',
                success=False,
                error=f"目录不存在: {source_dir}"
            )
            return all_snippets
        
        for root, _, files in os.walk(source_dir):
            for file in files:
                if any(file.endswith(suffix) for suffix in supported_formats):
                    file_path = os.path.join(root, file)
                    document_content = self.process_document(file_path)
                    
                    if document_content:
                        snippets = self.create_content_snippets(file_path, document_content, source_dir)
                        all_snippets.extend(snippets)
                        
                        self.logger.log_file_processing(
                            file_path=file_path,
                            file_type=os.path.splitext(file)[1],
                            success=True,
                            snippets_count=len(snippets)
                        )
        
        return all_snippets

