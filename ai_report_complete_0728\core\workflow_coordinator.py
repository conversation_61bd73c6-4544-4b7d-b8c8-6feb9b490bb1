#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
工作流协调器
协调串行和并行处理模块，按照设计工作流执行
"""

import time
import asyncio
from typing import List, Dict, Any, Optional
from pathlib import Path

from .serial_processor import SerialProcessor
from .parallel_processor import ParallelProcessor


class WorkflowCoordinator:
    """
    工作流协调器
    
    按照源代码中的设计工作流，协调串行和并行处理：
    
    阶段1（串行）：统筹模型完成框架工作
    - 读取框架文件
    - 生成完整标题结构
    - 制定任务指导
    
    阶段2（并行）：执行模型并行生成内容
    - 所有节点并行生成内容
    
    阶段3（混合）：迭代优化
    - 参考报告学习（并行）
    - 章节审核优化（并行）
    - 整体文档审核（串行）
    
    阶段4（串行）：最终文档生成
    - 字数控制优化
    - 生成最终文档
    """

    def __init__(self, generator):
        """
        初始化工作流协调器
        
        Args:
            generator: 主生成器实例
        """
        self.generator = generator
        self.serial_processor = SerialProcessor(generator)
        self.parallel_processor = ParallelProcessor(generator)
        self.report_config = generator.report_config

    async def execute_complete_workflow(
        self, 
        topic: str, 
        data_sources: List[str], 
        framework_file_path: Optional[str] = None
    ) -> str:
        """
        执行完整的工作流
        
        按照源代码设计的工作流，交替使用串行和并行处理
        """
        start_time = time.time()
        print(f"🚀 开始执行完整工作流: {topic}")
        print("📋 工作流设计:")
        print("   第一阶段：统筹模型串行完成框架工作")
        print("   第二阶段：执行模型并行生成内容")
        print("   第三阶段：混合模式迭代优化")
        print("   第四阶段：串行生成最终文档")

        try:
            # ==================== 阶段1：串行框架生成 ====================
            print("\n" + "🔄 开始阶段1：串行框架生成")
            
            # 步骤1.1：串行生成框架
            framework = await self.serial_processor.generate_framework_stage(topic, framework_file_path)
            sections = framework["sections"]
            
            # 步骤1.2：串行制定任务指导
            await self.serial_processor.generate_task_instructions_stage(sections, data_sources)

            # 保存阶段1 checkpoint
            self.generator.create_checkpoint("stage1_framework_completed", {
                "topic": topic,
                "framework": framework,
                "sections": sections,
                "data_sources": data_sources,
                "stage": 1
            })

            # ==================== 阶段2：并行内容生成 ====================
            print("\n" + "🔄 开始阶段2：并行内容生成")
            
            await self.parallel_processor.content_generation_stage(sections, data_sources)

            # 保存阶段2 checkpoint
            self.generator.create_checkpoint("stage2_content_completed", {
                "topic": topic,
                "framework": framework,
                "sections": sections,
                "data_sources": data_sources,
                "stage": 2
            })

            # ==================== 阶段3：混合模式迭代优化 ====================
            print("\n" + "🔄 开始阶段3：混合模式迭代优化")
            
            await self._execute_iterative_optimization_stage(sections, data_sources, topic)

            # 保存阶段3 checkpoint
            self.generator.create_checkpoint("stage3_optimization_completed", {
                "topic": topic,
                "framework": framework,
                "sections": sections,
                "data_sources": data_sources,
                "stage": 3
            })

            # ==================== 阶段4：串行最终文档生成 ====================
            print("\n" + "🔄 开始阶段4：串行最终文档生成")
            
            output_path = await self.serial_processor.final_document_generation_stage(topic, framework)

            # 保存最终 checkpoint
            self.generator.create_checkpoint("workflow_completed", {
                "topic": topic,
                "framework": framework,
                "sections": sections,
                "output_path": output_path,
                "stage": 4
            })

            # 清理旧checkpoint
            self.generator.cleanup_old_checkpoints(keep_count=5)

            total_time = time.time() - start_time
            print(f"\n🎉 完整工作流执行完成！总耗时: {total_time:.1f}秒")
            print(f"📄 输出文件: {output_path}")

            return output_path

        except Exception as e:
            print(f"\n❌ 工作流执行失败: {str(e)}")
            if hasattr(self.generator, 'current_checkpoint_id') and self.generator.current_checkpoint_id:
                print(f"   当前checkpoint: {self.generator.current_checkpoint_id}")
                print(f"   恢复命令: 使用resume_checkpoint='{self.generator.current_checkpoint_id}'")
            raise

    async def _execute_iterative_optimization_stage(self, sections: List[Dict[str, Any]], data_sources: List[str], topic: str):
        """
        执行迭代优化阶段（混合模式）
        
        每轮迭代包括：
        1. 参考报告学习（并行）
        2. 章节审核优化（并行）
        3. 整体文档审核（串行）
        """
        print("   📊 执行3轮迭代优化（混合串行/并行模式）")

        try:
            from tqdm import tqdm
            iteration_pbar = tqdm(total=3, desc="🔄 迭代优化", unit="轮", leave=False)
        except ImportError:
            iteration_pbar = None

        for iteration in range(1, 4):
            print(f"\n   🔄 第{iteration}轮迭代优化开始")

            # 子阶段3.1：参考报告学习（并行）
            if iteration == 1 and self.report_config.get("reference_report"):
                print(f"   📚 第{iteration}轮：参考报告学习（并行）")
                await self.parallel_processor.reference_learning_stage(sections, topic)

            # 子阶段3.2：章节审核优化（并行）
            print(f"   📋 第{iteration}轮：章节审核优化（并行）")
            await self.parallel_processor.section_optimization_stage(sections, data_sources, iteration)

            # 子阶段3.3：整体文档审核（串行）
            print(f"   📄 第{iteration}轮：整体文档审核（串行）")
            await self.serial_processor.overall_document_audit_stage(sections, topic, iteration)

            # 保存每轮优化checkpoint
            self.generator.create_checkpoint(f"optimization_round_{iteration}", {
                "topic": topic,
                "sections": sections,
                "data_sources": data_sources,
                "completed_iterations": iteration,
                "stage": f"3.{iteration}"
            })

            if iteration_pbar:
                iteration_pbar.update(1)

            print(f"   ✅ 第{iteration}轮迭代优化完成")

        if iteration_pbar:
            iteration_pbar.close()

        print("   🎉 所有迭代优化完成")

    async def resume_from_checkpoint(self, checkpoint_id: str, topic: str, data_sources: List[str], framework_file_path: Optional[str] = None) -> str:
        """
        从checkpoint恢复工作流执行
        
        根据checkpoint中的stage信息，从相应阶段继续执行
        """
        print(f"🔄 从checkpoint恢复工作流: {checkpoint_id}")

        # 加载checkpoint数据
        checkpoint_data = self.generator._load_checkpoint_data(checkpoint_id)
        if not checkpoint_data:
            raise ValueError(f"无法加载checkpoint: {checkpoint_id}")

        stage = checkpoint_data.get("stage", 0)
        framework = checkpoint_data.get("framework", {})
        sections = checkpoint_data.get("sections", [])

        print(f"📊 恢复到阶段: {stage}")

        try:
            if stage < 2:
                # 从阶段2开始：并行内容生成
                print("🔄 从阶段2开始：并行内容生成")
                await self.parallel_processor.content_generation_stage(sections, data_sources)
                
                # 继续后续阶段
                await self._execute_iterative_optimization_stage(sections, data_sources, topic)
                output_path = await self.serial_processor.final_document_generation_stage(topic, framework)

            elif stage < 3:
                # 从阶段3开始：迭代优化
                print("🔄 从阶段3开始：迭代优化")
                await self._execute_iterative_optimization_stage(sections, data_sources, topic)
                output_path = await self.serial_processor.final_document_generation_stage(topic, framework)

            elif stage < 4:
                # 从阶段4开始：最终文档生成
                print("🔄 从阶段4开始：最终文档生成")
                output_path = await self.serial_processor.final_document_generation_stage(topic, framework)

            else:
                # 已完成，直接返回
                output_path = checkpoint_data.get("output_path", "")
                if not output_path or not Path(output_path).exists():
                    print("🔄 重新生成最终文档")
                    output_path = await self.serial_processor.final_document_generation_stage(topic, framework)

            print(f"✅ 工作流恢复完成: {output_path}")
            return output_path

        except Exception as e:
            print(f"❌ 工作流恢复失败: {str(e)}")
            raise

    def get_workflow_status(self) -> Dict[str, Any]:
        """
        获取当前工作流状态
        """
        return {
            "serial_processor": "已初始化",
            "parallel_processor": "已初始化",
            "supported_stages": [
                "阶段1: 串行框架生成",
                "阶段2: 并行内容生成", 
                "阶段3: 混合迭代优化",
                "阶段4: 串行文档生成"
            ],
            "checkpoint_support": True,
            "resume_support": True
        }

    def print_workflow_summary(self):
        """
        打印工作流摘要
        """
        print("\n📋 工作流协调器摘要")
        print("=" * 60)
        print("🔄 支持的处理模式:")
        print("   • 串行处理: 框架生成、整体审核、文档生成")
        print("   • 并行处理: 内容生成、章节优化、参考学习")
        print("   • 混合模式: 迭代优化阶段")
        print()
        print("📊 工作流阶段:")
        print("   1️⃣ 串行框架生成 (统筹模型)")
        print("   2️⃣ 并行内容生成 (执行模型)")
        print("   3️⃣ 混合迭代优化 (3轮)")
        print("   4️⃣ 串行文档生成 (最终输出)")
        print()
        print("💾 支持功能:")
        print("   • Checkpoint保存和恢复")
        print("   • 进度跟踪和显示")
        print("   • 错误处理和恢复")
        print("   • 批次大小优化")

    async def test_workflow_components(self) -> bool:
        """
        测试工作流组件
        """
        print("🧪 测试工作流组件...")
        
        try:
            # 测试串行处理器
            print("   🔍 测试串行处理器...")
            serial_status = hasattr(self.serial_processor, 'generate_framework_stage')
            print(f"   {'✅' if serial_status else '❌'} 串行处理器: {'正常' if serial_status else '异常'}")

            # 测试并行处理器
            print("   🔍 测试并行处理器...")
            parallel_status = hasattr(self.parallel_processor, 'content_generation_stage')
            print(f"   {'✅' if parallel_status else '❌'} 并行处理器: {'正常' if parallel_status else '异常'}")

            # 测试协调器方法
            print("   🔍 测试协调器方法...")
            coordinator_status = hasattr(self, 'execute_complete_workflow')
            print(f"   {'✅' if coordinator_status else '❌'} 协调器: {'正常' if coordinator_status else '异常'}")

            overall_status = serial_status and parallel_status and coordinator_status
            print(f"\n   📊 整体状态: {'✅ 所有组件正常' if overall_status else '❌ 存在问题'}")
            
            return overall_status

        except Exception as e:
            print(f"   ❌ 组件测试失败: {str(e)}")
            return False
