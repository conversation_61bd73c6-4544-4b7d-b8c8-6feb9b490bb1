#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
完整的文件读取器模块
完全按照原代码实现，支持所有文件格式
"""

import os
import json
from pathlib import Path
from typing import Dict, Any, List, Optional

class CompleteFileReader:
    """完整的文件读取器 - 按原代码实现"""
    
    def __init__(self):
        # 支持的文件扩展名（完全按原代码）
        self.supported_extensions = {
            # 文本文件
            '.txt', '.md', '.rst', '.log', '.rtf',
            
            # Office文档
            '.docx', '.doc', '.xlsx', '.xls', '.csv', '.tsv', '.pptx', '.ppt',
            
            # PDF文件
            '.pdf',
            
            # 数据文件
            '.json', '.xml', '.yaml', '.yml',
            
            # 代码文件
            '.py', '.js', '.html', '.css', '.sql', '.java', '.cpp', '.c', '.h',
            '.php', '.rb', '.go', '.sh', '.bat',
            
            # 配置文件
            '.ini', '.cfg', '.conf', '.properties', '.toml',
            
            # 图片文件
            '.png', '.jpg', '.jpeg', '.gif', '.bmp', '.svg', '.webp', '.tiff',
            
            # 其他格式
            '.odt', '.ods', '.epub', '.mobi'
        }
    
    def read_file(self, file_path: str) -> str:
        """读取单个文件（按原代码逻辑）"""
        try:
            path = Path(file_path)
            if not path.exists():
                return ""
            
            return self._read_single_framework_file(path)
                
        except Exception as e:
            print(f"读取文件失败 {file_path}: {str(e)}")
            return ""

    def _read_single_framework_file(self, file_path: Path) -> str:
        """读取单个框架文件，根据扩展名选择合适的读取方法（完全按原代码）"""
        try:
            suffix = file_path.suffix.lower()

            # 文本文件
            if suffix in ['.md', '.txt', '.rst', '.log', '.py', '.js', '.html', '.css',
                         '.ini', '.cfg', '.conf', '.properties', '.toml', '.sh', '.bat']:
                return self._read_text_file(file_path)

            # JSON文件
            elif suffix == '.json':
                return self._read_json_file(file_path)

            # XML文件
            elif suffix == '.xml':
                return self._read_xml_file(file_path)

            # YAML文件
            elif suffix in ['.yaml', '.yml']:
                return self._read_yaml_file(file_path)

            # Office文档
            elif suffix == '.docx':
                return self._read_docx_file(file_path)
            elif suffix == '.doc':
                return self._read_doc_file(file_path)
            elif suffix in ['.xlsx', '.xls']:
                return self._read_excel_file(file_path)
            elif suffix == '.csv':
                return self._read_csv_file(file_path)
            elif suffix == '.tsv':
                return self._read_tsv_file(file_path)
            elif suffix in ['.pptx', '.ppt']:
                return self._read_ppt_file(file_path)

            # PDF文件
            elif suffix == '.pdf':
                return self._read_pdf_file(file_path)

            # RTF文件
            elif suffix == '.rtf':
                return self._read_rtf_file(file_path)

            # 图片文件
            elif suffix in ['.png', '.jpg', '.jpeg', '.gif', '.bmp', '.svg', '.webp', '.tiff']:
                return self._read_image_file(file_path)

            # 其他Office格式
            elif suffix == '.odt':
                return self._read_odt_file(file_path)
            elif suffix == '.ods':
                return self._read_ods_file(file_path)

            # 电子书格式
            elif suffix == '.epub':
                return self._read_epub_file(file_path)
            elif suffix == '.mobi':
                return self._read_mobi_file(file_path)

            # 默认尝试文本读取
            else:
                return self._read_text_file(file_path)

        except Exception as e:
            print(f"读取文件 {file_path.name} 失败: {str(e)}")
            return ""
    
    def _read_text_file(self, path: Path) -> str:
        """读取文本文件（支持多种编码）"""
        encodings = ['utf-8', 'gbk', 'gb2312', 'latin-1', 'ascii']
        
        for encoding in encodings:
            try:
                with open(path, 'r', encoding=encoding) as f:
                    content = f.read()
                return content
            except UnicodeDecodeError:
                continue
            except Exception as e:
                print(f"读取文本文件失败 {path.name}: {str(e)}")
                return ""
        
        print(f"无法解码文件 {path.name}，尝试了所有编码")
        return ""
    
    def _read_json_file(self, path: Path) -> str:
        """读取JSON文件"""
        try:
            with open(path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            return json.dumps(data, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"读取JSON文件失败 {path.name}: {str(e)}")
            return ""
    
    def _read_xml_file(self, path: Path) -> str:
        """读取XML文件"""
        try:
            return self._read_text_file(path)
        except Exception as e:
            print(f"读取XML文件失败 {path.name}: {str(e)}")
            return ""
    
    def _read_yaml_file(self, path: Path) -> str:
        """读取YAML文件"""
        try:
            import yaml
            with open(path, 'r', encoding='utf-8') as f:
                data = yaml.safe_load(f)
            return yaml.dump(data, default_flow_style=False, allow_unicode=True)
        except ImportError:
            print(f"需要安装PyYAML库来读取YAML文件: {path.name}")
            return self._read_text_file(path)
        except Exception as e:
            print(f"读取YAML文件失败 {path.name}: {str(e)}")
            return ""
    
    def _read_docx_file(self, path: Path) -> str:
        """读取Word文档"""
        try:
            from docx import Document
            doc = Document(path)
            
            content_parts = []
            
            # 读取段落
            for paragraph in doc.paragraphs:
                if paragraph.text.strip():
                    content_parts.append(paragraph.text)
            
            # 读取表格
            for table in doc.tables:
                table_data = []
                for row in table.rows:
                    row_data = []
                    for cell in row.cells:
                        row_data.append(cell.text.strip())
                    table_data.append(" | ".join(row_data))
                
                if table_data:
                    content_parts.append("\n表格内容:")
                    content_parts.extend(table_data)
            
            return '\n'.join(content_parts)
            
        except ImportError:
            print(f"需要安装python-docx库来读取Word文档: {path.name}")
            return ""
        except Exception as e:
            print(f"读取Word文档失败 {path.name}: {str(e)}")
            return ""
    
    def _read_doc_file(self, path: Path) -> str:
        """读取旧版Word文档"""
        try:
            # 尝试使用python-docx2txt
            import docx2txt
            return docx2txt.process(str(path))
        except ImportError:
            print(f"需要安装docx2txt库来读取.doc文件: {path.name}")
            return ""
        except Exception as e:
            print(f"读取.doc文件失败 {path.name}: {str(e)}")
            return ""
    
    def _read_excel_file(self, path: Path) -> str:
        """读取Excel文件"""
        try:
            import pandas as pd
            
            # 读取所有工作表
            excel_file = pd.ExcelFile(path)
            content_parts = []
            
            for sheet_name in excel_file.sheet_names:
                df = pd.read_excel(path, sheet_name=sheet_name)
                content_parts.append(f"工作表: {sheet_name}")
                content_parts.append(df.to_string(index=False))
                content_parts.append("")
            
            return '\n'.join(content_parts)
            
        except ImportError:
            print(f"需要安装pandas和openpyxl库来读取Excel文件: {path.name}")
            return ""
        except Exception as e:
            print(f"读取Excel文件失败 {path.name}: {str(e)}")
            return ""
    
    def _read_csv_file(self, path: Path) -> str:
        """读取CSV文件"""
        try:
            import pandas as pd
            df = pd.read_csv(path, encoding='utf-8')
            return df.to_string(index=False)
        except UnicodeDecodeError:
            try:
                import pandas as pd
                df = pd.read_csv(path, encoding='gbk')
                return df.to_string(index=False)
            except Exception as e:
                print(f"读取CSV文件失败 {path.name}: {str(e)}")
                return ""
        except ImportError:
            print(f"需要安装pandas库来读取CSV文件: {path.name}")
            return ""
        except Exception as e:
            print(f"读取CSV文件失败 {path.name}: {str(e)}")
            return ""
    
    def _read_tsv_file(self, path: Path) -> str:
        """读取TSV文件"""
        try:
            import pandas as pd
            df = pd.read_csv(path, sep='\t', encoding='utf-8')
            return df.to_string(index=False)
        except ImportError:
            print(f"需要安装pandas库来读取TSV文件: {path.name}")
            return ""
        except Exception as e:
            print(f"读取TSV文件失败 {path.name}: {str(e)}")
            return ""
    
    def _read_ppt_file(self, path: Path) -> str:
        """读取PowerPoint文件"""
        try:
            from pptx import Presentation
            prs = Presentation(path)
            
            content_parts = []
            for i, slide in enumerate(prs.slides):
                content_parts.append(f"幻灯片 {i+1}:")
                for shape in slide.shapes:
                    if hasattr(shape, "text") and shape.text.strip():
                        content_parts.append(shape.text)
                content_parts.append("")
            
            return '\n'.join(content_parts)
            
        except ImportError:
            print(f"需要安装python-pptx库来读取PowerPoint文件: {path.name}")
            return ""
        except Exception as e:
            print(f"读取PowerPoint文件失败 {path.name}: {str(e)}")
            return ""

    def _read_pdf_file(self, path: Path) -> str:
        """读取PDF文件（多种方法）"""
        # 方法1：尝试使用PyPDF2
        try:
            import PyPDF2
            with open(path, 'rb') as f:
                reader = PyPDF2.PdfReader(f)
                text_parts = []
                for page_num, page in enumerate(reader.pages):
                    text = page.extract_text()
                    if text.strip():
                        text_parts.append(f"第{page_num+1}页:\n{text}")

                if text_parts:
                    return '\n\n'.join(text_parts)
        except ImportError:
            pass
        except Exception as e:
            print(f"PyPDF2读取失败: {str(e)}")

        # 方法2：尝试使用pdfplumber
        try:
            import pdfplumber
            with pdfplumber.open(path) as pdf:
                text_parts = []
                for page_num, page in enumerate(pdf.pages):
                    text = page.extract_text()
                    if text and text.strip():
                        text_parts.append(f"第{page_num+1}页:\n{text}")

                if text_parts:
                    return '\n\n'.join(text_parts)
        except ImportError:
            pass
        except Exception as e:
            print(f"pdfplumber读取失败: {str(e)}")

        # 方法3：尝试使用pymupdf
        try:
            import fitz  # PyMuPDF
            doc = fitz.open(str(path))
            text_parts = []

            for page_num in range(len(doc)):
                page = doc.load_page(page_num)
                text = page.get_text()
                if text.strip():
                    text_parts.append(f"第{page_num+1}页:\n{text}")

            doc.close()

            if text_parts:
                return '\n\n'.join(text_parts)
        except ImportError:
            pass
        except Exception as e:
            print(f"PyMuPDF读取失败: {str(e)}")

        print(f"需要安装PDF处理库（PyPDF2、pdfplumber或PyMuPDF）来读取PDF文件: {path.name}")
        return ""

    def _read_rtf_file(self, path: Path) -> str:
        """读取RTF文件"""
        try:
            from striprtf.striprtf import rtf_to_text
            with open(path, 'r', encoding='utf-8') as f:
                rtf_content = f.read()
            return rtf_to_text(rtf_content)
        except ImportError:
            print(f"需要安装striprtf库来读取RTF文件: {path.name}")
            return self._read_text_file(path)
        except Exception as e:
            print(f"读取RTF文件失败 {path.name}: {str(e)}")
            return ""

    def _read_image_file(self, path: Path) -> str:
        """读取图片文件（使用OCR）"""
        try:
            # 尝试使用Tesseract OCR
            from PIL import Image
            import pytesseract

            image = Image.open(path)
            text = pytesseract.image_to_string(image, lang='chi_sim+eng')

            if text.strip():
                return f"图片OCR内容 ({path.name}):\n{text}"
            else:
                return f"图片文件: {path.name} (无法提取文本内容)"

        except ImportError:
            return f"图片文件: {path.name} (需要安装PIL和pytesseract库进行OCR)"
        except Exception as e:
            print(f"图片OCR失败 {path.name}: {str(e)}")
            return f"图片文件: {path.name} (OCR处理失败)"

    def _read_odt_file(self, path: Path) -> str:
        """读取OpenDocument文本文件"""
        try:
            from odf import text, teletype
            from odf.opendocument import load

            textdoc = load(str(path))
            allparas = textdoc.getElementsByType(text.P)
            content_parts = []

            for para in allparas:
                para_text = teletype.extractText(para)
                if para_text.strip():
                    content_parts.append(para_text)

            return '\n'.join(content_parts)

        except ImportError:
            print(f"需要安装odfpy库来读取ODT文件: {path.name}")
            return ""
        except Exception as e:
            print(f"读取ODT文件失败 {path.name}: {str(e)}")
            return ""

    def _read_ods_file(self, path: Path) -> str:
        """读取OpenDocument电子表格文件"""
        try:
            import pandas as pd
            df = pd.read_excel(path, engine='odf')
            return df.to_string(index=False)
        except ImportError:
            print(f"需要安装pandas和odfpy库来读取ODS文件: {path.name}")
            return ""
        except Exception as e:
            print(f"读取ODS文件失败 {path.name}: {str(e)}")
            return ""

    def _read_epub_file(self, path: Path) -> str:
        """读取EPUB电子书文件"""
        try:
            import ebooklib
            from ebooklib import epub
            from bs4 import BeautifulSoup

            book = epub.read_epub(str(path))
            content_parts = []

            for item in book.get_items():
                if item.get_type() == ebooklib.ITEM_DOCUMENT:
                    soup = BeautifulSoup(item.get_content(), 'html.parser')
                    text = soup.get_text()
                    if text.strip():
                        content_parts.append(text)

            return '\n\n'.join(content_parts)

        except ImportError:
            print(f"需要安装ebooklib和beautifulsoup4库来读取EPUB文件: {path.name}")
            return ""
        except Exception as e:
            print(f"读取EPUB文件失败 {path.name}: {str(e)}")
            return ""

    def _read_mobi_file(self, path: Path) -> str:
        """读取MOBI电子书文件"""
        try:
            # MOBI格式比较复杂，通常需要专门的库
            print(f"MOBI文件格式暂不支持直接读取: {path.name}")
            return f"MOBI电子书文件: {path.name} (格式暂不支持)"
        except Exception as e:
            print(f"读取MOBI文件失败 {path.name}: {str(e)}")
            return ""
