"""
图像处理器模块 - 严格按照原始代码的图像处理实现
处理PDF图像提取、图像分析和文本相关性计算
"""

import os
import base64
import hashlib
import fitz  # PyMuPDF
from PIL import Image
import io
import json
import requests
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple
from datetime import datetime
import time

from ..config import (
    PDF_IMAGE_DPI, PDF_IMAGE_FORMAT,
    MAX_IMAGE_SIZE_MB, IMAGE_COMPRESSION_QUALITY,
    CACHE_DIR, GEMINI_API_KEY, GEMINI_BASE_URL,
    MAX_RETRIES, RETRY_DELAY
)


class ImageHandler:
    """图像处理器 - 与原始代码完全相同的实现"""
    
    def __init__(self):
        """初始化图像处理器"""
        self.cache_dir = Path(CACHE_DIR) / "images"
        self.cache_dir.mkdir(parents=True, exist_ok=True)
        self.gemini_api_key = GEMINI_API_KEY
        
    def extract_images_from_pdf(self, pdf_path: str, output_dir: str = None) -> List[Dict[str, Any]]:
        """
        从PDF中提取图像 - 与原始代码完全相同
        
        Args:
            pdf_path: PDF文件路径
            output_dir: 图像输出目录
            
        Returns:
            提取的图像信息列表
        """
        if output_dir is None:
            output_dir = self.cache_dir / Path(pdf_path).stem
        
        output_dir = Path(output_dir)
        output_dir.mkdir(parents=True, exist_ok=True)
        
        images = []
        
        try:
            pdf_document = fitz.open(pdf_path)
            
            for page_num in range(pdf_document.page_count):
                page = pdf_document[page_num]
                image_list = page.get_images()
                
                for img_index, img in enumerate(image_list):
                    try:
                        # 获取图像对象
                        xref = img[0]
                        pix = fitz.Pixmap(pdf_document, xref)
                        
                        # 转换颜色空间
                        if pix.n - pix.alpha > 3:
                            pix = fitz.Pixmap(fitz.csRGB, pix)
                        
                        # 保存图像
                        img_filename = f"page{page_num + 1}_img{img_index + 1}.{PDF_IMAGE_FORMAT.lower()}"
                        img_path = output_dir / img_filename
                        
                        if pix.alpha:
                            pix = fitz.Pixmap(pix, 0)  # 移除alpha通道
                        
                        # 保存为PIL图像
                        img_data = pix.pil_tobytes(format=PDF_IMAGE_FORMAT)
                        pil_image = Image.open(io.BytesIO(img_data))
                        
                        # 压缩图像
                        pil_image.save(img_path, PDF_IMAGE_FORMAT, 
                                     quality=IMAGE_COMPRESSION_QUALITY, 
                                     optimize=True)
                        
                        # 获取图像位置信息
                        img_rect = page.get_image_bbox(img[7])
                        
                        image_info = {
                            'page': page_num + 1,
                            'index': img_index + 1,
                            'path': str(img_path),
                            'size': os.path.getsize(img_path),
                            'dimensions': (pil_image.width, pil_image.height),
                            'position': {
                                'x0': img_rect.x0,
                                'y0': img_rect.y0,
                                'x1': img_rect.x1,
                                'y1': img_rect.y1
                            }
                        }
                        
                        images.append(image_info)
                        print(f"   ✅ 提取图像: {img_filename}")
                        
                        pix = None  # 释放内存
                        
                    except Exception as e:
                        print(f"   ⚠️ 提取图像失败 (页{page_num + 1}, 图{img_index + 1}): {str(e)}")
            
            pdf_document.close()
            
        except Exception as e:
            print(f"❌ 处理PDF图像失败: {str(e)}")
        
        return images
    
    def analyze_image_content(self, image_path: str, use_cache: bool = True) -> Dict[str, Any]:
        """
        分析图像内容 - 与原始代码完全相同
        
        Args:
            image_path: 图像路径
            use_cache: 是否使用缓存
            
        Returns:
            图像分析结果
        """
        if not self.gemini_api_key:
            return self._get_basic_image_info(image_path)
        
        # 检查缓存
        if use_cache:
            cached_result = self._get_cached_analysis(image_path)
            if cached_result:
                return cached_result
        
        try:
            # 读取图像
            with open(image_path, 'rb') as f:
                image_data = f.read()
            
            # 检查图像大小
            if len(image_data) > MAX_IMAGE_SIZE_MB * 1024 * 1024:
                # 压缩图像
                image_data = self._compress_image(image_data)
            
            # 转换为base64
            image_base64 = base64.b64encode(image_data).decode()
            
            # 调用Gemini API
            prompt = """请分析这张图片，提供以下信息：
1. 图片类型（图表/流程图/照片/示意图等）
2. 主要内容描述
3. 关键信息提取（如数据、趋势、关键词等）
4. 与产业研究报告的相关性

请用JSON格式返回结果。"""
            
            result = self._call_gemini_vision(image_base64, prompt)
            
            if result:
                # 解析结果
                analysis = self._parse_image_analysis(result)
                
                # 保存到缓存
                if use_cache:
                    self._save_analysis_cache(image_path, analysis)
                
                return analysis
            
        except Exception as e:
            print(f"   ⚠️ 图像分析失败: {str(e)}")
        
        return self._get_basic_image_info(image_path)
    
    def _call_gemini_vision(self, image_base64: str, prompt: str) -> Optional[str]:
        """调用Gemini Vision API - 与原始代码完全相同"""
        url = f"{GEMINI_BASE_URL}/gemini-1.5-flash:generateContent?key={self.gemini_api_key}"
        
        headers = {
            "Content-Type": "application/json"
        }
        
        data = {
            "contents": [{
                "parts": [
                    {"text": prompt},
                    {
                        "inlineData": {
                            "mimeType": f"image/{PDF_IMAGE_FORMAT.lower()}",
                            "data": image_base64
                        }
                    }
                ]
            }],
            "generationConfig": {
                "temperature": 0.4,
                "maxOutputTokens": 2048
            }
        }
        
        for attempt in range(MAX_RETRIES):
            try:
                response = requests.post(url, headers=headers, json=data, timeout=30)
                
                if response.status_code == 200:
                    result = response.json()
                    if "candidates" in result and result["candidates"]:
                        return result["candidates"][0]["content"]["parts"][0]["text"]
                else:
                    print(f"   ⚠️ Gemini API错误 (尝试 {attempt + 1}/{MAX_RETRIES}): {response.status_code}")
                    
            except Exception as e:
                print(f"   ⚠️ 调用Gemini失败 (尝试 {attempt + 1}/{MAX_RETRIES}): {str(e)}")
            
            if attempt < MAX_RETRIES - 1:
                time.sleep(RETRY_DELAY * (attempt + 1))
        
        return None
    
    def _parse_image_analysis(self, result: str) -> Dict[str, Any]:
        """解析图像分析结果 - 与原始代码完全相同"""
        try:
            # 尝试提取JSON内容
            if "```json" in result:
                json_start = result.find("```json") + 7
                json_end = result.find("```", json_start)
                json_str = result[json_start:json_end].strip()
            else:
                # 尝试直接解析
                json_str = result.strip()
            
            analysis = json.loads(json_str)
            
            # 确保包含必要字段
            default_analysis = {
                "type": "未知",
                "description": "",
                "key_info": [],
                "relevance": "中等"
            }
            
            for key, value in default_analysis.items():
                if key not in analysis:
                    analysis[key] = value
            
            return analysis
            
        except:
            # 如果解析失败，返回基础分析
            return {
                "type": "图像",
                "description": result[:200] + "..." if len(result) > 200 else result,
                "key_info": [],
                "relevance": "未知"
            }
    
    def _get_basic_image_info(self, image_path: str) -> Dict[str, Any]:
        """获取基础图像信息 - 与原始代码完全相同"""
        try:
            with Image.open(image_path) as img:
                return {
                    "type": "图像",
                    "description": f"图像文件 ({img.format}, {img.width}x{img.height})",
                    "key_info": [f"尺寸: {img.width}x{img.height}", f"格式: {img.format}"],
                    "relevance": "未分析"
                }
        except:
            return {
                "type": "未知",
                "description": "无法读取图像信息",
                "key_info": [],
                "relevance": "未知"
            }
    
    def _compress_image(self, image_data: bytes, max_size_mb: float = 5.0) -> bytes:
        """压缩图像 - 与原始代码完全相同"""
        img = Image.open(io.BytesIO(image_data))
        
        # 计算压缩比例
        current_size = len(image_data) / (1024 * 1024)
        if current_size <= max_size_mb:
            return image_data
        
        scale_factor = (max_size_mb / current_size) ** 0.5
        
        # 调整尺寸
        new_width = int(img.width * scale_factor)
        new_height = int(img.height * scale_factor)
        img = img.resize((new_width, new_height), Image.Resampling.LANCZOS)
        
        # 保存到字节流
        output = io.BytesIO()
        img.save(output, format=PDF_IMAGE_FORMAT, quality=85, optimize=True)
        
        return output.getvalue()
    
    def _get_cached_analysis(self, image_path: str) -> Optional[Dict[str, Any]]:
        """获取缓存的分析结果 - 与原始代码完全相同"""
        try:
            # 计算图像哈希
            image_hash = self._calculate_image_hash(image_path)
            cache_file = self.cache_dir / f"{image_hash}_analysis.json"
            
            if cache_file.exists():
                with open(cache_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            
        except Exception as e:
            print(f"   ⚠️ 读取分析缓存失败: {str(e)}")
        
        return None
    
    def _save_analysis_cache(self, image_path: str, analysis: Dict[str, Any]):
        """保存分析结果到缓存 - 与原始代码完全相同"""
        try:
            image_hash = self._calculate_image_hash(image_path)
            cache_file = self.cache_dir / f"{image_hash}_analysis.json"
            
            with open(cache_file, 'w', encoding='utf-8') as f:
                json.dump(analysis, f, ensure_ascii=False, indent=2)
                
        except Exception as e:
            print(f"   ⚠️ 保存分析缓存失败: {str(e)}")
    
    def _calculate_image_hash(self, image_path: str) -> str:
        """计算图像哈希值 - 与原始代码完全相同"""
        with open(image_path, 'rb') as f:
            return hashlib.md5(f.read()).hexdigest()
    
    def calculate_image_text_relevance(self, image_analysis: Dict[str, Any], 
                                     text_context: str) -> float:
        """
        计算图像与文本的相关性 - 与原始代码完全相同
        
        Args:
            image_analysis: 图像分析结果
            text_context: 文本上下文
            
        Returns:
            相关性分数（0-1）
        """
        if not text_context or not image_analysis:
            return 0.0
        
        # 提取关键信息
        img_description = image_analysis.get('description', '')
        img_key_info = ' '.join(image_analysis.get('key_info', []))
        img_type = image_analysis.get('type', '')
        
        # 合并图像信息
        img_text = f"{img_description} {img_key_info} {img_type}".lower()
        text_context_lower = text_context.lower()
        
        # 计算关键词匹配度
        score = 0.0
        
        # 1. 类型相关性
        type_keywords = {
            '图表': ['数据', '统计', '分析', '趋势', '比例'],
            '流程图': ['流程', '步骤', '过程', '阶段'],
            '示意图': ['结构', '原理', '组成', '架构'],
            '照片': ['实物', '产品', '设备', '现场']
        }
        
        for img_type_key, keywords in type_keywords.items():
            if img_type_key in img_type:
                for keyword in keywords:
                    if keyword in text_context_lower:
                        score += 0.1
        
        # 2. 内容关键词匹配
        # 提取文本中的关键词（简单实现）
        text_words = set(text_context_lower.split())
        img_words = set(img_text.split())
        
        # 计算交集
        common_words = text_words & img_words
        if text_words:
            word_overlap = len(common_words) / len(text_words)
            score += word_overlap * 0.5
        
        # 3. 特殊关键词权重
        important_keywords = ['核心', '关键', '重要', '主要', '趋势', '数据', '分析']
        for keyword in important_keywords:
            if keyword in img_text and keyword in text_context_lower:
                score += 0.1
        
        # 限制分数范围
        return min(1.0, score)
