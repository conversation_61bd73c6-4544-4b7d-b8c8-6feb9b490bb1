#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
内容处理器模块
处理报告内容的生成、优化和格式化
"""

from typing import Dict, List, Any
from .content_cleaner import ContentCleaner


class ContentProcessor:
    """内容处理器"""
    
    def __init__(self, generator):
        self.generator = generator
        self.content_cleaner = ContentCleaner()
    
    def process_content(self, content: str, content_type: str = "general") -> str:
        """处理内容"""
        if not content:
            return content
        
        # 基础清理
        processed_content = self.content_cleaner.clean_model_response(content)
        
        # 根据内容类型进行特殊处理
        if content_type == "framework":
            processed_content = self._process_framework_content(processed_content)
        elif content_type == "section":
            processed_content = self._process_section_content(processed_content)
        elif content_type == "final":
            processed_content = self._process_final_content(processed_content)
        
        return processed_content
    
    def _process_framework_content(self, content: str) -> str:
        """处理框架内容"""
        # 框架内容通常是JSON格式，需要特殊处理
        return content
    
    def _process_section_content(self, content: str) -> str:
        """处理章节内容"""
        # 章节内容需要保持结构化
        return self.content_cleaner.extract_final_content_only(content)
    
    def _process_final_content(self, content: str) -> str:
        """处理最终内容"""
        # 最终内容需要最彻底的清理
        return self.content_cleaner.clean_content_thoroughly(content)
    
    def optimize_content_structure(self, framework: Dict[str, Any]) -> Dict[str, Any]:
        """优化内容结构"""
        # 递归处理所有节点的内容
        self._optimize_node_content(framework)
        return framework
    
    def _optimize_node_content(self, node: Dict[str, Any]):
        """优化节点内容"""
        if "content" in node and node["content"]:
            node["content"] = self.process_content(node["content"], "section")
        
        # 递归处理子节点
        if "children" in node:
            for child in node["children"]:
                self._optimize_node_content(child)
    
    def format_for_output(self, content: str, format_type: str = "docx") -> str:
        """格式化输出内容"""
        if format_type == "docx":
            return self._format_for_docx(content)
        elif format_type == "markdown":
            return self._format_for_markdown(content)
        else:
            return content
    
    def _format_for_docx(self, content: str) -> str:
        """格式化为Word文档格式"""
        # Word文档格式化
        return content
    
    def _format_for_markdown(self, content: str) -> str:
        """格式化为Markdown格式"""
        # Markdown格式化
        return content
