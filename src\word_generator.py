"""
Word文档生成模块
将结构化的报告内容生成为Word文档
"""
from typing import Dict, Any, List
from pathlib import Path
from docx import Document
from docx.shared import Pt, Inches
from docx.enum.style import WD_STYLE_TYPE
from docx.enum.text import WD_PARAGRAPH_ALIGNMENT
from docx.oxml.ns import qn

from .config import Config
from .state import ReportState
from .logger import ProcessLogger


class WordGenerator:
    """
    Word文档生成器
    负责将报告内容转换为格式化的Word文档
    """
    
    def __init__(self, config: Config, logger: ProcessLogger):
        self.config = config
        self.logger = logger
    
    def generate_word_document(self, state: ReportState, output_path: str) -> str:
        """
        生成Word文档
        
        Args:
            state: 报告状态
            output_path: 输出文件路径
            
        Returns:
            生成的文档路径
        """
        self.logger.log_node_start("generate_word_document", output_path=output_path)
        
        try:
            # 创建新文档
            doc = Document()
            
            # 设置文档样式
            self._setup_document_styles(doc)
            
            # 添加标题页
            self._add_title_page(doc, state.topic)
            
            # 添加目录（可选）
            # self._add_table_of_contents(doc)
            
            # 添加报告内容
            sections = state.report_structure.get("sections", [])
            for idx, section in enumerate(sections):
                section["index"] = idx + 1  # 添加一级章节索引
                self._add_section_to_document(doc, section)
            
            # 添加引用来源附录
            if state.source_citations:
                self._add_citations_appendix(doc, state.source_citations)
            
            # 添加元数据页脚
            self._add_metadata_footer(doc, state)
            
            # 保存文档
            output_file = Path(output_path)
            output_file.parent.mkdir(parents=True, exist_ok=True)
            doc.save(str(output_file))
            
            self.logger.log_node_complete("generate_word_document", duration=0, output_path=str(output_file))
            
            return str(output_file)
            
        except Exception as e:
            self.logger.log_node_error("generate_word_document", str(e))
            raise
    
    def _setup_document_styles(self, doc: Document):
        """
        设置文档样式
        """
        # 设置默认字体
        doc.styles['Normal'].font.name = self.config.output.word_document.font_family
        doc.styles['Normal']._element.rPr.rFonts.set(qn('w:eastAsia'), '宋体')
        doc.styles['Normal'].font.size = Pt(self.config.output.word_document.font_size_body)
        
        # 设置标题样式
        for i in range(1, 7):  # 标题1到标题6
            style_name = f'Heading {i}'
            if style_name in doc.styles:
                style = doc.styles[style_name]
                style.font.name = self.config.output.word_document.font_family
                style._element.rPr.rFonts.set(qn('w:eastAsia'), '黑体')
                
                # 设置字体大小
                font_size_attr = f"font_size_heading_{i}"
                font_size = getattr(self.config.output.word_document, font_size_attr)
                style.font.size = Pt(font_size)
                
                # 设置标题编号
                style.font.bold = True
                style.paragraph_format.keep_with_next = True
                style.paragraph_format.keep_together = True
    
    def _add_title_page(self, doc: Document, topic: str):
        """
        添加标题页
        """
        # 添加标题
        title = doc.add_heading(topic, level=0)
        title.alignment = WD_PARAGRAPH_ALIGNMENT.CENTER
        
        # 添加空行
        for _ in range(3):
            doc.add_paragraph()
        
        # 添加副标题
        subtitle = doc.add_paragraph("AI产业研究报告")
        subtitle.alignment = WD_PARAGRAPH_ALIGNMENT.CENTER
        subtitle.runs[0].font.size = Pt(16)
        
        # 添加日期
        from datetime import datetime
        date_para = doc.add_paragraph(datetime.now().strftime("%Y年%m月%d日"))
        date_para.alignment = WD_PARAGRAPH_ALIGNMENT.CENTER
        
        # 添加分页符
        doc.add_page_break()
    
    def _add_section_to_document(self, doc: Document, section: Dict[str, Any], parent_number: str = ""):
        """
        递归添加章节到文档
        
        Args:
            doc: Word文档对象
            section: 章节数据
            parent_number: 父章节编号
        """
        level = section.get("level", 1)
        title = section.get("title", "")
        content = section.get("content", "")
        
        # 生成章节编号
        if parent_number:
            section_number = f"{parent_number}.{section.get('index', 1)}"
        else:
            section_number = str(section.get('index', 1))
        
        # 添加标题
        if level <= 6:  # Word支持的标题级别
            heading = doc.add_heading(f"{title}", level=level)
            # 设置段落格式
            heading.paragraph_format.space_before = Pt(12)
            heading.paragraph_format.space_after = Pt(6)
        else:
            # 超过6级的标题使用加粗正文
            para = doc.add_paragraph(f"{title}")
            para.runs[0].bold = True
        
        # 添加内容
        if content and content.strip():
            content_para = doc.add_paragraph(content)
            content_para.paragraph_format.first_line_indent = Inches(0.5)
            content_para.paragraph_format.line_spacing = self.config.output.word_document.line_spacing
            content_para.paragraph_format.space_after = Pt(6)
        
        # 递归处理子章节
        children = section.get("children", [])
        for idx, child in enumerate(children):
            child["index"] = idx + 1  # 添加索引用于编号
            self._add_section_to_document(doc, child, section_number)
    
    def _add_citations_appendix(self, doc: Document, citations: Dict[str, List[str]]):
        """
        添加引用来源附录
        
        Args:
            doc: Word文档对象
            citations: 引用来源字典
        """
        # 添加分页符
        doc.add_page_break()
        
        # 添加附录标题
        doc.add_heading("附录：引用来源", level=1)
        
        # 按章节列出引用
        for section_key, sources in citations.items():
            if sources:
                # 添加章节标题
                doc.add_heading(section_key.replace("level_", "").replace("_", " - "), level=2)
                
                # 添加引用列表
                for source in sources:
                    para = doc.add_paragraph(f"• {source}")
                    para.paragraph_format.left_indent = Inches(0.5)
    
    def _add_metadata_footer(self, doc: Document, state: ReportState):
        """
        添加元数据页脚
        
        Args:
            doc: Word文档对象
            state: 报告状态
        """
        # 获取第一个节
        section = doc.sections[0]
        
        # 添加页脚
        footer = section.footer
        footer_para = footer.paragraphs[0]
        footer_para.text = f"生成时间：{state.created_at.strftime('%Y-%m-%d %H:%M:%S')} | 迭代次数：{state.current_iteration}"
        footer_para.alignment = WD_PARAGRAPH_ALIGNMENT.CENTER
        footer_para.runs[0].font.size = Pt(9)
        footer_para.runs[0].font.italic = True
