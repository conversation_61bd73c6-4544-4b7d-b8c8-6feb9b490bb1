#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
图片处理器模块
处理图片的读取、分析和嵌入
"""

import os
from pathlib import Path
from typing import List, Dict, Any, Optional


class ImageProcessor:
    """图片处理器"""
    
    def __init__(self, generator):
        self.generator = generator
        self.supported_formats = {'.jpg', '.jpeg', '.png', '.gif', '.bmp', '.tiff'}
    
    def find_images_in_data_sources(self, data_sources: List[str]) -> List[Dict[str, Any]]:
        """在数据源中查找图片"""
        images = []
        
        for data_source in data_sources:
            source_path = Path(data_source)
            
            if source_path.is_file():
                if source_path.suffix.lower() in self.supported_formats:
                    images.append(self._create_image_info(source_path))
            elif source_path.is_dir():
                for image_path in source_path.rglob('*'):
                    if (image_path.is_file() and 
                        image_path.suffix.lower() in self.supported_formats):
                        images.append(self._create_image_info(image_path))
        
        return images
    
    def _create_image_info(self, image_path: Path) -> Dict[str, Any]:
        """创建图片信息"""
        return {
            'path': str(image_path),
            'name': image_path.name,
            'size': image_path.stat().st_size,
            'format': image_path.suffix.lower(),
            'description': self._generate_image_description(image_path)
        }
    
    def _generate_image_description(self, image_path: Path) -> str:
        """生成图片描述"""
        # 基于文件名生成简单描述
        name = image_path.stem.lower()
        
        if 'chart' in name or '图表' in name:
            return "数据图表"
        elif 'graph' in name or '图形' in name:
            return "统计图形"
        elif 'flow' in name or '流程' in name:
            return "流程图"
        elif 'structure' in name or '结构' in name:
            return "结构图"
        elif 'market' in name or '市场' in name:
            return "市场分析图"
        elif 'trend' in name or '趋势' in name:
            return "趋势分析图"
        else:
            return "相关图片"
    
    def analyze_image_relevance(self, 
                               image_info: Dict[str, Any], 
                               section_title: str) -> float:
        """分析图片与章节的相关性"""
        image_name = image_info.get('name', '').lower()
        section_title_lower = section_title.lower()
        
        score = 0.0
        
        # 关键词匹配
        section_keywords = section_title_lower.split()
        for keyword in section_keywords:
            if keyword in image_name:
                score += 0.3
        
        # 特定主题匹配
        if '市场' in section_title_lower and 'market' in image_name:
            score += 0.4
        if '技术' in section_title_lower and 'tech' in image_name:
            score += 0.4
        if '竞争' in section_title_lower and 'competition' in image_name:
            score += 0.4
        if '趋势' in section_title_lower and 'trend' in image_name:
            score += 0.4
        
        return score
    
    def embed_images_in_framework(self, 
                                 framework: Dict[str, Any], 
                                 images: List[Dict[str, Any]]) -> Dict[str, Any]:
        """将图片嵌入到框架中"""
        self._embed_images_in_sections(framework.get('sections', []), images)
        return framework
    
    def _embed_images_in_sections(self, 
                                 sections: List[Dict[str, Any]], 
                                 images: List[Dict[str, Any]]):
        """递归地将图片嵌入到章节中"""
        for section in sections:
            section_title = section.get('title', '')
            
            # 为当前章节找到最相关的图片
            relevant_images = []
            for image in images:
                relevance = self.analyze_image_relevance(image, section_title)
                if relevance > 0.3:
                    image['relevance_score'] = relevance
                    relevant_images.append(image)
            
            # 按相关性排序并选择最佳图片
            relevant_images.sort(key=lambda x: x['relevance_score'], reverse=True)
            
            if relevant_images:
                section['embedded_images'] = relevant_images[:2]  # 最多嵌入2张图片
            
            # 递归处理子章节
            if 'children' in section:
                self._embed_images_in_sections(section['children'], images)
    
    def generate_image_content(self, image_info: Dict[str, Any]) -> str:
        """生成图片相关的内容"""
        name = image_info.get('name', '')
        description = image_info.get('description', '')
        
        content = f"\n\n### {description}\n\n"
        content += f"![{description}]({image_info.get('path', '')})\n\n"
        content += f"*图片说明: {description}*\n\n"
        
        return content
    
    def apply_image_embedding(self, framework: Dict[str, Any]) -> Dict[str, Any]:
        """应用图片嵌入到整个框架"""
        self._apply_embedding_to_sections(framework.get('sections', []))
        return framework
    
    def _apply_embedding_to_sections(self, sections: List[Dict[str, Any]]):
        """递归应用图片嵌入到所有章节"""
        for section in sections:
            if 'embedded_images' in section:
                original_content = section.get('content', '')
                image_content = ""
                
                for image in section['embedded_images']:
                    image_content += self.generate_image_content(image)
                
                # 将图片内容添加到章节末尾
                section['content'] = original_content + image_content
            
            # 递归处理子章节
            if 'children' in section:
                self._apply_embedding_to_sections(section['children'])
    
    def optimize_images_for_document(self, images: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """优化图片用于文档生成"""
        optimized_images = []
        
        for image in images:
            # 检查图片大小
            size = image.get('size', 0)
            if size > 5 * 1024 * 1024:  # 5MB
                print(f"⚠️ 图片过大，建议压缩: {image.get('name', '')}")
            
            # 检查格式
            format_ext = image.get('format', '')
            if format_ext in {'.jpg', '.jpeg', '.png'}:
                optimized_images.append(image)
            else:
                print(f"⚠️ 不支持的图片格式: {image.get('name', '')}")
        
        return optimized_images

    # ==================== 原代码中的复杂图片处理功能 ====================

    class ImageMatcher:
        """图片匹配器（完全按原代码实现）"""

        def __init__(self, generator):
            self.generator = generator

        def analyze_report_and_match_images(self, report_content, images_dir):
            """分析报告内容并匹配合适的图片"""
            print(f"🔍 开始分析报告内容并匹配图片...")

            try:
                from pathlib import Path
                images_path = Path(images_dir)
                if not images_path.exists():
                    print(f"❌ 图片目录不存在: {images_dir}")
                    return []

                # 获取所有图片文件
                image_files = self.get_image_files(images_path)
                if not image_files:
                    print(f"❌ 图片目录中没有找到图片文件: {images_dir}")
                    return []

                print(f"📊 找到 {len(image_files)} 个图片文件")

                # 将报告内容分段处理（避免token限制）
                segments = self.split_content_into_segments(report_content)
                print(f"📄 报告内容分为 {len(segments)} 个段落进行分析")

                all_matches = []

                # 为每个段落分析匹配的图片
                try:
                    from tqdm import tqdm
                    segment_pbar = tqdm(total=len(segments), desc="🧠 分析段落", unit="段", leave=False)
                except ImportError:
                    segment_pbar = None

                for i, segment in enumerate(segments):
                    if segment_pbar:
                        segment_pbar.set_description(f"🧠 段落 {i+1}/{len(segments)}")

                    try:
                        matches = self.analyze_segment_with_gemini(segment, image_files, i)
                        all_matches.extend(matches)
                    except Exception as e:
                        print(f"⚠️ 段落 {i+1} 分析失败: {str(e)}")

                    if segment_pbar:
                        segment_pbar.update(1)

                if segment_pbar:
                    segment_pbar.close()

                # 优化和去重匹配结果
                optimized_matches = self.optimize_matches(all_matches)

                print(f"✅ 图片匹配分析完成，找到 {len(optimized_matches)} 个高质量匹配")

                return optimized_matches

            except Exception as e:
                print(f"❌ 图片匹配分析失败: {str(e)}")
                return []

        def get_image_files(self, images_path):
            """获取图片文件列表"""
            image_extensions = {'.png', '.jpg', '.jpeg', '.gif', '.bmp', '.svg', '.webp', '.tiff'}
            image_files = []

            for file_path in images_path.rglob("*"):
                if file_path.suffix.lower() in image_extensions:
                    # 使用相对路径作为图片ID
                    relative_path = file_path.relative_to(images_path)
                    image_files.append(str(relative_path))

            return image_files

        def split_content_into_segments(self, content, max_length=3000):
            """将内容分割成段落"""
            segments = []
            lines = content.split('\n')
            current_segment = []
            current_length = 0

            for line in lines:
                line_length = len(line)

                if current_length + line_length > max_length and current_segment:
                    # 当前段落已满，开始新段落
                    segments.append('\n'.join(current_segment))
                    current_segment = [line]
                    current_length = line_length
                else:
                    current_segment.append(line)
                    current_length += line_length

            # 添加最后一个段落
            if current_segment:
                segments.append('\n'.join(current_segment))

            return segments

        def analyze_segment_with_gemini(self, segment, image_files, segment_index):
            """使用Gemini分析段落并匹配图片"""
            # 限制图片列表长度以避免token限制
            limited_images = image_files[:20]  # 每次最多分析20个图片

            prompt = f"""
作为专业的图片匹配专家，请分析以下报告段落内容，并从提供的图片列表中选择最相关的图片。

## 报告段落内容
{segment}

## 可用图片列表
{chr(10).join(f"- {img}" for img in limited_images)}

## 分析要求
请为每个相关的图片提供详细的匹配分析，包括：
1. 相关性评分（0-1，1为最相关）
2. 匹配理由
3. 建议的图片标题
4. 推荐程度（strongly_recommend/recommend/neutral/not_recommend）

请以JSON格式返回分析结果：

```json
{{
  "image_recommendations": [
    {{
      "image_id": "图片文件名",
      "relevance_score": 0.85,
      "content_match_score": 0.8,
      "visual_appeal_score": 0.9,
      "overall_score": 0.85,
      "match_reason": "详细的匹配理由",
      "caption_suggestion": "建议的图片标题",
      "recommendation": "strongly_recommend",
      "insert_position": "段落中的建议插入位置"
    }}
  ]
}}
```

只推荐真正相关且有价值的图片，避免无关匹配。
"""

            try:
                response = self.generator.call_orchestrator_model(prompt)
                return self.parse_gemini_response(response, segment_index)
            except Exception as e:
                print(f"⚠️ Gemini分析失败: {str(e)}")
                return []

        def parse_gemini_response(self, response, segment_index):
            """解析Gemini的JSON响应"""
            try:
                import json
                import re

                # 清理响应，移除可能的markdown标记
                cleaned_response = response.strip()
                if cleaned_response.startswith('```json'):
                    cleaned_response = cleaned_response[7:]
                if cleaned_response.endswith('```'):
                    cleaned_response = cleaned_response[:-3]

                # 提取JSON部分
                json_match = re.search(r'\{.*\}', cleaned_response, re.DOTALL)
                if json_match:
                    json_str = json_match.group()
                    result = json.loads(json_str)

                    matches = result.get('image_recommendations', [])

                    # 为每个匹配添加段落偏移信息
                    for match in matches:
                        match['segment_index'] = segment_index
                        # 确保所有必需字段存在
                        if 'overall_score' not in match:
                            match['overall_score'] = 0.5
                        if 'recommendation' not in match:
                            match['recommendation'] = 'neutral'

                    return matches
                else:
                    print(f"⚠️ 无法从响应中提取JSON格式")
                    return []

            except json.JSONDecodeError as e:
                print(f"⚠️ JSON解析失败: {str(e)}")
                print(f"响应内容: {response[:200]}...")
                return []
            except Exception as e:
                print(f"⚠️ 响应解析异常: {str(e)}")
                return []

        def optimize_matches(self, all_matches):
            """优化和去重匹配结果"""
            if not all_matches:
                return []

            # 按相关性得分排序
            sorted_matches = sorted(all_matches,
                                  key=lambda x: x.get('overall_score', 0),
                                  reverse=True)

            # 去除重复的图片匹配，保留得分最高的
            seen_images = {}
            optimized = []

            for match in sorted_matches:
                image_id = match.get('image_id')
                recommendation = match.get('recommendation', 'neutral')

                # 只保留推荐度较高的匹配
                if recommendation in ['strongly_recommend', 'recommend']:
                    if image_id not in seen_images:
                        seen_images[image_id] = match
                        optimized.append(match)
                    else:
                        # 如果已存在，比较得分，保留更高的
                        existing_score = seen_images[image_id].get('overall_score', 0)
                        current_score = match.get('overall_score', 0)
                        if current_score > existing_score:
                            # 替换为更高得分的匹配
                            optimized.remove(seen_images[image_id])
                            seen_images[image_id] = match
                            optimized.append(match)

            # 按得分重新排序
            optimized.sort(key=lambda x: x.get('overall_score', 0), reverse=True)

            return optimized
