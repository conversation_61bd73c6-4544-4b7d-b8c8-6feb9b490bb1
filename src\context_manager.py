"""
数据源上下文管理模块
负责构建和管理API请求的上下文内容
"""
from typing import List, Dict, Any, Optional
import json
from pathlib import Path

from .config import Config
from .data_ingestion import DataIngestion, ContentSnippet
from .logger import ProcessLogger
from .rate_limiter import TokenRateLimiter


class ContextManager:
    """
    数据源上下文管理类
    负责根据给定的数据源路径构建完整的上下文
    """
    
    def __init__(self, config: Config, logger: ProcessLogger):
        self.config = config
        self.logger = logger
        self.data_ingestion = DataIngestion(config, logger)
        self.rate_limiter = TokenRateLimiter(config, logger)
    
    def build_context_from_path(self, source_path: str) -> List[ContentSnippet]:
        """
        根据给定的数据源路径，读取所有文件并构建内容片段列表
        
        Args:
            source_path: 数据源文件夹路径
            
        Returns:
            内容片段列表
        """
        self.logger.log_node_start("build_context", source_path=source_path)
        
        try:
            snippets = self.data_ingestion.load_documents_as_snippets(source_path)
            
            self.logger.log_node_complete(
                "build_context", 
                duration=0,  # 实际使用时应计算耗时
                snippets_count=len(snippets)
            )
            
            return snippets
            
        except Exception as e:
            self.logger.log_node_error(
                "build_context",
                error=str(e),
                source_path=source_path
            )
            raise
    
    def format_for_gemini_api(self, snippets: List[ContentSnippet]) -> Dict[str, Any]:
        """
        将内容片段列表格式化为符合Gemini API多模态输入要求的请求体
        
        Args:
            snippets: 内容片段列表
            
        Returns:
            格式化后的API请求内容
        """
        parts = []
        
        for snippet in snippets:
            if snippet.content_type == 'text':
                # 文本内容
                part = {
                    "text": f"[来源: {snippet.metadata['source_file']}]\n{snippet.content}"
                }
                parts.append(part)
                
            elif snippet.content_type == 'image':
                # 图像内容（Base64编码）
                image_data = snippet.content
                part = {
                    "inline_data": {
                        "mime_type": f"image/{image_data['format']}",
                        "data": image_data['data']
                    }
                }
                parts.append(part)
                
                # 添加图像的元数据说明
                metadata_text = f"[图像来源: {snippet.metadata['source_file']}, 页码: {image_data.get('page', 'N/A')}]"
                parts.append({"text": metadata_text})
                
            elif snippet.content_type == 'table':
                # 表格内容（Markdown格式）
                table_metadata = []
                if 'sheet_name' in snippet.metadata:
                    table_metadata.append(f"工作表: {snippet.metadata['sheet_name']}")
                if 'table_index' in snippet.metadata:
                    table_metadata.append(f"表格索引: {snippet.metadata['table_index']}")
                
                metadata_str = ", ".join(table_metadata) if table_metadata else ""
                
                part = {
                    "text": f"[表格来源: {snippet.metadata['source_file']}{', ' + metadata_str if metadata_str else ''}]\n{snippet.content}"
                }
                parts.append(part)
        
        return {"parts": parts}
    
    def prepare_chapter_context(self, chapter_title: str, source_path: str) -> Dict[str, Any]:
        """
        为特定章节准备完整的上下文
        
        Args:
            chapter_title: 章节标题
            source_path: 该章节的专属数据源路径
            
        Returns:
            准备好的API请求内容
        """
        # 构建内容片段
        snippets = self.build_context_from_path(source_path)
        
        if not snippets:
            # 如果没有找到任何内容
            return {
                "parts": [
                    {"text": f"根据指定数据源（{source_path}），未找到相关信息。"}
                ]
            }
        
        # 限制片段数量以控制token使用
        limited_snippets = self._limit_snippets_by_tokens(snippets)
        
        # 格式化为API请求
        context = self.format_for_gemini_api(limited_snippets)
        
        # 添加章节相关的提示
        chapter_prompt = {
            "text": f"\n基于以上所有提供的文件内容，请为章节'{chapter_title}'撰写详细的分析内容。"
        }
        context["parts"].append(chapter_prompt)
        
        return context
    
    def calculate_context_size(self, snippets: List[ContentSnippet]) -> Dict[str, int]:
        """
        计算上下文的大小信息
        
        Args:
            snippets: 内容片段列表
            
        Returns:
            包含各种统计信息的字典
        """
        stats = {
            "total_snippets": len(snippets),
            "text_snippets": 0,
            "image_snippets": 0,
            "table_snippets": 0,
            "total_text_length": 0,
            "total_images_size": 0
        }
        
        for snippet in snippets:
            if snippet.content_type == 'text':
                stats["text_snippets"] += 1
                stats["total_text_length"] += len(snippet.content)
            elif snippet.content_type == 'image':
                stats["image_snippets"] += 1
                # Base64编码的图像数据长度
                stats["total_images_size"] += len(snippet.content.get('data', ''))
            elif snippet.content_type == 'table':
                stats["table_snippets"] += 1
                stats["total_text_length"] += len(snippet.content)
        
        return stats
    
    def _limit_snippets_by_tokens(self, snippets: List[ContentSnippet]) -> List[ContentSnippet]:
        """
        限制片段数量以确保不超过token限制
        
        Args:
            snippets: 原始内容片段列表
            
        Returns:
            限制后的内容片段列表
        """
        # 计算每个片段的token数
        snippet_tokens = []
        for snippet in snippets:
            if snippet.content_type == 'text':
                tokens = self.rate_limiter.estimate_tokens(snippet.content)
            elif snippet.content_type == 'image':
                tokens = 258  # 图像固定token数
            elif snippet.content_type == 'table':
                tokens = self.rate_limiter.estimate_tokens(snippet.content)
            else:
                tokens = 0
            
            snippet_tokens.append((snippet, tokens))
        
        # 按token数排序，优先保留较小的片段
        snippet_tokens.sort(key=lambda x: x[1])
        
        # 选择片段，确保总token数不超过限制的80%（留出缓冲）
        max_tokens = int(self.config.rate_limiting.max_tokens_per_request * 0.8)
        selected_snippets = []
        total_tokens = 0
        
        for snippet, tokens in snippet_tokens:
            if total_tokens + tokens <= max_tokens:
                selected_snippets.append(snippet)
                total_tokens += tokens
            else:
                # 如果加入这个片段会超过限制，停止添加
                break
        
        # 记录日志
        if len(selected_snippets) < len(snippets):
            self.logger.logger.warning(
                f"Token限制：从{len(snippets)}个片段中选择了{len(selected_snippets)}个，"
                f"总token数约{total_tokens}"
            )
        
        return selected_snippets
