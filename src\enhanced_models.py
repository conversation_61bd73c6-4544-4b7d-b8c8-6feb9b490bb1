"""
增强的统筹和执行模型
实现统筹-执行模型深度协作
"""
import time
import json
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime

from .state import ReportState, QualityIssue, QualityDimension, SeverityLevel
from .config import Config
from .logger import ProcessLogger
from .llm_client import LLMClient


class CoordinatorModel:
    """
    增强的统筹模型
    支持多轮迭代控制和质量检查指导
    """
    
    def __init__(self, config: Config, llm_client: LLMClient, logger: ProcessLogger):
        self.config = config
        self.llm_client = llm_client
        self.logger = logger
    
    def provide_iteration_guidance(
        self, 
        state: ReportState, 
        iteration_number: int
    ) -> Dict[str, Any]:
        """
        提供迭代指导
        
        Args:
            state: 报告状态
            iteration_number: 迭代轮次
            
        Returns:
            指导信息
        """
        self.logger.logger.info(f"统筹模型提供第 {iteration_number} 轮迭代指导")
        
        try:
            # 分析当前状态
            current_quality = state.get_current_quality_score()
            total_issues = state.get_total_quality_issues()
            
            # 构建指导提示
            prompt = f"""
作为报告生成的统筹模型，请为第 {iteration_number} 轮迭代提供指导。

当前状态：
- 报告主题：{state.topic}
- 当前质量分数：{current_quality:.3f}
- 发现的质量问题数：{total_issues}
- 迭代轮次：{iteration_number}/{state.max_iterations}

请提供以下指导：
1. 本轮迭代的重点关注领域
2. 质量改进的优先级建议
3. 执行模型的具体指导方针
4. 预期的质量提升目标

请以JSON格式返回：
{{
    "focus_areas": ["领域1", "领域2"],
    "priority_dimensions": ["维度1", "维度2"],
    "execution_guidelines": "具体指导",
    "quality_target": 0.85,
    "special_instructions": "特殊说明"
}}
"""
            
            guidance = self.llm_client.call_llm(
                prompt=prompt,
                model_type="orchestrator",
                response_format="json"
            )
            
            self.logger.logger.info(f"统筹模型指导生成完成")
            return guidance
            
        except Exception as e:
            self.logger.logger.error(f"统筹模型指导生成失败: {str(e)}")
            # 返回默认指导
            return {
                "focus_areas": ["content_quality", "logical_consistency"],
                "priority_dimensions": ["coherence", "consistency"],
                "execution_guidelines": "专注于提高内容质量和逻辑一致性",
                "quality_target": 0.8,
                "special_instructions": "使用默认指导策略"
            }
    
    def analyze_quality_issues(
        self, 
        state: ReportState
    ) -> Dict[str, Any]:
        """
        分析质量问题并提供改进策略
        
        Args:
            state: 报告状态
            
        Returns:
            质量分析结果
        """
        self.logger.logger.info("统筹模型分析质量问题")
        
        try:
            # 收集质量问题统计
            issue_stats = self._collect_quality_issue_statistics(state)
            
            # 构建分析提示
            prompt = f"""
作为报告质量分析专家，请分析以下质量问题统计：

问题统计：
{json.dumps(issue_stats, ensure_ascii=False, indent=2)}

请提供：
1. 主要质量问题的根因分析
2. 改进策略的优先级排序
3. 针对性的解决方案
4. 预期的改进效果评估

请以JSON格式返回：
{{
    "root_cause_analysis": "根因分析",
    "improvement_priorities": ["优先级1", "优先级2"],
    "targeted_solutions": {{"问题类型": "解决方案"}},
    "expected_improvement": 0.15
}}
"""
            
            analysis = self.llm_client.call_llm(
                prompt=prompt,
                model_type="orchestrator",
                response_format="json"
            )
            
            self.logger.logger.info("质量问题分析完成")
            return analysis
            
        except Exception as e:
            self.logger.logger.error(f"质量问题分析失败: {str(e)}")
            return {
                "root_cause_analysis": "分析失败",
                "improvement_priorities": ["coherence", "consistency"],
                "targeted_solutions": {},
                "expected_improvement": 0.1
            }
    
    def generate_correction_instructions(
        self, 
        quality_issues: List[QualityIssue],
        target_node: Dict[str, Any]
    ) -> str:
        """
        生成修正指令
        
        Args:
            quality_issues: 质量问题列表
            target_node: 目标节点
            
        Returns:
            修正指令
        """
        try:
            issues_text = "\n".join([
                f"- {issue.dimension.value}: {issue.description} (严重程度: {issue.severity.value})"
                for issue in quality_issues
            ])
            
            prompt = f"""
针对以下质量问题，为执行模型生成具体的修正指令：

目标节点：{target_node.get('title', 'Unknown')}
当前内容：{target_node.get('content', '')[:500]}...

质量问题：
{issues_text}

请生成详细的修正指令，包括：
1. 具体的修改要求
2. 保持不变的内容
3. 改进的重点方向
4. 质量标准要求

修正指令：
"""
            
            instructions = self.llm_client.call_llm(
                prompt=prompt,
                model_type="orchestrator",
                response_format="text"
            )
            
            return instructions.strip()
            
        except Exception as e:
            self.logger.logger.error(f"生成修正指令失败: {str(e)}")
            return "请根据质量问题进行相应的内容改进。"
    
    def _collect_quality_issue_statistics(self, state: ReportState) -> Dict[str, Any]:
        """收集质量问题统计"""
        stats = {
            "total_issues": 0,
            "by_severity": {level.value: 0 for level in SeverityLevel},
            "by_dimension": {dim.value: 0 for dim in QualityDimension},
            "by_level": {}
        }
        
        def collect_recursive(node: Dict[str, Any], level: int = 1):
            if "quality_issues" in node:
                issues = node["quality_issues"]
                stats["total_issues"] += len(issues)
                
                if level not in stats["by_level"]:
                    stats["by_level"][level] = 0
                stats["by_level"][level] += len(issues)
                
                for issue_dict in issues:
                    if isinstance(issue_dict, dict):
                        severity = issue_dict.get("severity", "low")
                        dimension = issue_dict.get("dimension", "coherence")
                        
                        if severity in stats["by_severity"]:
                            stats["by_severity"][severity] += 1
                        if dimension in stats["by_dimension"]:
                            stats["by_dimension"][dimension] += 1
            
            if "children" in node:
                for child in node["children"]:
                    collect_recursive(child, level + 1)
        
        if state.report_structure and "sections" in state.report_structure:
            for section in state.report_structure["sections"]:
                collect_recursive(section)
        
        return stats


class ExecutorModel:
    """
    增强的执行模型
    支持迭代修正和改进，基于统筹模型指导
    """
    
    def __init__(self, config: Config, llm_client: LLMClient, logger: ProcessLogger):
        self.config = config
        self.llm_client = llm_client
        self.logger = logger
    
    def execute_content_regeneration(
        self, 
        node: Dict[str, Any],
        guidance: Dict[str, Any],
        correction_instructions: str,
        context: Dict[str, Any]
    ) -> str:
        """
        基于统筹模型指导重新生成内容
        
        Args:
            node: 目标节点
            guidance: 统筹模型指导
            correction_instructions: 修正指令
            context: 上下文信息
            
        Returns:
            重新生成的内容
        """
        self.logger.logger.info(f"执行模型重新生成内容: {node.get('title', 'Unknown')}")
        
        try:
            original_content = node.get("content", "")
            
            prompt = f"""
作为内容执行模型，请根据统筹模型的指导重新生成内容。

原始内容：
{original_content}

统筹模型指导：
- 重点关注领域：{guidance.get('focus_areas', [])}
- 优先维度：{guidance.get('priority_dimensions', [])}
- 执行指导：{guidance.get('execution_guidelines', '')}
- 质量目标：{guidance.get('quality_target', 0.8)}

修正指令：
{correction_instructions}

特殊说明：{guidance.get('special_instructions', '')}

要求：
1. 严格按照统筹模型的指导进行改进
2. 保持所有引用标记[来源: xxx]不变
3. 确保内容质量达到目标分数
4. 保持原有信息的准确性

请生成改进后的内容：
"""
            
            regenerated_content = self.llm_client.call_llm(
                prompt=prompt,
                context=context,
                model_type="executor",
                response_format="text"
            )
            
            self.logger.logger.info("内容重新生成完成")
            return regenerated_content.strip()
            
        except Exception as e:
            self.logger.logger.error(f"内容重新生成失败: {str(e)}")
            return node.get("content", "")
    
    def perform_self_assessment(
        self, 
        generated_content: str,
        quality_target: float
    ) -> Dict[str, Any]:
        """
        执行模型自评估
        
        Args:
            generated_content: 生成的内容
            quality_target: 质量目标
            
        Returns:
            自评估结果
        """
        try:
            prompt = f"""
请对以下生成的内容进行自评估：

内容：
{generated_content}

质量目标：{quality_target}

请从以下维度评估（0-10分）：
1. 连贯性：内容逻辑是否清晰流畅
2. 一致性：表述是否统一一致
3. 准确性：信息是否准确可靠
4. 完整性：内容是否充实完整
5. 全面性：是否涵盖关键要点
6. 严谨性：表述是否专业严谨

请以JSON格式返回：
{{
    "coherence_score": 8.5,
    "consistency_score": 8.0,
    "accuracy_score": 9.0,
    "completeness_score": 7.5,
    "comprehensiveness_score": 8.0,
    "rigor_score": 8.5,
    "overall_assessment": "整体评价",
    "meets_target": true,
    "improvement_suggestions": ["建议1", "建议2"]
}}
"""
            
            assessment = self.llm_client.call_llm(
                prompt=prompt,
                model_type="executor",
                response_format="json"
            )
            
            self.logger.logger.info("执行模型自评估完成")
            return assessment
            
        except Exception as e:
            self.logger.logger.error(f"执行模型自评估失败: {str(e)}")
            return {
                "coherence_score": 7.0,
                "consistency_score": 7.0,
                "accuracy_score": 7.0,
                "completeness_score": 7.0,
                "comprehensiveness_score": 7.0,
                "rigor_score": 7.0,
                "overall_assessment": "评估失败",
                "meets_target": False,
                "improvement_suggestions": []
            }
    
    def provide_status_feedback(
        self, 
        node: Dict[str, Any],
        execution_result: str,
        assessment: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        提供状态反馈给统筹模型
        
        Args:
            node: 处理的节点
            execution_result: 执行结果
            assessment: 自评估结果
            
        Returns:
            状态反馈
        """
        feedback = {
            "node_title": node.get("title", "Unknown"),
            "node_level": node.get("level", 1),
            "execution_status": "completed" if execution_result else "failed",
            "content_length": len(execution_result),
            "quality_scores": {
                "coherence": assessment.get("coherence_score", 0) / 10,
                "consistency": assessment.get("consistency_score", 0) / 10,
                "accuracy": assessment.get("accuracy_score", 0) / 10,
                "completeness": assessment.get("completeness_score", 0) / 10,
                "comprehensiveness": assessment.get("comprehensiveness_score", 0) / 10,
                "rigor": assessment.get("rigor_score", 0) / 10
            },
            "meets_quality_target": assessment.get("meets_target", False),
            "improvement_suggestions": assessment.get("improvement_suggestions", []),
            "timestamp": datetime.now().isoformat()
        }
        
        self.logger.logger.info(f"执行模型反馈: {node.get('title', 'Unknown')} - 状态: {feedback['execution_status']}")
        
        return feedback


class ModelCommunicationProtocol:
    """
    模型间通信协议
    实现统筹模型和执行模型间的深度交互
    """
    
    def __init__(self, config: Config, logger: ProcessLogger):
        self.config = config
        self.logger = logger
        self.communication_log = []
    
    def coordinate_iteration_cycle(
        self,
        coordinator: CoordinatorModel,
        executor: ExecutorModel,
        state: ReportState,
        iteration_number: int
    ) -> Dict[str, Any]:
        """
        协调一轮完整的迭代周期
        
        Args:
            coordinator: 统筹模型
            executor: 执行模型
            state: 报告状态
            iteration_number: 迭代轮次
            
        Returns:
            迭代结果
        """
        self.logger.logger.info(f"开始协调第 {iteration_number} 轮迭代")
        
        cycle_result = {
            "iteration_number": iteration_number,
            "start_time": datetime.now(),
            "guidance_provided": False,
            "nodes_processed": 0,
            "successful_improvements": 0,
            "communication_events": []
        }
        
        try:
            # 1. 统筹模型提供指导
            guidance = coordinator.provide_iteration_guidance(state, iteration_number)
            cycle_result["guidance_provided"] = True
            self._log_communication("coordinator_guidance", guidance)
            
            # 2. 统筹模型分析质量问题
            quality_analysis = coordinator.analyze_quality_issues(state)
            self._log_communication("quality_analysis", quality_analysis)
            
            # 3. 处理需要改进的节点
            nodes_to_improve = self._identify_nodes_for_improvement(state)
            cycle_result["nodes_processed"] = len(nodes_to_improve)
            
            for node, issues in nodes_to_improve:
                # 统筹模型生成修正指令
                correction_instructions = coordinator.generate_correction_instructions(issues, node)
                
                # 执行模型重新生成内容
                context = self._build_node_context(node, state)
                regenerated_content = executor.execute_content_regeneration(
                    node, guidance, correction_instructions, context
                )
                
                # 执行模型自评估
                assessment = executor.perform_self_assessment(
                    regenerated_content, guidance.get("quality_target", 0.8)
                )
                
                # 更新节点内容
                if regenerated_content and assessment.get("meets_target", False):
                    node["content"] = regenerated_content
                    cycle_result["successful_improvements"] += 1
                
                # 执行模型提供反馈
                feedback = executor.provide_status_feedback(node, regenerated_content, assessment)
                self._log_communication("executor_feedback", feedback)
            
            cycle_result["end_time"] = datetime.now()
            cycle_result["success"] = True
            
            self.logger.logger.info(
                f"第 {iteration_number} 轮迭代协调完成，"
                f"成功改进 {cycle_result['successful_improvements']}/{cycle_result['nodes_processed']} 个节点"
            )
            
        except Exception as e:
            cycle_result["success"] = False
            cycle_result["error"] = str(e)
            self.logger.logger.error(f"迭代协调失败: {str(e)}")
        
        return cycle_result
    
    def _identify_nodes_for_improvement(self, state: ReportState) -> List[Tuple[Dict[str, Any], List[QualityIssue]]]:
        """识别需要改进的节点"""
        nodes_to_improve = []
        
        def collect_recursive(node: Dict[str, Any]):
            if "quality_issues" in node and node["quality_issues"]:
                issues = [QualityIssue(**issue_dict) for issue_dict in node["quality_issues"] 
                         if isinstance(issue_dict, dict)]
                if issues:
                    nodes_to_improve.append((node, issues))
            
            if "children" in node:
                for child in node["children"]:
                    collect_recursive(child)
        
        if state.report_structure and "sections" in state.report_structure:
            for section in state.report_structure["sections"]:
                collect_recursive(section)
        
        return nodes_to_improve
    
    def _build_node_context(self, node: Dict[str, Any], state: ReportState) -> Dict[str, Any]:
        """构建节点上下文"""
        # 这里应该实现完整的上下文构建逻辑
        # 暂时返回基本上下文
        return {"parts": [{"text": f"节点标题: {node.get('title', '')}"}]}
    
    def _log_communication(self, event_type: str, data: Any):
        """记录通信事件"""
        event = {
            "timestamp": datetime.now().isoformat(),
            "event_type": event_type,
            "data": data
        }
        self.communication_log.append(event)
        
        self.logger.logger.debug(f"通信事件记录: {event_type}")
    
    def get_communication_summary(self) -> Dict[str, Any]:
        """获取通信摘要"""
        return {
            "total_events": len(self.communication_log),
            "event_types": list(set(event["event_type"] for event in self.communication_log)),
            "latest_events": self.communication_log[-5:] if self.communication_log else []
        }
