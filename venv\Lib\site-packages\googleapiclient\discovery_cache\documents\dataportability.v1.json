{"auth": {"oauth2": {"scopes": {"https://www.googleapis.com/auth/dataportability.alerts.subscriptions": {"description": "Move a copy of the Google Alerts subscriptions you created"}, "https://www.googleapis.com/auth/dataportability.businessmessaging.conversations": {"description": "Move a copy of messages between you and the businesses you have conversations with across Google services"}, "https://www.googleapis.com/auth/dataportability.chrome.autofill": {"description": "Move a copy of the information you entered into online forms in Chrome"}, "https://www.googleapis.com/auth/dataportability.chrome.bookmarks": {"description": "Move a copy of pages you bookmarked in Chrome"}, "https://www.googleapis.com/auth/dataportability.chrome.dictionary": {"description": "Move a copy of words you added to <PERSON><PERSON>'s dictionary"}, "https://www.googleapis.com/auth/dataportability.chrome.extensions": {"description": "Move a copy of extensions you installed from the Chrome Web Store"}, "https://www.googleapis.com/auth/dataportability.chrome.history": {"description": "Move a copy of sites you visited in Chrome"}, "https://www.googleapis.com/auth/dataportability.chrome.reading_list": {"description": "Move a copy of pages you added to your reading list in Chrome"}, "https://www.googleapis.com/auth/dataportability.chrome.settings": {"description": "Move a copy of your settings in Chrome"}, "https://www.googleapis.com/auth/dataportability.discover.follows": {"description": "Move a copy of searches and sites you follow, saved by Discover"}, "https://www.googleapis.com/auth/dataportability.discover.likes": {"description": "Move a copy of links to your liked documents, saved by Discover"}, "https://www.googleapis.com/auth/dataportability.discover.not_interested": {"description": "Move a copy of content you marked as not interested, saved by Discover"}, "https://www.googleapis.com/auth/dataportability.maps.aliased_places": {"description": "Move a copy of the places you labeled on Maps"}, "https://www.googleapis.com/auth/dataportability.maps.commute_routes": {"description": "Move a copy of your pinned trips on Maps"}, "https://www.googleapis.com/auth/dataportability.maps.commute_settings": {"description": "Move a copy of your commute settings on Maps"}, "https://www.googleapis.com/auth/dataportability.maps.ev_profile": {"description": "Move a copy of your electric vehicle profile on Maps"}, "https://www.googleapis.com/auth/dataportability.maps.factual_contributions": {"description": "Move a copy of the corrections you made to places or map information on Maps"}, "https://www.googleapis.com/auth/dataportability.maps.offering_contributions": {"description": "Move a copy of your updates to places on Maps"}, "https://www.googleapis.com/auth/dataportability.maps.photos_videos": {"description": "Move a copy of the photos and videos you posted on Maps"}, "https://www.googleapis.com/auth/dataportability.maps.questions_answers": {"description": "Move a copy of the questions and answers you posted on Maps"}, "https://www.googleapis.com/auth/dataportability.maps.reviews": {"description": "Move a copy of your reviews and posts on Maps"}, "https://www.googleapis.com/auth/dataportability.maps.starred_places": {"description": "Move a copy of your Starred places list on Maps"}, "https://www.googleapis.com/auth/dataportability.maps.vehicle_profile": {"description": "Move a copy of your vehicle profile on Maps"}, "https://www.googleapis.com/auth/dataportability.myactivity.maps": {"description": "Move a copy of your Maps activity"}, "https://www.googleapis.com/auth/dataportability.myactivity.myadcenter": {"description": "Move a copy of your My Ad Center activity"}, "https://www.googleapis.com/auth/dataportability.myactivity.play": {"description": "Move a copy of your Google Play activity"}, "https://www.googleapis.com/auth/dataportability.myactivity.search": {"description": "Move a copy of your Google Search activity"}, "https://www.googleapis.com/auth/dataportability.myactivity.shopping": {"description": "Move a copy of your Shopping activity"}, "https://www.googleapis.com/auth/dataportability.myactivity.youtube": {"description": "Move a copy of your YouTube activity"}, "https://www.googleapis.com/auth/dataportability.mymaps.maps": {"description": "Move a copy of the maps you created in My Maps"}, "https://www.googleapis.com/auth/dataportability.order_reserve.purchases_reservations": {"description": "Move a copy of your food purchase and reservation activity"}, "https://www.googleapis.com/auth/dataportability.play.devices": {"description": "Move a copy of information about your devices with Google Play Store installed"}, "https://www.googleapis.com/auth/dataportability.play.grouping": {"description": "Move a copy of your Google Play Store Grouping tags created by app developers"}, "https://www.googleapis.com/auth/dataportability.play.installs": {"description": "Move a copy of your Google Play Store app installations"}, "https://www.googleapis.com/auth/dataportability.play.library": {"description": "Move a copy of your Google Play Store downloads, including books, games, and apps"}, "https://www.googleapis.com/auth/dataportability.play.playpoints": {"description": "Move a copy of information about your Google Play Store Points"}, "https://www.googleapis.com/auth/dataportability.play.promotions": {"description": "Move a copy of information about your Google Play Store promotions"}, "https://www.googleapis.com/auth/dataportability.play.purchases": {"description": "Move a copy of your Google Play Store purchases"}, "https://www.googleapis.com/auth/dataportability.play.redemptions": {"description": "Move a copy of your Google Play Store redemption activities"}, "https://www.googleapis.com/auth/dataportability.play.subscriptions": {"description": "Move a copy of your Google Play Store subscriptions"}, "https://www.googleapis.com/auth/dataportability.play.usersettings": {"description": "Move a copy of your Google Play Store user settings and preferences"}, "https://www.googleapis.com/auth/dataportability.saved.collections": {"description": "Move a copy of your saved links, images, places, and collections from your use of Google services"}, "https://www.googleapis.com/auth/dataportability.search_ugc.comments": {"description": "Move a copy of your comments on Google Search"}, "https://www.googleapis.com/auth/dataportability.search_ugc.media.reviews_and_stars": {"description": "Move a copy of your media reviews on Google Search"}, "https://www.googleapis.com/auth/dataportability.search_ugc.media.streaming_video_providers": {"description": "Move a copy of your self-reported video streaming provider preferences from Google Search and Google TV"}, "https://www.googleapis.com/auth/dataportability.search_ugc.media.thumbs": {"description": "Move a copy of your indicated thumbs up and thumbs down on media in Google Search and Google TV"}, "https://www.googleapis.com/auth/dataportability.search_ugc.media.watched": {"description": "Move a copy of information about the movies and TV shows you marked as watched on Google Search and Google TV"}, "https://www.googleapis.com/auth/dataportability.searchnotifications.settings": {"description": "Move a copy of your notification settings on the Google Search app"}, "https://www.googleapis.com/auth/dataportability.searchnotifications.subscriptions": {"description": "Move a copy of your notification subscriptions on Google Search app"}, "https://www.googleapis.com/auth/dataportability.shopping.addresses": {"description": "Move a copy of your shipping information on Shopping"}, "https://www.googleapis.com/auth/dataportability.shopping.reviews": {"description": "Move a copy of reviews you wrote about products or online stores on Google Search"}, "https://www.googleapis.com/auth/dataportability.streetview.imagery": {"description": "Move a copy of the images and videos you uploaded to Street View"}, "https://www.googleapis.com/auth/dataportability.youtube.channel": {"description": "Move a copy of information about your YouTube channel"}, "https://www.googleapis.com/auth/dataportability.youtube.clips": {"description": "Move a copy of your YouTube clips metadata"}, "https://www.googleapis.com/auth/dataportability.youtube.comments": {"description": "Move a copy of your YouTube comments"}, "https://www.googleapis.com/auth/dataportability.youtube.live_chat": {"description": "Move a copy of your YouTube messages in live chat"}, "https://www.googleapis.com/auth/dataportability.youtube.music": {"description": "Move a copy of your uploaded YouTube music tracks and your YouTube music library"}, "https://www.googleapis.com/auth/dataportability.youtube.playable": {"description": "Move a copy of your YouTube playables saved game progress files"}, "https://www.googleapis.com/auth/dataportability.youtube.posts": {"description": "Move a copy of your YouTube posts"}, "https://www.googleapis.com/auth/dataportability.youtube.private_playlists": {"description": "Move a copy of your YouTube private playlists"}, "https://www.googleapis.com/auth/dataportability.youtube.private_videos": {"description": "Move a copy of your private YouTube videos and information about them"}, "https://www.googleapis.com/auth/dataportability.youtube.public_playlists": {"description": "Move a copy of your public YouTube playlists"}, "https://www.googleapis.com/auth/dataportability.youtube.public_videos": {"description": "Move a copy of your public YouTube videos and information about them"}, "https://www.googleapis.com/auth/dataportability.youtube.shopping": {"description": "Move a copy of your YouTube shopping wishlists, and wishlist items"}, "https://www.googleapis.com/auth/dataportability.youtube.subscriptions": {"description": "Move a copy of your YouTube channel subscriptions, even if they're private"}, "https://www.googleapis.com/auth/dataportability.youtube.unlisted_playlists": {"description": "Move a copy of your unlisted YouTube playlists"}, "https://www.googleapis.com/auth/dataportability.youtube.unlisted_videos": {"description": "Move a copy of your unlisted YouTube videos and information about them"}}}}, "basePath": "", "baseUrl": "https://dataportability.googleapis.com/", "batchPath": "batch", "canonicalName": "Data Portability", "description": "The Data Portability API lets you build applications that request authorization from a user to move a copy of data from Google services into your application. This enables data portability and facilitates switching services.", "discoveryVersion": "v1", "documentationLink": "https://developers.google.com/data-portability", "fullyEncodeReservedExpansion": true, "icons": {"x16": "http://www.google.com/images/icons/product/search-16.gif", "x32": "http://www.google.com/images/icons/product/search-32.gif"}, "id": "dataportability:v1", "kind": "discovery#restDescription", "mtlsRootUrl": "https://dataportability.mtls.googleapis.com/", "name": "dataportability", "ownerDomain": "google.com", "ownerName": "Google", "parameters": {"$.xgafv": {"description": "V1 error format.", "enum": ["1", "2"], "enumDescriptions": ["v1 error format", "v2 error format"], "location": "query", "type": "string"}, "access_token": {"description": "OAuth access token.", "location": "query", "type": "string"}, "alt": {"default": "json", "description": "Data format for response.", "enum": ["json", "media", "proto"], "enumDescriptions": ["Responses with Content-Type of application/json", "Media download with context-dependent Content-Type", "Responses with Content-Type of application/x-protobuf"], "location": "query", "type": "string"}, "callback": {"description": "JSONP", "location": "query", "type": "string"}, "fields": {"description": "Selector specifying which fields to include in a partial response.", "location": "query", "type": "string"}, "key": {"description": "API key. Your API key identifies your project and provides you with API access, quota, and reports. Required unless you provide an OAuth 2.0 token.", "location": "query", "type": "string"}, "oauth_token": {"description": "OAuth 2.0 token for the current user.", "location": "query", "type": "string"}, "prettyPrint": {"default": "true", "description": "Returns response with indentations and line breaks.", "location": "query", "type": "boolean"}, "quotaUser": {"description": "Available to use for quota purposes for server-side applications. Can be any arbitrary string assigned to a user, but should not exceed 40 characters.", "location": "query", "type": "string"}, "uploadType": {"description": "Legacy upload protocol for media (e.g. \"media\", \"multipart\").", "location": "query", "type": "string"}, "upload_protocol": {"description": "Upload protocol for media (e.g. \"raw\", \"multipart\").", "location": "query", "type": "string"}}, "protocol": "rest", "resources": {"accessType": {"methods": {"check": {"description": "Gets the access type of the token.", "flatPath": "v1/accessType:check", "httpMethod": "POST", "id": "dataportability.accessType.check", "parameterOrder": [], "parameters": {}, "path": "v1/accessType:check", "request": {"$ref": "CheckAccessTypeRequest"}, "response": {"$ref": "CheckAccessTypeResponse"}, "scopes": ["https://www.googleapis.com/auth/dataportability.alerts.subscriptions", "https://www.googleapis.com/auth/dataportability.businessmessaging.conversations", "https://www.googleapis.com/auth/dataportability.chrome.autofill", "https://www.googleapis.com/auth/dataportability.chrome.bookmarks", "https://www.googleapis.com/auth/dataportability.chrome.dictionary", "https://www.googleapis.com/auth/dataportability.chrome.extensions", "https://www.googleapis.com/auth/dataportability.chrome.history", "https://www.googleapis.com/auth/dataportability.chrome.reading_list", "https://www.googleapis.com/auth/dataportability.chrome.settings", "https://www.googleapis.com/auth/dataportability.discover.follows", "https://www.googleapis.com/auth/dataportability.discover.likes", "https://www.googleapis.com/auth/dataportability.discover.not_interested", "https://www.googleapis.com/auth/dataportability.maps.aliased_places", "https://www.googleapis.com/auth/dataportability.maps.commute_routes", "https://www.googleapis.com/auth/dataportability.maps.commute_settings", "https://www.googleapis.com/auth/dataportability.maps.ev_profile", "https://www.googleapis.com/auth/dataportability.maps.factual_contributions", "https://www.googleapis.com/auth/dataportability.maps.offering_contributions", "https://www.googleapis.com/auth/dataportability.maps.photos_videos", "https://www.googleapis.com/auth/dataportability.maps.questions_answers", "https://www.googleapis.com/auth/dataportability.maps.reviews", "https://www.googleapis.com/auth/dataportability.maps.starred_places", "https://www.googleapis.com/auth/dataportability.maps.vehicle_profile", "https://www.googleapis.com/auth/dataportability.myactivity.maps", "https://www.googleapis.com/auth/dataportability.myactivity.myadcenter", "https://www.googleapis.com/auth/dataportability.myactivity.play", "https://www.googleapis.com/auth/dataportability.myactivity.search", "https://www.googleapis.com/auth/dataportability.myactivity.shopping", "https://www.googleapis.com/auth/dataportability.myactivity.youtube", "https://www.googleapis.com/auth/dataportability.mymaps.maps", "https://www.googleapis.com/auth/dataportability.order_reserve.purchases_reservations", "https://www.googleapis.com/auth/dataportability.play.devices", "https://www.googleapis.com/auth/dataportability.play.grouping", "https://www.googleapis.com/auth/dataportability.play.installs", "https://www.googleapis.com/auth/dataportability.play.library", "https://www.googleapis.com/auth/dataportability.play.playpoints", "https://www.googleapis.com/auth/dataportability.play.promotions", "https://www.googleapis.com/auth/dataportability.play.purchases", "https://www.googleapis.com/auth/dataportability.play.redemptions", "https://www.googleapis.com/auth/dataportability.play.subscriptions", "https://www.googleapis.com/auth/dataportability.play.usersettings", "https://www.googleapis.com/auth/dataportability.saved.collections", "https://www.googleapis.com/auth/dataportability.search_ugc.comments", "https://www.googleapis.com/auth/dataportability.search_ugc.media.reviews_and_stars", "https://www.googleapis.com/auth/dataportability.search_ugc.media.streaming_video_providers", "https://www.googleapis.com/auth/dataportability.search_ugc.media.thumbs", "https://www.googleapis.com/auth/dataportability.search_ugc.media.watched", "https://www.googleapis.com/auth/dataportability.searchnotifications.settings", "https://www.googleapis.com/auth/dataportability.searchnotifications.subscriptions", "https://www.googleapis.com/auth/dataportability.shopping.addresses", "https://www.googleapis.com/auth/dataportability.shopping.reviews", "https://www.googleapis.com/auth/dataportability.streetview.imagery", "https://www.googleapis.com/auth/dataportability.youtube.channel", "https://www.googleapis.com/auth/dataportability.youtube.clips", "https://www.googleapis.com/auth/dataportability.youtube.comments", "https://www.googleapis.com/auth/dataportability.youtube.live_chat", "https://www.googleapis.com/auth/dataportability.youtube.music", "https://www.googleapis.com/auth/dataportability.youtube.playable", "https://www.googleapis.com/auth/dataportability.youtube.posts", "https://www.googleapis.com/auth/dataportability.youtube.private_playlists", "https://www.googleapis.com/auth/dataportability.youtube.private_videos", "https://www.googleapis.com/auth/dataportability.youtube.public_playlists", "https://www.googleapis.com/auth/dataportability.youtube.public_videos", "https://www.googleapis.com/auth/dataportability.youtube.shopping", "https://www.googleapis.com/auth/dataportability.youtube.subscriptions", "https://www.googleapis.com/auth/dataportability.youtube.unlisted_playlists", "https://www.googleapis.com/auth/dataportability.youtube.unlisted_videos"]}}}, "archiveJobs": {"methods": {"cancel": {"description": "Cancels a Portability Archive job.", "flatPath": "v1/archiveJobs/{archiveJobsId}:cancel", "httpMethod": "POST", "id": "dataportability.archiveJobs.cancel", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The Archive job ID you're canceling. This is returned by the InitiatePortabilityArchive response. The format is: archiveJobs/{archive_job}. Canceling is only executed if the job is in progress.", "location": "path", "pattern": "^archiveJobs/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}:cancel", "request": {"$ref": "CancelPortabilityArchiveRequest"}, "response": {"$ref": "CancelPortabilityArchiveResponse"}, "scopes": ["https://www.googleapis.com/auth/dataportability.alerts.subscriptions", "https://www.googleapis.com/auth/dataportability.businessmessaging.conversations", "https://www.googleapis.com/auth/dataportability.chrome.autofill", "https://www.googleapis.com/auth/dataportability.chrome.bookmarks", "https://www.googleapis.com/auth/dataportability.chrome.dictionary", "https://www.googleapis.com/auth/dataportability.chrome.extensions", "https://www.googleapis.com/auth/dataportability.chrome.history", "https://www.googleapis.com/auth/dataportability.chrome.reading_list", "https://www.googleapis.com/auth/dataportability.chrome.settings", "https://www.googleapis.com/auth/dataportability.discover.follows", "https://www.googleapis.com/auth/dataportability.discover.likes", "https://www.googleapis.com/auth/dataportability.discover.not_interested", "https://www.googleapis.com/auth/dataportability.maps.aliased_places", "https://www.googleapis.com/auth/dataportability.maps.commute_routes", "https://www.googleapis.com/auth/dataportability.maps.commute_settings", "https://www.googleapis.com/auth/dataportability.maps.ev_profile", "https://www.googleapis.com/auth/dataportability.maps.factual_contributions", "https://www.googleapis.com/auth/dataportability.maps.offering_contributions", "https://www.googleapis.com/auth/dataportability.maps.photos_videos", "https://www.googleapis.com/auth/dataportability.maps.questions_answers", "https://www.googleapis.com/auth/dataportability.maps.reviews", "https://www.googleapis.com/auth/dataportability.maps.starred_places", "https://www.googleapis.com/auth/dataportability.maps.vehicle_profile", "https://www.googleapis.com/auth/dataportability.myactivity.maps", "https://www.googleapis.com/auth/dataportability.myactivity.myadcenter", "https://www.googleapis.com/auth/dataportability.myactivity.play", "https://www.googleapis.com/auth/dataportability.myactivity.search", "https://www.googleapis.com/auth/dataportability.myactivity.shopping", "https://www.googleapis.com/auth/dataportability.myactivity.youtube", "https://www.googleapis.com/auth/dataportability.mymaps.maps", "https://www.googleapis.com/auth/dataportability.order_reserve.purchases_reservations", "https://www.googleapis.com/auth/dataportability.play.devices", "https://www.googleapis.com/auth/dataportability.play.grouping", "https://www.googleapis.com/auth/dataportability.play.installs", "https://www.googleapis.com/auth/dataportability.play.library", "https://www.googleapis.com/auth/dataportability.play.playpoints", "https://www.googleapis.com/auth/dataportability.play.promotions", "https://www.googleapis.com/auth/dataportability.play.purchases", "https://www.googleapis.com/auth/dataportability.play.redemptions", "https://www.googleapis.com/auth/dataportability.play.subscriptions", "https://www.googleapis.com/auth/dataportability.play.usersettings", "https://www.googleapis.com/auth/dataportability.saved.collections", "https://www.googleapis.com/auth/dataportability.search_ugc.comments", "https://www.googleapis.com/auth/dataportability.search_ugc.media.reviews_and_stars", "https://www.googleapis.com/auth/dataportability.search_ugc.media.streaming_video_providers", "https://www.googleapis.com/auth/dataportability.search_ugc.media.thumbs", "https://www.googleapis.com/auth/dataportability.search_ugc.media.watched", "https://www.googleapis.com/auth/dataportability.searchnotifications.settings", "https://www.googleapis.com/auth/dataportability.searchnotifications.subscriptions", "https://www.googleapis.com/auth/dataportability.shopping.addresses", "https://www.googleapis.com/auth/dataportability.shopping.reviews", "https://www.googleapis.com/auth/dataportability.streetview.imagery", "https://www.googleapis.com/auth/dataportability.youtube.channel", "https://www.googleapis.com/auth/dataportability.youtube.clips", "https://www.googleapis.com/auth/dataportability.youtube.comments", "https://www.googleapis.com/auth/dataportability.youtube.live_chat", "https://www.googleapis.com/auth/dataportability.youtube.music", "https://www.googleapis.com/auth/dataportability.youtube.playable", "https://www.googleapis.com/auth/dataportability.youtube.posts", "https://www.googleapis.com/auth/dataportability.youtube.private_playlists", "https://www.googleapis.com/auth/dataportability.youtube.private_videos", "https://www.googleapis.com/auth/dataportability.youtube.public_playlists", "https://www.googleapis.com/auth/dataportability.youtube.public_videos", "https://www.googleapis.com/auth/dataportability.youtube.shopping", "https://www.googleapis.com/auth/dataportability.youtube.subscriptions", "https://www.googleapis.com/auth/dataportability.youtube.unlisted_playlists", "https://www.googleapis.com/auth/dataportability.youtube.unlisted_videos"]}, "getPortabilityArchiveState": {"description": "Retrieves the state of an Archive job for the Portability API.", "flatPath": "v1/archiveJobs/{archiveJobsId}/portabilityArchiveState", "httpMethod": "GET", "id": "dataportability.archiveJobs.getPortabilityArchiveState", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The archive job ID that is returned when you request the state of the job. The format is: archiveJobs/{archive_job}/portabilityArchiveState. archive_job is the job ID returned by the InitiatePortabilityArchiveResponse.", "location": "path", "pattern": "^archiveJobs/[^/]+/portabilityArchiveState$", "required": true, "type": "string"}}, "path": "v1/{+name}", "response": {"$ref": "PortabilityArchiveState"}, "scopes": ["https://www.googleapis.com/auth/dataportability.alerts.subscriptions", "https://www.googleapis.com/auth/dataportability.businessmessaging.conversations", "https://www.googleapis.com/auth/dataportability.chrome.autofill", "https://www.googleapis.com/auth/dataportability.chrome.bookmarks", "https://www.googleapis.com/auth/dataportability.chrome.dictionary", "https://www.googleapis.com/auth/dataportability.chrome.extensions", "https://www.googleapis.com/auth/dataportability.chrome.history", "https://www.googleapis.com/auth/dataportability.chrome.reading_list", "https://www.googleapis.com/auth/dataportability.chrome.settings", "https://www.googleapis.com/auth/dataportability.discover.follows", "https://www.googleapis.com/auth/dataportability.discover.likes", "https://www.googleapis.com/auth/dataportability.discover.not_interested", "https://www.googleapis.com/auth/dataportability.maps.aliased_places", "https://www.googleapis.com/auth/dataportability.maps.commute_routes", "https://www.googleapis.com/auth/dataportability.maps.commute_settings", "https://www.googleapis.com/auth/dataportability.maps.ev_profile", "https://www.googleapis.com/auth/dataportability.maps.factual_contributions", "https://www.googleapis.com/auth/dataportability.maps.offering_contributions", "https://www.googleapis.com/auth/dataportability.maps.photos_videos", "https://www.googleapis.com/auth/dataportability.maps.questions_answers", "https://www.googleapis.com/auth/dataportability.maps.reviews", "https://www.googleapis.com/auth/dataportability.maps.starred_places", "https://www.googleapis.com/auth/dataportability.maps.vehicle_profile", "https://www.googleapis.com/auth/dataportability.myactivity.maps", "https://www.googleapis.com/auth/dataportability.myactivity.myadcenter", "https://www.googleapis.com/auth/dataportability.myactivity.play", "https://www.googleapis.com/auth/dataportability.myactivity.search", "https://www.googleapis.com/auth/dataportability.myactivity.shopping", "https://www.googleapis.com/auth/dataportability.myactivity.youtube", "https://www.googleapis.com/auth/dataportability.mymaps.maps", "https://www.googleapis.com/auth/dataportability.order_reserve.purchases_reservations", "https://www.googleapis.com/auth/dataportability.play.devices", "https://www.googleapis.com/auth/dataportability.play.grouping", "https://www.googleapis.com/auth/dataportability.play.installs", "https://www.googleapis.com/auth/dataportability.play.library", "https://www.googleapis.com/auth/dataportability.play.playpoints", "https://www.googleapis.com/auth/dataportability.play.promotions", "https://www.googleapis.com/auth/dataportability.play.purchases", "https://www.googleapis.com/auth/dataportability.play.redemptions", "https://www.googleapis.com/auth/dataportability.play.subscriptions", "https://www.googleapis.com/auth/dataportability.play.usersettings", "https://www.googleapis.com/auth/dataportability.saved.collections", "https://www.googleapis.com/auth/dataportability.search_ugc.comments", "https://www.googleapis.com/auth/dataportability.search_ugc.media.reviews_and_stars", "https://www.googleapis.com/auth/dataportability.search_ugc.media.streaming_video_providers", "https://www.googleapis.com/auth/dataportability.search_ugc.media.thumbs", "https://www.googleapis.com/auth/dataportability.search_ugc.media.watched", "https://www.googleapis.com/auth/dataportability.searchnotifications.settings", "https://www.googleapis.com/auth/dataportability.searchnotifications.subscriptions", "https://www.googleapis.com/auth/dataportability.shopping.addresses", "https://www.googleapis.com/auth/dataportability.shopping.reviews", "https://www.googleapis.com/auth/dataportability.streetview.imagery", "https://www.googleapis.com/auth/dataportability.youtube.channel", "https://www.googleapis.com/auth/dataportability.youtube.clips", "https://www.googleapis.com/auth/dataportability.youtube.comments", "https://www.googleapis.com/auth/dataportability.youtube.live_chat", "https://www.googleapis.com/auth/dataportability.youtube.music", "https://www.googleapis.com/auth/dataportability.youtube.playable", "https://www.googleapis.com/auth/dataportability.youtube.posts", "https://www.googleapis.com/auth/dataportability.youtube.private_playlists", "https://www.googleapis.com/auth/dataportability.youtube.private_videos", "https://www.googleapis.com/auth/dataportability.youtube.public_playlists", "https://www.googleapis.com/auth/dataportability.youtube.public_videos", "https://www.googleapis.com/auth/dataportability.youtube.shopping", "https://www.googleapis.com/auth/dataportability.youtube.subscriptions", "https://www.googleapis.com/auth/dataportability.youtube.unlisted_playlists", "https://www.googleapis.com/auth/dataportability.youtube.unlisted_videos"]}, "retry": {"description": "Retries a failed Portability Archive job.", "flatPath": "v1/archiveJobs/{archiveJobsId}:retry", "httpMethod": "POST", "id": "dataportability.archiveJobs.retry", "parameterOrder": ["name"], "parameters": {"name": {"description": "Required. The Archive job ID you're retrying. This is returned by the InitiatePortabilityArchiveResponse. Retrying is only executed if the initial job failed.", "location": "path", "pattern": "^archiveJobs/[^/]+$", "required": true, "type": "string"}}, "path": "v1/{+name}:retry", "request": {"$ref": "RetryPortabilityArchiveRequest"}, "response": {"$ref": "RetryPortabilityArchiveResponse"}, "scopes": ["https://www.googleapis.com/auth/dataportability.alerts.subscriptions", "https://www.googleapis.com/auth/dataportability.businessmessaging.conversations", "https://www.googleapis.com/auth/dataportability.chrome.autofill", "https://www.googleapis.com/auth/dataportability.chrome.bookmarks", "https://www.googleapis.com/auth/dataportability.chrome.dictionary", "https://www.googleapis.com/auth/dataportability.chrome.extensions", "https://www.googleapis.com/auth/dataportability.chrome.history", "https://www.googleapis.com/auth/dataportability.chrome.reading_list", "https://www.googleapis.com/auth/dataportability.chrome.settings", "https://www.googleapis.com/auth/dataportability.discover.follows", "https://www.googleapis.com/auth/dataportability.discover.likes", "https://www.googleapis.com/auth/dataportability.discover.not_interested", "https://www.googleapis.com/auth/dataportability.maps.aliased_places", "https://www.googleapis.com/auth/dataportability.maps.commute_routes", "https://www.googleapis.com/auth/dataportability.maps.commute_settings", "https://www.googleapis.com/auth/dataportability.maps.ev_profile", "https://www.googleapis.com/auth/dataportability.maps.factual_contributions", "https://www.googleapis.com/auth/dataportability.maps.offering_contributions", "https://www.googleapis.com/auth/dataportability.maps.photos_videos", "https://www.googleapis.com/auth/dataportability.maps.questions_answers", "https://www.googleapis.com/auth/dataportability.maps.reviews", "https://www.googleapis.com/auth/dataportability.maps.starred_places", "https://www.googleapis.com/auth/dataportability.maps.vehicle_profile", "https://www.googleapis.com/auth/dataportability.myactivity.maps", "https://www.googleapis.com/auth/dataportability.myactivity.myadcenter", "https://www.googleapis.com/auth/dataportability.myactivity.play", "https://www.googleapis.com/auth/dataportability.myactivity.search", "https://www.googleapis.com/auth/dataportability.myactivity.shopping", "https://www.googleapis.com/auth/dataportability.myactivity.youtube", "https://www.googleapis.com/auth/dataportability.mymaps.maps", "https://www.googleapis.com/auth/dataportability.order_reserve.purchases_reservations", "https://www.googleapis.com/auth/dataportability.play.devices", "https://www.googleapis.com/auth/dataportability.play.grouping", "https://www.googleapis.com/auth/dataportability.play.installs", "https://www.googleapis.com/auth/dataportability.play.library", "https://www.googleapis.com/auth/dataportability.play.playpoints", "https://www.googleapis.com/auth/dataportability.play.promotions", "https://www.googleapis.com/auth/dataportability.play.purchases", "https://www.googleapis.com/auth/dataportability.play.redemptions", "https://www.googleapis.com/auth/dataportability.play.subscriptions", "https://www.googleapis.com/auth/dataportability.play.usersettings", "https://www.googleapis.com/auth/dataportability.saved.collections", "https://www.googleapis.com/auth/dataportability.search_ugc.comments", "https://www.googleapis.com/auth/dataportability.search_ugc.media.reviews_and_stars", "https://www.googleapis.com/auth/dataportability.search_ugc.media.streaming_video_providers", "https://www.googleapis.com/auth/dataportability.search_ugc.media.thumbs", "https://www.googleapis.com/auth/dataportability.search_ugc.media.watched", "https://www.googleapis.com/auth/dataportability.searchnotifications.settings", "https://www.googleapis.com/auth/dataportability.searchnotifications.subscriptions", "https://www.googleapis.com/auth/dataportability.shopping.addresses", "https://www.googleapis.com/auth/dataportability.shopping.reviews", "https://www.googleapis.com/auth/dataportability.streetview.imagery", "https://www.googleapis.com/auth/dataportability.youtube.channel", "https://www.googleapis.com/auth/dataportability.youtube.clips", "https://www.googleapis.com/auth/dataportability.youtube.comments", "https://www.googleapis.com/auth/dataportability.youtube.live_chat", "https://www.googleapis.com/auth/dataportability.youtube.music", "https://www.googleapis.com/auth/dataportability.youtube.playable", "https://www.googleapis.com/auth/dataportability.youtube.posts", "https://www.googleapis.com/auth/dataportability.youtube.private_playlists", "https://www.googleapis.com/auth/dataportability.youtube.private_videos", "https://www.googleapis.com/auth/dataportability.youtube.public_playlists", "https://www.googleapis.com/auth/dataportability.youtube.public_videos", "https://www.googleapis.com/auth/dataportability.youtube.shopping", "https://www.googleapis.com/auth/dataportability.youtube.subscriptions", "https://www.googleapis.com/auth/dataportability.youtube.unlisted_playlists", "https://www.googleapis.com/auth/dataportability.youtube.unlisted_videos"]}}}, "authorization": {"methods": {"reset": {"description": "Revokes OAuth tokens and resets exhausted scopes for a user/project pair. This method allows you to initiate a request after a new consent is granted. This method also indicates that previous archives can be garbage collected. You should call this method when all jobs are complete and all archives are downloaded. Do not call it only when you start a new job.", "flatPath": "v1/authorization:reset", "httpMethod": "POST", "id": "dataportability.authorization.reset", "parameterOrder": [], "parameters": {}, "path": "v1/authorization:reset", "request": {"$ref": "ResetAuthorizationRequest"}, "response": {"$ref": "Empty"}, "scopes": ["https://www.googleapis.com/auth/dataportability.alerts.subscriptions", "https://www.googleapis.com/auth/dataportability.businessmessaging.conversations", "https://www.googleapis.com/auth/dataportability.chrome.autofill", "https://www.googleapis.com/auth/dataportability.chrome.bookmarks", "https://www.googleapis.com/auth/dataportability.chrome.dictionary", "https://www.googleapis.com/auth/dataportability.chrome.extensions", "https://www.googleapis.com/auth/dataportability.chrome.history", "https://www.googleapis.com/auth/dataportability.chrome.reading_list", "https://www.googleapis.com/auth/dataportability.chrome.settings", "https://www.googleapis.com/auth/dataportability.discover.follows", "https://www.googleapis.com/auth/dataportability.discover.likes", "https://www.googleapis.com/auth/dataportability.discover.not_interested", "https://www.googleapis.com/auth/dataportability.maps.aliased_places", "https://www.googleapis.com/auth/dataportability.maps.commute_routes", "https://www.googleapis.com/auth/dataportability.maps.commute_settings", "https://www.googleapis.com/auth/dataportability.maps.ev_profile", "https://www.googleapis.com/auth/dataportability.maps.factual_contributions", "https://www.googleapis.com/auth/dataportability.maps.offering_contributions", "https://www.googleapis.com/auth/dataportability.maps.photos_videos", "https://www.googleapis.com/auth/dataportability.maps.questions_answers", "https://www.googleapis.com/auth/dataportability.maps.reviews", "https://www.googleapis.com/auth/dataportability.maps.starred_places", "https://www.googleapis.com/auth/dataportability.maps.vehicle_profile", "https://www.googleapis.com/auth/dataportability.myactivity.maps", "https://www.googleapis.com/auth/dataportability.myactivity.myadcenter", "https://www.googleapis.com/auth/dataportability.myactivity.play", "https://www.googleapis.com/auth/dataportability.myactivity.search", "https://www.googleapis.com/auth/dataportability.myactivity.shopping", "https://www.googleapis.com/auth/dataportability.myactivity.youtube", "https://www.googleapis.com/auth/dataportability.mymaps.maps", "https://www.googleapis.com/auth/dataportability.order_reserve.purchases_reservations", "https://www.googleapis.com/auth/dataportability.play.devices", "https://www.googleapis.com/auth/dataportability.play.grouping", "https://www.googleapis.com/auth/dataportability.play.installs", "https://www.googleapis.com/auth/dataportability.play.library", "https://www.googleapis.com/auth/dataportability.play.playpoints", "https://www.googleapis.com/auth/dataportability.play.promotions", "https://www.googleapis.com/auth/dataportability.play.purchases", "https://www.googleapis.com/auth/dataportability.play.redemptions", "https://www.googleapis.com/auth/dataportability.play.subscriptions", "https://www.googleapis.com/auth/dataportability.play.usersettings", "https://www.googleapis.com/auth/dataportability.saved.collections", "https://www.googleapis.com/auth/dataportability.search_ugc.comments", "https://www.googleapis.com/auth/dataportability.search_ugc.media.reviews_and_stars", "https://www.googleapis.com/auth/dataportability.search_ugc.media.streaming_video_providers", "https://www.googleapis.com/auth/dataportability.search_ugc.media.thumbs", "https://www.googleapis.com/auth/dataportability.search_ugc.media.watched", "https://www.googleapis.com/auth/dataportability.searchnotifications.settings", "https://www.googleapis.com/auth/dataportability.searchnotifications.subscriptions", "https://www.googleapis.com/auth/dataportability.shopping.addresses", "https://www.googleapis.com/auth/dataportability.shopping.reviews", "https://www.googleapis.com/auth/dataportability.streetview.imagery", "https://www.googleapis.com/auth/dataportability.youtube.channel", "https://www.googleapis.com/auth/dataportability.youtube.clips", "https://www.googleapis.com/auth/dataportability.youtube.comments", "https://www.googleapis.com/auth/dataportability.youtube.live_chat", "https://www.googleapis.com/auth/dataportability.youtube.music", "https://www.googleapis.com/auth/dataportability.youtube.playable", "https://www.googleapis.com/auth/dataportability.youtube.posts", "https://www.googleapis.com/auth/dataportability.youtube.private_playlists", "https://www.googleapis.com/auth/dataportability.youtube.private_videos", "https://www.googleapis.com/auth/dataportability.youtube.public_playlists", "https://www.googleapis.com/auth/dataportability.youtube.public_videos", "https://www.googleapis.com/auth/dataportability.youtube.shopping", "https://www.googleapis.com/auth/dataportability.youtube.subscriptions", "https://www.googleapis.com/auth/dataportability.youtube.unlisted_playlists", "https://www.googleapis.com/auth/dataportability.youtube.unlisted_videos"]}}}, "portabilityArchive": {"methods": {"initiate": {"description": "Initiates a new Archive job for the Portability API.", "flatPath": "v1/portabilityArchive:initiate", "httpMethod": "POST", "id": "dataportability.portabilityArchive.initiate", "parameterOrder": [], "parameters": {}, "path": "v1/portabilityArchive:initiate", "request": {"$ref": "InitiatePortabilityArchiveRequest"}, "response": {"$ref": "InitiatePortabilityArchiveResponse"}, "scopes": ["https://www.googleapis.com/auth/dataportability.alerts.subscriptions", "https://www.googleapis.com/auth/dataportability.businessmessaging.conversations", "https://www.googleapis.com/auth/dataportability.chrome.autofill", "https://www.googleapis.com/auth/dataportability.chrome.bookmarks", "https://www.googleapis.com/auth/dataportability.chrome.dictionary", "https://www.googleapis.com/auth/dataportability.chrome.extensions", "https://www.googleapis.com/auth/dataportability.chrome.history", "https://www.googleapis.com/auth/dataportability.chrome.reading_list", "https://www.googleapis.com/auth/dataportability.chrome.settings", "https://www.googleapis.com/auth/dataportability.discover.follows", "https://www.googleapis.com/auth/dataportability.discover.likes", "https://www.googleapis.com/auth/dataportability.discover.not_interested", "https://www.googleapis.com/auth/dataportability.maps.aliased_places", "https://www.googleapis.com/auth/dataportability.maps.commute_routes", "https://www.googleapis.com/auth/dataportability.maps.commute_settings", "https://www.googleapis.com/auth/dataportability.maps.ev_profile", "https://www.googleapis.com/auth/dataportability.maps.factual_contributions", "https://www.googleapis.com/auth/dataportability.maps.offering_contributions", "https://www.googleapis.com/auth/dataportability.maps.photos_videos", "https://www.googleapis.com/auth/dataportability.maps.questions_answers", "https://www.googleapis.com/auth/dataportability.maps.reviews", "https://www.googleapis.com/auth/dataportability.maps.starred_places", "https://www.googleapis.com/auth/dataportability.maps.vehicle_profile", "https://www.googleapis.com/auth/dataportability.myactivity.maps", "https://www.googleapis.com/auth/dataportability.myactivity.myadcenter", "https://www.googleapis.com/auth/dataportability.myactivity.play", "https://www.googleapis.com/auth/dataportability.myactivity.search", "https://www.googleapis.com/auth/dataportability.myactivity.shopping", "https://www.googleapis.com/auth/dataportability.myactivity.youtube", "https://www.googleapis.com/auth/dataportability.mymaps.maps", "https://www.googleapis.com/auth/dataportability.order_reserve.purchases_reservations", "https://www.googleapis.com/auth/dataportability.play.devices", "https://www.googleapis.com/auth/dataportability.play.grouping", "https://www.googleapis.com/auth/dataportability.play.installs", "https://www.googleapis.com/auth/dataportability.play.library", "https://www.googleapis.com/auth/dataportability.play.playpoints", "https://www.googleapis.com/auth/dataportability.play.promotions", "https://www.googleapis.com/auth/dataportability.play.purchases", "https://www.googleapis.com/auth/dataportability.play.redemptions", "https://www.googleapis.com/auth/dataportability.play.subscriptions", "https://www.googleapis.com/auth/dataportability.play.usersettings", "https://www.googleapis.com/auth/dataportability.saved.collections", "https://www.googleapis.com/auth/dataportability.search_ugc.comments", "https://www.googleapis.com/auth/dataportability.search_ugc.media.reviews_and_stars", "https://www.googleapis.com/auth/dataportability.search_ugc.media.streaming_video_providers", "https://www.googleapis.com/auth/dataportability.search_ugc.media.thumbs", "https://www.googleapis.com/auth/dataportability.search_ugc.media.watched", "https://www.googleapis.com/auth/dataportability.searchnotifications.settings", "https://www.googleapis.com/auth/dataportability.searchnotifications.subscriptions", "https://www.googleapis.com/auth/dataportability.shopping.addresses", "https://www.googleapis.com/auth/dataportability.shopping.reviews", "https://www.googleapis.com/auth/dataportability.streetview.imagery", "https://www.googleapis.com/auth/dataportability.youtube.channel", "https://www.googleapis.com/auth/dataportability.youtube.clips", "https://www.googleapis.com/auth/dataportability.youtube.comments", "https://www.googleapis.com/auth/dataportability.youtube.live_chat", "https://www.googleapis.com/auth/dataportability.youtube.music", "https://www.googleapis.com/auth/dataportability.youtube.playable", "https://www.googleapis.com/auth/dataportability.youtube.posts", "https://www.googleapis.com/auth/dataportability.youtube.private_playlists", "https://www.googleapis.com/auth/dataportability.youtube.private_videos", "https://www.googleapis.com/auth/dataportability.youtube.public_playlists", "https://www.googleapis.com/auth/dataportability.youtube.public_videos", "https://www.googleapis.com/auth/dataportability.youtube.shopping", "https://www.googleapis.com/auth/dataportability.youtube.subscriptions", "https://www.googleapis.com/auth/dataportability.youtube.unlisted_playlists", "https://www.googleapis.com/auth/dataportability.youtube.unlisted_videos"]}}}}, "revision": "20250605", "rootUrl": "https://dataportability.googleapis.com/", "schemas": {"CancelPortabilityArchiveRequest": {"description": "Request to cancel a Portability Archive job.", "id": "CancelPortabilityArchiveRequest", "properties": {}, "type": "object"}, "CancelPortabilityArchiveResponse": {"description": "Response to canceling a Data Portability Archive job.", "id": "CancelPortabilityArchiveResponse", "properties": {}, "type": "object"}, "CheckAccessTypeRequest": {"description": "Request to check the token's access type. All required information is derived from the attached OAuth token.", "id": "CheckAccessTypeRequest", "properties": {}, "type": "object"}, "CheckAccessTypeResponse": {"description": "Response to checking the token's access type.", "id": "CheckAccessTypeResponse", "properties": {"oneTimeResources": {"description": "Jobs initiated with this token will be one-time if any requested resources have one-time access.", "items": {"type": "string"}, "type": "array"}, "timeBasedResources": {"description": "Jobs initiated with this token will be time-based if all requested resources have time-based access.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "Empty": {"description": "A generic empty message that you can re-use to avoid defining duplicated empty messages in your APIs. A typical example is to use it as the request or the response type of an API method. For instance: service Foo { rpc Bar(google.protobuf.Empty) returns (google.protobuf.Empty); }", "id": "Empty", "properties": {}, "type": "object"}, "InitiatePortabilityArchiveRequest": {"description": "Request to kick off an Archive job.", "id": "InitiatePortabilityArchiveRequest", "properties": {"endTime": {"description": "Optional. The timestamp that represents the end point for the data you are exporting. If the end_time is not specified in the InitiatePortabilityArchiveRequest, this field is set to the latest available data.", "format": "google-datetime", "type": "string"}, "resources": {"description": "The resources from which you're exporting data. These values have a 1:1 correspondence with the OAuth scopes.", "items": {"type": "string"}, "type": "array"}, "startTime": {"description": "Optional. The timestamp that represents the starting point for the data you are exporting. If the start_time is not specified in the InitiatePortabilityArchiveRequest, the field is set to the earliest available data.", "format": "google-datetime", "type": "string"}}, "type": "object"}, "InitiatePortabilityArchiveResponse": {"description": "Response from initiating an Archive job.", "id": "InitiatePortabilityArchiveResponse", "properties": {"accessType": {"description": "The access type of the Archive job initiated by the API.", "enum": ["ACCESS_TYPE_UNSPECIFIED", "ACCESS_TYPE_ONE_TIME", "ACCESS_TYPE_TIME_BASED"], "enumDescriptions": ["Default value. This value is unused.", "One-time access to the requested scopes.", "Multiple exports allowed over 30 days. Enum value subject to change before launch."], "type": "string"}, "archiveJobId": {"description": "The archive job ID that is initiated in the API. This can be used to get the state of the job.", "type": "string"}}, "type": "object"}, "PortabilityArchiveState": {"description": "Resource that contains the state of an Archive job.", "id": "PortabilityArchiveState", "properties": {"exportTime": {"description": "The timestamp that represents the end point for the data you are exporting. If the end_time value is set in the InitiatePortabilityArchiveRequest, this field is set to that value. If end_time is not set, this value is set to the time the export was requested.", "format": "google-datetime", "type": "string"}, "name": {"description": "The resource name of ArchiveJob's PortabilityArchiveState singleton. The format is: archiveJobs/{archive_job}/portabilityArchiveState. archive_job is the job ID provided in the request.", "type": "string"}, "startTime": {"description": "The timestamp that represents the starting point for the data you are exporting. This field is set only if the start_time field is specified in the InitiatePortabilityArchiveRequest.", "format": "google-datetime", "type": "string"}, "state": {"description": "Resource that represents the state of the Archive job.", "enum": ["STATE_UNSPECIFIED", "IN_PROGRESS", "COMPLETE", "FAILED", "CANCELLED"], "enumDescriptions": ["Default value. This value is unused.", "The job is in progress.", "The job is complete.", "The job failed.", "The job is cancelled."], "type": "string"}, "urls": {"description": "If the state is complete, this method returns the signed URLs of the objects in the Cloud Storage bucket.", "items": {"type": "string"}, "type": "array"}}, "type": "object"}, "ResetAuthorizationRequest": {"description": "Request to reset exhausted OAuth scopes.", "id": "ResetAuthorizationRequest", "properties": {}, "type": "object"}, "RetryPortabilityArchiveRequest": {"description": "Request to retry a failed Portability Archive job.", "id": "RetryPortabilityArchiveRequest", "properties": {}, "type": "object"}, "RetryPortabilityArchiveResponse": {"description": "Response from retrying a Portability Archive.", "id": "RetryPortabilityArchiveResponse", "properties": {"archiveJobId": {"description": "The archive job ID that is initiated by the retry endpoint. This can be used to get the state of the new job.", "type": "string"}}, "type": "object"}}, "servicePath": "", "title": "Data Portability API", "version": "v1", "version_module": true}