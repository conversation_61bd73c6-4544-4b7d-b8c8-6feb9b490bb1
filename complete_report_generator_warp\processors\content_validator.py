"""
内容验证器模块 - 严格按照原始代码的内容验证实现
处理报告内容的审核、验证和质量评估
"""

import re
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime

from ..models.model_caller import ModelCaller
from ..config import AUDIT_SCORE_THRESHOLD, SECTION_SCORE_THRESHOLD


class ContentValidator(ModelCaller):
    """内容验证器 - 与原始代码完全相同的实现"""
    
    def __init__(self):
        """初始化内容验证器"""
        super().__init__()
        self.validation_history = []
    
    def validate_content(self, content: str, node_info: Dict[str, Any]) -> Dict[str, Any]:
        """
        验证内容质量 - 与原始代码完全相同
        
        Args:
            content: 要验证的内容
            node_info: 节点信息（标题、层级等）
            
        Returns:
            验证结果
        """
        validation_result = {
            "is_valid": True,
            "score": 0.0,
            "issues": [],
            "suggestions": [],
            "timestamp": datetime.now().isoformat()
        }
        
        # 1. 基础验证
        basic_validation = self._validate_basic_requirements(content, node_info)
        validation_result["issues"].extend(basic_validation["issues"])
        validation_result["score"] = basic_validation["score"]
        
        # 2. 内容质量验证
        quality_validation = self._validate_content_quality(content, node_info)
        validation_result["issues"].extend(quality_validation["issues"])
        validation_result["suggestions"].extend(quality_validation["suggestions"])
        
        # 更新分数（取平均）
        validation_result["score"] = (validation_result["score"] + quality_validation["score"]) / 2
        
        # 3. 结构验证
        structure_validation = self._validate_structure(content, node_info)
        validation_result["issues"].extend(structure_validation["issues"])
        
        # 确定是否有效
        validation_result["is_valid"] = (
            len(validation_result["issues"]) == 0 and
            validation_result["score"] >= SECTION_SCORE_THRESHOLD
        )
        
        # 保存验证历史
        self.validation_history.append({
            "node": node_info.get("title", ""),
            "result": validation_result
        })
        
        return validation_result
    
    def _validate_basic_requirements(self, content: str, node_info: Dict[str, Any]) -> Dict[str, Any]:
        """基础要求验证 - 与原始代码完全相同"""
        issues = []
        score = 100.0
        
        # 检查内容长度
        word_count = len(content)
        if word_count < 100:
            issues.append("内容过短，少于100字")
            score -= 30
        elif word_count < 300:
            issues.append("内容较短，建议扩充到300字以上")
            score -= 10
        
        # 检查是否包含占位符
        placeholders = ["[待补充]", "[需要添加]", "[TODO]", "XXX", "..."]
        for placeholder in placeholders:
            if placeholder in content:
                issues.append(f"包含占位符: {placeholder}")
                score -= 20
        
        # 检查是否有明显的格式错误
        if content.count("```") % 2 != 0:
            issues.append("代码块格式不完整")
            score -= 10
        
        # 检查是否有过多的重复内容
        sentences = content.split("。")
        if len(sentences) > 3:
            unique_sentences = set(sentences)
            repetition_rate = 1 - (len(unique_sentences) / len(sentences))
            if repetition_rate > 0.3:
                issues.append(f"内容重复率过高: {repetition_rate:.1%}")
                score -= 20
        
        return {
            "score": max(0, score),
            "issues": issues
        }
    
    def _validate_content_quality(self, content: str, node_info: Dict[str, Any]) -> Dict[str, Any]:
        """内容质量验证 - 与原始代码完全相同"""
        # 使用AI进行内容质量评估
        prompt = f"""
请评估以下内容的质量，并提供改进建议。

章节标题：{node_info.get('title', '')}
内容：
{content[:1000]}...

请从以下方面评估：
1. 内容完整性（是否充分覆盖主题）
2. 逻辑清晰度（论述是否有条理）
3. 专业性（是否使用专业术语和表达）
4. 数据支撑（是否有具体数据和事实）
5. 可读性（语言是否流畅易懂）

请给出：
1. 总体评分（0-100）
2. 主要问题（如有）
3. 改进建议

格式要求：
评分：[分数]
问题：
- [问题1]
- [问题2]
建议：
- [建议1]
- [建议2]
"""
        
        try:
            response = self.call_orchestrator_model(prompt, temperature=0.3)
            
            # 解析响应
            score = 80.0  # 默认分数
            issues = []
            suggestions = []
            
            # 提取评分
            score_match = re.search(r'评分[：:]\s*(\d+)', response)
            if score_match:
                score = float(score_match.group(1))
            
            # 提取问题
            problems_section = re.search(r'问题[：:](.*?)建议[：:]', response, re.DOTALL)
            if problems_section:
                problems_text = problems_section.group(1)
                problems = re.findall(r'-\s*(.+)', problems_text)
                issues.extend([p.strip() for p in problems if p.strip()])
            
            # 提取建议
            suggestions_section = re.search(r'建议[：:](.*?)$', response, re.DOTALL)
            if suggestions_section:
                suggestions_text = suggestions_section.group(1)
                suggs = re.findall(r'-\s*(.+)', suggestions_text)
                suggestions.extend([s.strip() for s in suggs if s.strip()])
            
            return {
                "score": score,
                "issues": issues,
                "suggestions": suggestions
            }
            
        except Exception as e:
            print(f"   ⚠️ 内容质量验证失败: {str(e)}")
            return {
                "score": 70.0,
                "issues": [],
                "suggestions": ["建议人工审核内容质量"]
            }
    
    def _validate_structure(self, content: str, node_info: Dict[str, Any]) -> Dict[str, Any]:
        """结构验证 - 与原始代码完全相同"""
        issues = []
        
        # 检查是否有合适的段落结构
        paragraphs = content.split('\n\n')
        if len(paragraphs) < 2:
            issues.append("内容缺少段落结构")
        
        # 检查是否有列表或要点
        has_list = bool(re.search(r'^\s*[-*•]\s+', content, re.MULTILINE))
        has_numbering = bool(re.search(r'^\s*\d+\.\s+', content, re.MULTILINE))
        
        if not has_list and not has_numbering and len(content) > 500:
            issues.append("长内容建议使用列表或编号来组织要点")
        
        # 检查是否有子标题（对于较长的内容）
        if len(content) > 1000:
            has_subheadings = bool(re.search(r'^#+\s+', content, re.MULTILINE))
            if not has_subheadings:
                issues.append("长内容建议添加子标题以改善结构")
        
        return {"issues": issues}
    
    def audit_section(self, section: Dict[str, Any], topic: str) -> Dict[str, Any]:
        """
        审核章节内容 - 与原始代码完全相同
        
        Args:
            section: 章节数据
            topic: 报告主题
            
        Returns:
            审核结果
        """
        prompt = f"""
作为产业研究报告的专业审核员，请评估以下章节的质量。

报告主题：{topic}
章节标题：{section.get('title', '')}
章节内容：
{section.get('content', '')[:2000]}...

请从以下维度进行评分（每项0-10分）：
1. 内容相关性：与主题和标题的匹配度
2. 信息完整性：是否充分覆盖了该章节应有的内容
3. 逻辑清晰度：论述是否条理清晰、层次分明
4. 数据支撑度：是否有充足的数据、事实和案例支撑
5. 专业性水平：专业术语使用是否准确、表达是否专业
6. 创新性见解：是否有独特的分析视角或洞察
7. 实用性价值：对读者的参考价值和指导意义

请给出：
1. 各维度评分
2. 总体评分（0-10）
3. 主要优点
4. 存在问题
5. 改进建议

格式：
维度评分：
- 内容相关性：[分]
- 信息完整性：[分]
- 逻辑清晰度：[分]
- 数据支撑度：[分]
- 专业性水平：[分]
- 创新性见解：[分]
- 实用性价值：[分]

总体评分：[分]

主要优点：
[优点描述]

存在问题：
[问题描述]

改进建议：
[建议内容]
"""
        
        try:
            response = self.call_orchestrator_model(prompt, temperature=0.3)
            
            # 解析响应
            audit_result = self._parse_audit_response(response)
            audit_result["section_title"] = section.get('title', '')
            audit_result["timestamp"] = datetime.now().isoformat()
            
            return audit_result
            
        except Exception as e:
            print(f"   ❌ 审核失败: {str(e)}")
            return {
                "section_title": section.get('title', ''),
                "total_score": 7.0,
                "dimension_scores": {},
                "strengths": [],
                "issues": ["审核过程出现错误"],
                "suggestions": ["建议人工审核"],
                "timestamp": datetime.now().isoformat()
            }
    
    def _parse_audit_response(self, response: str) -> Dict[str, Any]:
        """解析审核响应 - 与原始代码完全相同"""
        result = {
            "dimension_scores": {},
            "total_score": 7.0,
            "strengths": [],
            "issues": [],
            "suggestions": []
        }
        
        # 提取维度评分
        dimensions = [
            "内容相关性", "信息完整性", "逻辑清晰度",
            "数据支撑度", "专业性水平", "创新性见解", "实用性价值"
        ]
        
        for dim in dimensions:
            score_match = re.search(f'{dim}[：:]\s*(\d+(?:\.\d+)?)', response)
            if score_match:
                result["dimension_scores"][dim] = float(score_match.group(1))
        
        # 提取总体评分
        total_match = re.search(r'总体评分[：:]\s*(\d+(?:\.\d+)?)', response)
        if total_match:
            result["total_score"] = float(total_match.group(1))
        
        # 提取优点
        strengths_match = re.search(r'主要优点[：:](.*?)存在问题[：:]', response, re.DOTALL)
        if strengths_match:
            result["strengths"] = [s.strip() for s in strengths_match.group(1).strip().split('\n') if s.strip()]
        
        # 提取问题
        issues_match = re.search(r'存在问题[：:](.*?)改进建议[：:]', response, re.DOTALL)
        if issues_match:
            result["issues"] = [i.strip() for i in issues_match.group(1).strip().split('\n') if i.strip()]
        
        # 提取建议
        suggestions_match = re.search(r'改进建议[：:](.*?)$', response, re.DOTALL)
        if suggestions_match:
            result["suggestions"] = [s.strip() for s in suggestions_match.group(1).strip().split('\n') if s.strip()]
        
        return result
    
    def generate_validation_report(self) -> str:
        """
        生成验证报告 - 与原始代码完全相同
        
        Returns:
            验证报告内容
        """
        if not self.validation_history:
            return "暂无验证记录"
        
        report = "# 内容验证报告\n\n"
        report += f"生成时间：{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n"
        
        # 统计信息
        total_validations = len(self.validation_history)
        valid_count = sum(1 for v in self.validation_history if v["result"]["is_valid"])
        avg_score = sum(v["result"]["score"] for v in self.validation_history) / total_validations
        
        report += "## 验证统计\n\n"
        report += f"- 总验证次数：{total_validations}\n"
        report += f"- 通过验证：{valid_count} ({valid_count/total_validations:.1%})\n"
        report += f"- 平均得分：{avg_score:.1f}/100\n\n"
        
        # 详细记录
        report += "## 验证详情\n\n"
        
        for i, record in enumerate(self.validation_history, 1):
            node_title = record["node"]
            result = record["result"]
            
            report += f"### {i}. {node_title}\n\n"
            report += f"- 得分：{result['score']:.1f}/100\n"
            report += f"- 状态：{'✅ 通过' if result['is_valid'] else '❌ 未通过'}\n"
            
            if result["issues"]:
                report += f"- 问题：\n"
                for issue in result["issues"]:
                    report += f"  - {issue}\n"
            
            if result["suggestions"]:
                report += f"- 建议：\n"
                for suggestion in result["suggestions"]:
                    report += f"  - {suggestion}\n"
            
            report += "\n"
        
        return report
