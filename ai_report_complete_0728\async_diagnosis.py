#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
异步机制诊断脚本
检查重构后代码中的异步工作机制问题
"""

import sys
import asyncio
import inspect
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from core.generator import CompleteReportGenerator

def analyze_async_issues():
    """分析异步机制中的潜在问题"""
    print("🔍 异步机制诊断分析")
    print("=" * 60)
    
    # 1. 检查异步方法定义
    print("\n1️⃣ 检查异步方法定义...")
    generator = CompleteReportGenerator()
    
    async_methods = []
    sync_methods = []
    
    for name, method in inspect.getmembers(generator, predicate=inspect.ismethod):
        if inspect.iscoroutinefunction(method):
            async_methods.append(name)
        elif name.startswith('call_') or name.endswith('_async'):
            sync_methods.append(name)
    
    print(f"✅ 发现 {len(async_methods)} 个异步方法:")
    for method in async_methods:
        print(f"   - {method}")
    
    print(f"⚠️ 发现 {len(sync_methods)} 个可能应该是异步的方法:")
    for method in sync_methods:
        print(f"   - {method}")
    
    # 2. 检查异步调用链
    print("\n2️⃣ 检查异步调用链...")
    
    # 检查 generate_report 方法的异步处理
    print("📋 generate_report 异步处理:")
    print(f"   - use_async 默认值: {generator.use_async}")
    print(f"   - 异步事件循环创建: 使用 asyncio.new_event_loop()")
    print(f"   - 异步方法委托: generate_report_async -> generate_report_sync")
    
    # 3. 检查潜在的死锁问题
    print("\n3️⃣ 检查潜在的死锁问题...")
    
    issues = []
    
    # 问题1: 混合同步和异步调用
    issues.append({
        "type": "混合调用",
        "description": "在异步方法中调用同步的 record_successful_processing",
        "location": "call_orchestrator_model_async, call_executor_model_async",
        "severity": "高"
    })
    
    # 问题2: 事件循环嵌套
    issues.append({
        "type": "事件循环嵌套",
        "description": "在已有事件循环中创建新的事件循环",
        "location": "generate_report 方法",
        "severity": "高"
    })
    
    # 问题3: 异步方法委托给同步方法
    issues.append({
        "type": "异步委托同步",
        "description": "generate_report_async 直接调用 generate_report_sync",
        "location": "generate_report_async",
        "severity": "中"
    })
    
    # 问题4: 缺少 await 关键字
    issues.append({
        "type": "缺少await",
        "description": "可能存在未正确等待的异步调用",
        "location": "多个异步方法",
        "severity": "中"
    })
    
    for i, issue in enumerate(issues, 1):
        severity_icon = {"高": "🔴", "中": "🟡", "低": "🟢"}[issue["severity"]]
        print(f"{severity_icon} 问题 {i}: {issue['type']}")
        print(f"   描述: {issue['description']}")
        print(f"   位置: {issue['location']}")
        print(f"   严重性: {issue['severity']}")
        print()
    
    return issues

def test_async_functionality():
    """测试异步功能是否正常工作"""
    print("\n4️⃣ 测试异步功能...")
    
    try:
        generator = CompleteReportGenerator()
        
        # 测试1: 检查是否在事件循环中
        try:
            loop = asyncio.get_running_loop()
            print("⚠️ 当前已在事件循环中运行")
            in_loop = True
        except RuntimeError:
            print("✅ 当前不在事件循环中")
            in_loop = False
        
        # 测试2: 尝试异步调用
        if not in_loop:
            print("🧪 测试异步模型调用...")
            
            async def test_async_call():
                try:
                    result = await generator.call_orchestrator_model_async("测试prompt")
                    return f"成功: {len(result)} 字符"
                except Exception as e:
                    return f"失败: {str(e)}"
            
            # 运行异步测试
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            try:
                result = loop.run_until_complete(test_async_call())
                print(f"   结果: {result}")
            finally:
                loop.close()
        else:
            print("⚠️ 跳过异步测试（已在事件循环中）")
        
        # 测试3: 检查API管理器的异步支持
        print("🔧 检查API管理器异步支持...")
        has_async_method = hasattr(generator.api_manager, 'generate_content_with_model_async')
        print(f"   异步方法存在: {has_async_method}")
        
        if has_async_method:
            async_method = getattr(generator.api_manager, 'generate_content_with_model_async')
            is_coroutine = inspect.iscoroutinefunction(async_method)
            print(f"   是协程函数: {is_coroutine}")
        
        return True
        
    except Exception as e:
        print(f"❌ 异步功能测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def suggest_fixes():
    """建议修复方案"""
    print("\n5️⃣ 修复建议...")
    print("=" * 60)
    
    fixes = [
        {
            "问题": "混合同步异步调用",
            "建议": "将 record_successful_processing 改为异步方法或使用 asyncio.create_task",
            "优先级": "高"
        },
        {
            "问题": "事件循环嵌套",
            "建议": "检查是否已在事件循环中，避免创建新循环",
            "优先级": "高"
        },
        {
            "问题": "异步方法委托同步",
            "建议": "实现真正的异步版本或使用 asyncio.to_thread",
            "优先级": "中"
        },
        {
            "问题": "错误处理不完善",
            "建议": "添加异步特定的错误处理和超时机制",
            "优先级": "中"
        },
        {
            "问题": "缺少并发控制",
            "建议": "添加信号量或其他并发控制机制",
            "优先级": "低"
        }
    ]
    
    for i, fix in enumerate(fixes, 1):
        priority_icon = {"高": "🔴", "中": "🟡", "低": "🟢"}[fix["优先级"]]
        print(f"{priority_icon} 修复 {i}: {fix['问题']}")
        print(f"   建议: {fix['建议']}")
        print(f"   优先级: {fix['优先级']}")
        print()

def main():
    """主诊断函数"""
    print("🚀 异步机制诊断工具")
    print("检查重构后代码中的异步工作机制问题")
    print("=" * 60)
    
    # 分析异步问题
    issues = analyze_async_issues()
    
    # 测试异步功能
    async_works = test_async_functionality()
    
    # 建议修复方案
    suggest_fixes()
    
    # 总结
    print("\n📊 诊断总结:")
    print("=" * 60)
    print(f"🔍 发现问题数量: {len(issues)}")
    print(f"🧪 异步功能测试: {'通过' if async_works else '失败'}")
    
    high_priority_issues = [i for i in issues if i["severity"] == "高"]
    if high_priority_issues:
        print(f"🔴 高优先级问题: {len(high_priority_issues)} 个")
        print("   建议立即修复以避免异步卡死问题")
    else:
        print("✅ 无高优先级异步问题")
    
    print("\n💡 建议:")
    print("1. 优先修复高优先级问题")
    print("2. 实现真正的异步并行处理")
    print("3. 添加完善的错误处理和超时机制")
    print("4. 考虑使用 asyncio.gather 进行并发处理")

if __name__ == "__main__":
    main()
