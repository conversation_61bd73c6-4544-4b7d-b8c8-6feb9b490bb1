"""
简单的异步测试
"""
import asyncio
import time

async def test_basic_async():
    """基础异步测试"""
    print("🧪 基础异步测试")
    
    try:
        # 测试tqdm导入
        from tqdm.asyncio import tqdm
        print("✅ tqdm导入成功")
        
        # 测试进度条
        pbar = tqdm(total=3, desc="测试", unit="项")
        for i in range(3):
            await asyncio.sleep(0.5)
            pbar.update(1)
        pbar.close()
        print("✅ 进度条测试成功")
        
        # 测试生成器导入
        from complete_report_generator import CompleteReportGenerator
        print("✅ 生成器导入成功")
        
        # 创建生成器
        generator = CompleteReportGenerator(use_async=True)
        print("✅ 异步生成器创建成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    print("🚀 简单异步测试")
    result = await test_basic_async()
    if result:
        print("🎉 基础测试通过")
    else:
        print("❌ 基础测试失败")
    return result

if __name__ == "__main__":
    try:
        success = asyncio.run(main())
        exit(0 if success else 1)
    except Exception as e:
        print(f"错误: {e}")
        exit(1)
