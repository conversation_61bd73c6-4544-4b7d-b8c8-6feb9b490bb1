"""
搜索增强功能演示
展示完整的用户交互流程，包括y/n选择界面
"""
import os
import sys
import time
from pathlib import Path
from complete_report_generator import CompleteReportGenerator

def create_demo_report():
    """创建演示报告"""
    print("📄 创建演示报告...")
    
    demo_dir = Path("demo_search_data")
    if demo_dir.exists():
        import shutil
        shutil.rmtree(demo_dir)
    demo_dir.mkdir()
    
    # 创建一个需要搜索增强的报告
    demo_content = """# 量子计算技术发展报告

## 1. 技术概述

量子计算是一种基于量子力学原理的计算技术，具有处理特定问题的巨大优势。

### 1.1 基本原理

量子计算利用量子比特（qubit）的叠加态和纠缠特性进行计算。

### 1.2 技术优势

相比传统计算机，量子计算在某些算法上具有指数级的速度优势。

## 2. 技术发展

### 2.1 硬件发展

量子计算硬件包括超导量子比特、离子阱、光量子等多种技术路线。

### 2.2 软件发展

量子编程语言和量子算法是量子计算软件发展的重点。

## 3. 应用前景

### 3.1 密码学

量子计算对现有加密算法构成威胁，同时也为量子密码学提供机会。

### 3.2 优化问题

量子计算在组合优化、机器学习等领域有广阔应用前景。

## 4. 挑战与展望

量子计算仍面临量子纠错、扩展性等技术挑战。

---

*本报告基于公开资料整理，可能缺少最新的发展动态和市场数据。*
"""
    
    demo_report_path = demo_dir / "quantum_computing_report.md"
    with open(demo_report_path, 'w', encoding='utf-8') as f:
        f.write(demo_content)
    
    print(f"✅ 演示报告已创建: {demo_report_path}")
    return demo_report_path

def demo_search_enhancement_with_user_interaction():
    """演示带用户交互的搜索增强功能"""
    print("🚀 搜索增强功能演示")
    print("=" * 60)
    
    # 创建演示报告
    demo_report_path = create_demo_report()
    
    # 初始化生成器
    generator = CompleteReportGenerator(use_async=False)
    
    print(f"\n📖 演示报告内容:")
    print(f"   主题: 量子计算技术发展")
    print(f"   文件: {demo_report_path}")
    print(f"   特点: 缺少最新数据和市场信息")
    
    # 执行搜索增强（这里会触发用户交互）
    print(f"\n🔍 开始搜索增强流程...")
    print(f"   系统将分析报告内容并识别可以通过搜索补充的信息缺口")
    
    try:
        enhanced_path = generator.enhance_report_with_search(
            str(demo_report_path),
            "量子计算技术发展",
            user_confirm=True  # 启用用户确认
        )
        
        if enhanced_path != str(demo_report_path):
            print(f"\n✅ 搜索增强完成!")
            print(f"   原始报告: {demo_report_path}")
            print(f"   增强报告: {enhanced_path}")
            
            # 显示增强效果
            show_enhancement_comparison(demo_report_path, enhanced_path)
        else:
            print(f"\n📄 搜索增强未执行")
            print(f"   可能原因: 用户取消、API未配置或未找到合适的补充信息")
    
    except Exception as e:
        print(f"\n❌ 搜索增强过程出错: {str(e)}")
        print(f"   这通常是由于搜索API未配置导致的")
    
    # 清理演示数据
    cleanup_demo_data()

def show_enhancement_comparison(original_path, enhanced_path):
    """显示增强效果对比"""
    print(f"\n📊 增强效果对比:")
    
    try:
        # 读取原始内容
        with open(original_path, 'r', encoding='utf-8') as f:
            original_content = f.read()
        
        # 读取增强内容
        with open(enhanced_path, 'r', encoding='utf-8') as f:
            enhanced_content = f.read()
        
        print(f"   原始报告长度: {len(original_content)} 字符")
        print(f"   增强报告长度: {len(enhanced_content)} 字符")
        print(f"   增加内容: {len(enhanced_content) - len(original_content)} 字符")
        
        # 检查是否包含补充信息
        if "最新信息补充" in enhanced_content:
            print(f"   ✅ 包含最新信息补充部分")
            
            # 提取补充部分
            supplement_start = enhanced_content.find("## 最新信息补充")
            if supplement_start != -1:
                supplement_content = enhanced_content[supplement_start:]
                supplement_sections = supplement_content.count("###")
                print(f"   📋 补充信息包含 {supplement_sections} 个部分")
        
        # 显示增强内容预览
        print(f"\n📝 增强内容预览:")
        lines = enhanced_content.split('\n')
        for i, line in enumerate(lines[-10:], len(lines)-9):  # 显示最后10行
            print(f"   {i:3d}: {line}")
    
    except Exception as e:
        print(f"   ⚠️ 无法读取文件进行对比: {str(e)}")

def demo_api_configuration_guide():
    """演示API配置指南"""
    print(f"\n🔧 搜索API配置指南")
    print("=" * 50)
    
    print(f"为了使用搜索增强功能，您需要配置以下API:")
    
    print(f"\n📍 Google Custom Search API:")
    print(f"   1. 访问: https://developers.google.com/custom-search/v1/introduction")
    print(f"   2. 创建项目并启用Custom Search API")
    print(f"   3. 获取API Key")
    print(f"   4. 创建自定义搜索引擎获取CX ID")
    print(f"   5. 设置环境变量:")
    print(f"      GOOGLE_SEARCH_API_KEY=your_api_key")
    print(f"      GOOGLE_SEARCH_CX=your_cx_id")
    
    print(f"\n🔍 Bing Search API:")
    print(f"   1. 访问: https://www.microsoft.com/en-us/bing/apis/bing-web-search-api")
    print(f"   2. 创建Azure账户并订阅Bing Search API")
    print(f"   3. 获取订阅密钥")
    print(f"   4. 设置环境变量:")
    print(f"      BING_SEARCH_API_KEY=your_api_key")
    
    print(f"\n⚙️ 配置方法:")
    print(f"   方法1: 运行配置工具")
    print(f"          python search_api_config.py")
    print(f"   方法2: 手动创建.env文件")
    print(f"          复制.env.template为.env并填入密钥")
    print(f"   方法3: 设置系统环境变量")

def demo_search_enhancement_features():
    """演示搜索增强功能特性"""
    print(f"\n✨ 搜索增强功能特性")
    print("=" * 50)
    
    features = [
        ("🧠 智能缺口识别", "自动分析报告内容，识别需要补充的信息类型"),
        ("🔍 多源搜索", "支持Google Search和Bing Search API"),
        ("🔒 质量控制", "对搜索结果进行相关性、权威性、时效性验证"),
        ("🤖 智能整合", "使用Gemini AI将搜索结果智能整合到报告中"),
        ("👤 用户确认", "提供详细的缺口分析和用户确认机制"),
        ("📊 增强报告", "生成包含最新信息的增强版报告"),
        ("⚡ 高效处理", "批量搜索和并行处理提高效率"),
        ("🛡️ 错误恢复", "完善的异常处理和错误恢复机制")
    ]
    
    for feature, description in features:
        print(f"   {feature}: {description}")
    
    print(f"\n📋 支持的信息类型:")
    gap_types = [
        ("最新数据", "2024-2025年的最新统计数据和发展动态"),
        ("市场分析", "市场规模、竞争格局、行业分析"),
        ("技术发展", "最新技术突破、创新和发展趋势"),
        ("政策法规", "相关政策、法规、标准和监管信息"),
        ("案例研究", "实际应用案例、项目和实践经验")
    ]
    
    for gap_type, description in gap_types:
        print(f"   • {gap_type}: {description}")

def cleanup_demo_data():
    """清理演示数据"""
    try:
        demo_dir = Path("demo_search_data")
        if demo_dir.exists():
            import shutil
            shutil.rmtree(demo_dir)
            print(f"\n🧹 演示数据已清理")
    except Exception as e:
        print(f"\n⚠️ 清理演示数据失败: {str(e)}")

def main():
    """主演示函数"""
    print("🎯 AI报告生成器 - 搜索增强功能演示")
    print("=" * 60)
    
    print("本演示将展示搜索增强功能的完整流程，包括:")
    print("   1. 内容缺口分析")
    print("   2. 用户确认界面 (y/n选择)")
    print("   3. 搜索API调用")
    print("   4. 智能内容整合")
    print("   5. 增强报告生成")
    
    # 检查API配置状态
    google_api = os.getenv('GOOGLE_SEARCH_API_KEY')
    google_cx = os.getenv('GOOGLE_SEARCH_CX')
    bing_api = os.getenv('BING_SEARCH_API_KEY')
    
    api_configured = (google_api and google_cx) or bing_api
    
    if not api_configured:
        print(f"\n⚠️ 注意: 搜索API未配置")
        print(f"   演示将展示完整流程，但无法进行实际搜索")
        print(f"   配置API后可体验完整功能")
    
    print(f"\n🚀 开始演示...")
    
    try:
        # 演示主要功能
        demo_search_enhancement_with_user_interaction()
        
        # 显示功能特性
        demo_search_enhancement_features()
        
        # 显示配置指南
        if not api_configured:
            demo_api_configuration_guide()
        
        print(f"\n🎉 演示完成!")
        print(f"\n💡 要体验完整功能，请:")
        print(f"   1. 配置搜索API: python search_api_config.py")
        print(f"   2. 重新运行演示: python demo_search_enhancement.py")
        
    except KeyboardInterrupt:
        print(f"\n\n❌ 用户中断演示")
        cleanup_demo_data()
    except Exception as e:
        print(f"\n❌ 演示过程出错: {str(e)}")
        cleanup_demo_data()

if __name__ == "__main__":
    main()
