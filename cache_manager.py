"""
缓存管理工具
用于管理AI报告生成器的文件处理缓存
"""
import os
import sys
import json
import shutil
from pathlib import Path
from datetime import datetime
import argparse

class CacheManager:
    """缓存管理器"""
    
    def __init__(self):
        self.cache_dirs = []
        self.total_cache_size = 0
        self.total_cache_files = 0
    
    def scan_caches(self, root_dir="."):
        """扫描指定目录下的所有缓存"""
        root_path = Path(root_dir)
        self.cache_dirs = []
        self.total_cache_size = 0
        self.total_cache_files = 0
        
        print(f"🔍 扫描缓存目录: {root_path.absolute()}")
        
        for processed_dir in root_path.rglob("processed"):
            if processed_dir.is_dir():
                cache_info = self._analyze_cache(processed_dir)
                if cache_info:
                    self.cache_dirs.append(cache_info)
                    self.total_cache_size += cache_info["size"]
                    self.total_cache_files += cache_info["file_count"]
        
        print(f"✅ 发现 {len(self.cache_dirs)} 个缓存目录")
        print(f"📊 总缓存大小: {self._format_size(self.total_cache_size)}")
        print(f"📄 总缓存文件: {self.total_cache_files} 个")
        
        return self.cache_dirs
    
    def _analyze_cache(self, processed_dir):
        """分析单个缓存目录"""
        try:
            cache_info = {
                "path": processed_dir,
                "parent": processed_dir.parent,
                "size": 0,
                "file_count": 0,
                "created_time": None,
                "files": []
            }
            
            # 计算缓存大小和文件数量
            for file_path in processed_dir.rglob("*"):
                if file_path.is_file():
                    cache_info["size"] += file_path.stat().st_size
                    cache_info["file_count"] += 1
                    cache_info["files"].append(file_path.name)
            
            # 读取处理信息
            info_file = processed_dir / "processing_info.json"
            if info_file.exists():
                with open(info_file, 'r', encoding='utf-8') as f:
                    info = json.load(f)
                cache_info["created_time"] = info.get("processed_time")
                cache_info["processed_files"] = info.get("total_files", 0)
                cache_info["cache_version"] = info.get("cache_version", "1.0")
                cache_info["has_image_index"] = bool(info.get("image_index"))

            # 检查图片索引
            image_index_dir = processed_dir / "image_index"
            if image_index_dir.exists():
                cache_info["image_index_dir"] = image_index_dir

                # 读取图片索引信息
                index_file = image_index_dir / "image_index.json"
                if index_file.exists():
                    try:
                        with open(index_file, 'r', encoding='utf-8') as f:
                            img_index = json.load(f)
                        cache_info["image_count"] = len(img_index.get("images", {}))
                        cache_info["image_index_size"] = index_file.stat().st_size
                    except:
                        cache_info["image_count"] = 0

            return cache_info
            
        except Exception as e:
            print(f"⚠️ 分析缓存失败 {processed_dir}: {str(e)}")
            return None
    
    def list_caches(self):
        """列出所有缓存"""
        if not self.cache_dirs:
            print("❌ 未发现任何缓存目录")
            return
        
        print(f"\n📋 缓存目录列表:")
        print("=" * 80)
        
        for i, cache_info in enumerate(self.cache_dirs, 1):
            print(f"\n{i}. {cache_info['parent'].name}")
            print(f"   📁 路径: {cache_info['path']}")
            print(f"   📊 大小: {self._format_size(cache_info['size'])}")
            print(f"   📄 文件: {cache_info['file_count']} 个")
            
            if cache_info['created_time']:
                try:
                    created = datetime.fromisoformat(cache_info['created_time'])
                    print(f"   🕒 创建: {created.strftime('%Y-%m-%d %H:%M:%S')}")
                except:
                    print(f"   🕒 创建: {cache_info['created_time']}")
            
            if 'processed_files' in cache_info:
                print(f"   📋 处理: {cache_info['processed_files']} 个源文件")

            # 显示缓存版本和图片信息
            if 'cache_version' in cache_info:
                print(f"   🔖 版本: {cache_info['cache_version']}")

            if cache_info.get('has_image_index'):
                image_count = cache_info.get('image_count', 0)
                print(f"   🖼️ 图片索引: {image_count} 个图片")

            # 显示缓存文件
            cache_files = cache_info['files'][:5]  # 只显示前5个
            if cache_files:
                print(f"   📄 缓存文件: {', '.join(cache_files)}")
                if len(cache_info['files']) > 5:
                    print(f"              ... 还有 {len(cache_info['files']) - 5} 个文件")
    
    def clean_cache(self, cache_index=None, confirm=True):
        """清理缓存"""
        if not self.cache_dirs:
            print("❌ 未发现任何缓存目录")
            return False
        
        if cache_index is not None:
            # 清理指定缓存
            if 1 <= cache_index <= len(self.cache_dirs):
                cache_info = self.cache_dirs[cache_index - 1]
                return self._clean_single_cache(cache_info, confirm)
            else:
                print(f"❌ 无效的缓存索引: {cache_index}")
                return False
        else:
            # 清理所有缓存
            return self._clean_all_caches(confirm)
    
    def _clean_single_cache(self, cache_info, confirm=True):
        """清理单个缓存"""
        cache_path = cache_info['path']
        cache_size = self._format_size(cache_info['size'])
        
        print(f"\n🗑️ 准备清理缓存:")
        print(f"   📁 路径: {cache_path}")
        print(f"   📊 大小: {cache_size}")
        print(f"   📄 文件: {cache_info['file_count']} 个")
        
        if confirm:
            response = input(f"\n❓ 确认清理此缓存? (y/N): ").strip().lower()
            if response != 'y':
                print("❌ 取消清理")
                return False
        
        try:
            shutil.rmtree(cache_path)
            print(f"✅ 缓存清理完成: {cache_path}")
            return True
        except Exception as e:
            print(f"❌ 清理失败: {str(e)}")
            return False
    
    def _clean_all_caches(self, confirm=True):
        """清理所有缓存"""
        total_size = self._format_size(self.total_cache_size)
        
        print(f"\n🗑️ 准备清理所有缓存:")
        print(f"   📁 目录数: {len(self.cache_dirs)} 个")
        print(f"   📊 总大小: {total_size}")
        print(f"   📄 总文件: {self.total_cache_files} 个")
        
        if confirm:
            response = input(f"\n❓ 确认清理所有缓存? (y/N): ").strip().lower()
            if response != 'y':
                print("❌ 取消清理")
                return False
        
        success_count = 0
        for cache_info in self.cache_dirs:
            try:
                shutil.rmtree(cache_info['path'])
                success_count += 1
                print(f"✅ 已清理: {cache_info['path']}")
            except Exception as e:
                print(f"❌ 清理失败 {cache_info['path']}: {str(e)}")
        
        print(f"\n📊 清理结果: {success_count}/{len(self.cache_dirs)} 个缓存清理成功")
        return success_count == len(self.cache_dirs)
    
    def cache_stats(self):
        """显示缓存统计信息"""
        if not self.cache_dirs:
            print("❌ 未发现任何缓存目录")
            return
        
        print(f"\n📊 缓存统计信息:")
        print("=" * 50)
        print(f"缓存目录数量: {len(self.cache_dirs)}")
        print(f"总缓存大小: {self._format_size(self.total_cache_size)}")
        print(f"总缓存文件: {self.total_cache_files}")
        print(f"平均缓存大小: {self._format_size(self.total_cache_size / len(self.cache_dirs))}")
        
        # 按大小排序
        sorted_caches = sorted(self.cache_dirs, key=lambda x: x['size'], reverse=True)
        
        print(f"\n🔝 最大的5个缓存:")
        for i, cache_info in enumerate(sorted_caches[:5], 1):
            print(f"   {i}. {cache_info['parent'].name}: {self._format_size(cache_info['size'])}")
    
    def _format_size(self, size_bytes):
        """格式化文件大小"""
        if size_bytes < 1024:
            return f"{size_bytes} B"
        elif size_bytes < 1024 * 1024:
            return f"{size_bytes / 1024:.1f} KB"
        elif size_bytes < 1024 * 1024 * 1024:
            return f"{size_bytes / (1024 * 1024):.1f} MB"
        else:
            return f"{size_bytes / (1024 * 1024 * 1024):.1f} GB"

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="AI报告生成器缓存管理工具")
    parser.add_argument("command", choices=["scan", "list", "clean", "stats"], 
                       help="操作命令")
    parser.add_argument("--dir", default=".", help="扫描目录 (默认: 当前目录)")
    parser.add_argument("--index", type=int, help="缓存索引 (用于clean命令)")
    parser.add_argument("--force", action="store_true", help="强制执行，不询问确认")
    
    args = parser.parse_args()
    
    print("🗂️ AI报告生成器 - 缓存管理工具")
    print("=" * 60)
    
    manager = CacheManager()
    
    if args.command == "scan":
        manager.scan_caches(args.dir)
    
    elif args.command == "list":
        manager.scan_caches(args.dir)
        manager.list_caches()
    
    elif args.command == "clean":
        manager.scan_caches(args.dir)
        if manager.cache_dirs:
            manager.clean_cache(args.index, not args.force)
        else:
            print("❌ 未发现任何缓存目录")
    
    elif args.command == "stats":
        manager.scan_caches(args.dir)
        manager.cache_stats()

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n❌ 用户中断操作")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 程序错误: {str(e)}")
        sys.exit(1)
