#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
内容整合器模块
将搜索结果整合到报告中
"""

from typing import List, Dict, Any


class ContentIntegrator:
    """内容整合器"""
    
    def __init__(self, generator):
        self.generator = generator
    
    def integrate_search_results(self, 
                                framework: Dict[str, Any],
                                search_results: List[Dict[str, Any]],
                                topic: str) -> Dict[str, Any]:
        """将搜索结果整合到报告框架中"""
        
        # 为每个搜索结果找到最适合的章节
        for result in search_results:
            best_section = self._find_best_section(framework, result, topic)
            if best_section:
                self._integrate_result_to_section(best_section, result)
        
        return framework
    
    def _find_best_section(self, 
                          framework: Dict[str, Any], 
                          result: Dict[str, Any],
                          topic: str) -> Dict[str, Any]:
        """找到最适合的章节"""
        best_section = None
        best_score = 0.0
        
        sections = framework.get('sections', [])
        
        for section in sections:
            score = self._calculate_section_match_score(section, result)
            if score > best_score:
                best_score = score
                best_section = section
            
            # 递归检查子章节
            if 'children' in section:
                child_section = self._find_best_section_in_children(
                    section['children'], result
                )
                if child_section:
                    child_score = self._calculate_section_match_score(child_section, result)
                    if child_score > best_score:
                        best_score = child_score
                        best_section = child_section
        
        return best_section if best_score > 0.3 else None
    
    def _find_best_section_in_children(self, 
                                      children: List[Dict[str, Any]], 
                                      result: Dict[str, Any]) -> Dict[str, Any]:
        """在子章节中找到最适合的章节"""
        best_section = None
        best_score = 0.0
        
        for child in children:
            score = self._calculate_section_match_score(child, result)
            if score > best_score:
                best_score = score
                best_section = child
            
            # 递归检查更深层的子章节
            if 'children' in child:
                deeper_section = self._find_best_section_in_children(
                    child['children'], result
                )
                if deeper_section:
                    deeper_score = self._calculate_section_match_score(deeper_section, result)
                    if deeper_score > best_score:
                        best_score = deeper_score
                        best_section = deeper_section
        
        return best_section
    
    def _calculate_section_match_score(self, 
                                      section: Dict[str, Any], 
                                      result: Dict[str, Any]) -> float:
        """计算章节匹配评分"""
        section_title = section.get('title', '').lower()
        result_title = result.get('title', '').lower()
        result_snippet = result.get('snippet', '').lower()
        
        score = 0.0
        
        # 标题关键词匹配
        section_keywords = section_title.split()
        for keyword in section_keywords:
            if keyword in result_title:
                score += 0.3
            if keyword in result_snippet:
                score += 0.1
        
        # 特定主题匹配
        if '市场' in section_title and ('市场' in result_title or '市场' in result_snippet):
            score += 0.4
        if '技术' in section_title and ('技术' in result_title or '技术' in result_snippet):
            score += 0.4
        if '政策' in section_title and ('政策' in result_title or '政策' in result_snippet):
            score += 0.4
        if '竞争' in section_title and ('竞争' in result_title or '竞争' in result_snippet):
            score += 0.4
        
        return score
    
    def _integrate_result_to_section(self, 
                                   section: Dict[str, Any], 
                                   result: Dict[str, Any]):
        """将搜索结果整合到章节中"""
        
        # 确保章节有搜索增强内容字段
        if 'search_enhanced_content' not in section:
            section['search_enhanced_content'] = []
        
        # 添加搜索结果
        enhanced_content = {
            'title': result.get('title', ''),
            'snippet': result.get('snippet', ''),
            'url': result.get('url', ''),
            'source': result.get('source', ''),
            'relevance_score': result.get('relevance_score', 0),
            'date': result.get('date', '')
        }
        
        section['search_enhanced_content'].append(enhanced_content)
    
    def generate_enhanced_content(self, section: Dict[str, Any]) -> str:
        """生成增强后的章节内容"""
        original_content = section.get('content', '')
        search_content = section.get('search_enhanced_content', [])
        
        if not search_content:
            return original_content
        
        # 构建增强内容
        enhanced_parts = [original_content] if original_content else []
        
        # 添加搜索增强信息
        enhanced_parts.append("\n\n## 最新信息补充\n")
        
        for item in search_content[:3]:  # 只取前3个最相关的结果
            title = item.get('title', '')
            snippet = item.get('snippet', '')
            source = item.get('source', '')
            
            enhanced_parts.append(f"### {title}\n")
            enhanced_parts.append(f"{snippet}\n")
            enhanced_parts.append(f"*来源: {source}*\n")
        
        return "\n".join(enhanced_parts)
    
    def apply_search_enhancement(self, framework: Dict[str, Any]) -> Dict[str, Any]:
        """应用搜索增强到整个框架"""
        self._apply_enhancement_to_sections(framework.get('sections', []))
        return framework
    
    def _apply_enhancement_to_sections(self, sections: List[Dict[str, Any]]):
        """递归应用搜索增强到所有章节"""
        for section in sections:
            if 'search_enhanced_content' in section:
                section['content'] = self.generate_enhanced_content(section)
            
            # 递归处理子章节
            if 'children' in section:
                self._apply_enhancement_to_sections(section['children'])
