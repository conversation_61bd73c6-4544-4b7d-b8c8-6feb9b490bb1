from _typeshed import ReadableBuffer
from codecs import _ReadableStream, _WritableStream
from collections.abc import Iterable
from typing import final, type_check_only

# This class is not exposed. It calls itself _multibytecodec.MultibyteCodec.
@final
@type_check_only
class _MultibyteCodec:
    def decode(self, input: ReadableBuffer, errors: str | None = None) -> str: ...
    def encode(self, input: str, errors: str | None = None) -> bytes: ...

class MultibyteIncrementalDecoder:
    errors: str
    def __init__(self, errors: str = "strict") -> None: ...
    def decode(self, input: ReadableBuffer, final: bool = False) -> str: ...
    def getstate(self) -> tuple[bytes, int]: ...
    def reset(self) -> None: ...
    def setstate(self, state: tuple[bytes, int], /) -> None: ...

class MultibyteIncrementalEncoder:
    errors: str
    def __init__(self, errors: str = "strict") -> None: ...
    def encode(self, input: str, final: bool = False) -> bytes: ...
    def getstate(self) -> int: ...
    def reset(self) -> None: ...
    def setstate(self, state: int, /) -> None: ...

class MultibyteStreamReader:
    errors: str
    stream: _ReadableStream
    def __init__(self, stream: _ReadableStream, errors: str = "strict") -> None: ...
    def read(self, sizeobj: int | None = None, /) -> str: ...
    def readline(self, sizeobj: int | None = None, /) -> str: ...
    def readlines(self, sizehintobj: int | None = None, /) -> list[str]: ...
    def reset(self) -> None: ...

class MultibyteStreamWriter:
    errors: str
    stream: _WritableStream
    def __init__(self, stream: _WritableStream, errors: str = "strict") -> None: ...
    def reset(self) -> None: ...
    def write(self, strobj: str, /) -> None: ...
    def writelines(self, lines: Iterable[str], /) -> None: ...
