"""
API轮询管理器模块 - 严格按照原始代码的APIRotator实现
处理多个API密钥的负载均衡和轮询
"""

import asyncio
import threading
from typing import List, Dict, Any, Optional
from datetime import datetime, timedelta


class APIRotator:
    """API轮询器（同步版本）- 与原始代码完全相同"""
    
    def __init__(self, api_keys: List[str]):
        """
        初始化API轮询器
        
        Args:
            api_keys: API密钥列表
        """
        self.api_keys = api_keys
        self.current_index = 0
        self.usage_count = {key: 0 for key in api_keys}
        self.last_used = {key: None for key in api_keys}
        self.lock = threading.Lock()  # 同步版本使用线程锁
    
    def get_next_key(self) -> str:
        """获取下一个可用的API密钥 - 与原始代码完全相同"""
        with self.lock:
            if not self.api_keys:
                raise ValueError("No API keys available")
            
            # 简单的轮询策略
            key = self.api_keys[self.current_index]
            self.current_index = (self.current_index + 1) % len(self.api_keys)
            
            # 更新使用统计
            self.usage_count[key] += 1
            self.last_used[key] = datetime.now()
            
            return key
    
    def get_least_used_key(self) -> str:
        """获取使用次数最少的API密钥 - 与原始代码完全相同"""
        with self.lock:
            if not self.api_keys:
                raise ValueError("No API keys available")
            
            # 找出使用次数最少的密钥
            min_usage = min(self.usage_count.values())
            for key in self.api_keys:
                if self.usage_count[key] == min_usage:
                    self.usage_count[key] += 1
                    self.last_used[key] = datetime.now()
                    return key
            
            return self.api_keys[0]
    
    def get_usage_stats(self) -> Dict[str, Any]:
        """获取API使用统计 - 与原始代码完全相同"""
        with self.lock:
            stats = {
                "total_keys": len(self.api_keys),
                "usage_count": self.usage_count.copy(),
                "last_used": {k: v.isoformat() if v else None 
                             for k, v in self.last_used.items()}
            }
            return stats


class AsyncAPIRotator:
    """异步API轮询器 - 与原始代码完全相同"""
    
    def __init__(self, api_keys: List[str]):
        """
        初始化异步API轮询器
        
        Args:
            api_keys: API密钥列表
        """
        self.api_keys = api_keys
        self.current_index = 0
        self.usage_count = {key: 0 for key in api_keys}
        self.last_used = {key: None for key in api_keys}
        self.lock = asyncio.Lock()
    
    async def get_next_key(self) -> str:
        """异步获取下一个可用的API密钥 - 与原始代码完全相同"""
        async with self.lock:
            if not self.api_keys:
                raise ValueError("No API keys available")
            
            # 简单的轮询策略
            key = self.api_keys[self.current_index]
            self.current_index = (self.current_index + 1) % len(self.api_keys)
            
            # 更新使用统计
            self.usage_count[key] += 1
            self.last_used[key] = datetime.now()
            
            return key
    
    async def get_least_used_key(self) -> str:
        """异步获取使用次数最少的API密钥 - 与原始代码完全相同"""
        async with self.lock:
            if not self.api_keys:
                raise ValueError("No API keys available")
            
            # 找出使用次数最少的密钥
            min_usage = min(self.usage_count.values())
            for key in self.api_keys:
                if self.usage_count[key] == min_usage:
                    self.usage_count[key] += 1
                    self.last_used[key] = datetime.now()
                    return key
            
            return self.api_keys[0]
    
    async def get_usage_stats(self) -> Dict[str, Any]:
        """异步获取API使用统计 - 与原始代码完全相同"""
        async with self.lock:
            stats = {
                "total_keys": len(self.api_keys),
                "usage_count": self.usage_count.copy(),
                "last_used": {k: v.isoformat() if v else None 
                             for k, v in self.last_used.items()}
            }
            return stats
