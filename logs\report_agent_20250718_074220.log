{"node": "setup_and_prepare_sources", "event": "\u8282\u70b9\u5f00\u59cb\u6267\u884c", "logger": "src.logger", "level": "info", "timestamp": "2025-07-17T23:42:20.380980Z"}
{"node": "setup_and_prepare_sources", "duration_seconds": 0.00116729736328125, "event": "\u8282\u70b9\u6267\u884c\u5b8c\u6210", "logger": "src.logger", "level": "info", "timestamp": "2025-07-17T23:42:20.383181Z"}
{"node": "design_framework", "event": "\u8282\u70b9\u5f00\u59cb\u6267\u884c", "logger": "src.logger", "level": "info", "timestamp": "2025-07-17T23:42:20.385604Z"}
{"model": "gemini-2.5-pro", "operation": "orchestrator_json", "success": false, "duration_seconds": 356.54005670547485, "input_tokens": 0, "output_tokens": 0, "total_tokens": 0, "estimated_cost_usd": 0.0, "error": "\u65e0\u6cd5\u4fee\u590dJSON\u54cd\u5e94: Expecting value: line 1 column 1 (char 0)", "event": "API\u8c03\u7528\u5931\u8d25", "logger": "src.logger", "level": "error", "timestamp": "2025-07-17T23:48:16.926651Z"}
{"model": "gemini-2.5-pro", "operation": "orchestrator_json", "success": false, "duration_seconds": 512.334246635437, "input_tokens": 0, "output_tokens": 0, "total_tokens": 0, "estimated_cost_usd": 0.0, "error": "\u65e0\u6cd5\u4fee\u590dJSON\u54cd\u5e94: 429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {\n  quota_metric: \"generativelanguage.googleapis.com/generate_content_free_tier_requests\"\n  quota_id: \"GenerateRequestsPerDayPerProjectPerModel-FreeTier\"\n  quota_dimensions {\n    key: \"model\"\n    value: \"gemini-2.5-pro\"\n  }\n  quota_dimensions {\n    key: \"location\"\n    value: \"global\"\n  }\n  quota_value: 100\n}\n, links {\n  description: \"Learn more about Gemini API quotas\"\n  url: \"https://ai.google.dev/gemini-api/docs/rate-limits\"\n}\n, retry_delay {\n  seconds: 11\n}\n]", "event": "API\u8c03\u7528\u5931\u8d25", "logger": "src.logger", "level": "error", "timestamp": "2025-07-17T23:56:51.262610Z"}
{"model": "gemini-2.5-pro", "operation": "orchestrator_json", "success": false, "duration_seconds": 0.3285660743713379, "input_tokens": 0, "output_tokens": 0, "total_tokens": 0, "estimated_cost_usd": 0.0, "error": "429 You exceeded your current quota, please check your plan and billing details. For more information on this error, head to: https://ai.google.dev/gemini-api/docs/rate-limits. [violations {\n  quota_metric: \"generativelanguage.googleapis.com/generate_content_free_tier_requests\"\n  quota_id: \"GenerateRequestsPerDayPerProjectPerModel-FreeTier\"\n  quota_dimensions {\n    key: \"model\"\n    value: \"gemini-2.5-pro\"\n  }\n  quota_dimensions {\n    key: \"location\"\n    value: \"global\"\n  }\n  quota_value: 100\n}\n, links {\n  description: \"Learn more about Gemini API quotas\"\n  url: \"https://ai.google.dev/gemini-api/docs/rate-limits\"\n}\n, retry_delay {\n  seconds: 7\n}\n]", "event": "API\u8c03\u7528\u5931\u8d25", "logger": "src.logger", "level": "error", "timestamp": "2025-07-17T23:56:55.593617Z"}
{"node": "design_framework", "error": "RetryError[<Future at 0x241ee8cc080 state=finished raised ResourceExhausted>]", "event": "\u8282\u70b9\u6267\u884c\u9519\u8bef", "logger": "src.logger", "level": "error", "timestamp": "2025-07-17T23:56:55.594168Z"}
