"""
LangGraph工作流定义模块
使用LangGraph定义状态机工作流
"""
import json
from typing import Dict, Any, List, Optional
from pathlib import Path
from datetime import datetime

from langgraph.graph import StateGraph, END
from langgraph.checkpoint.memory import MemorySaver

from .state import ReportState
from .config import Config
from .logger import ProcessLogger, setup_logger, APICallLogger
from .llm_client import LLMClient
from .context_manager import ContextManager
from .workflow_nodes import WorkflowNodes
from .word_generator import WordGenerator


class ReportWorkflowBuilder:
    """
    报告工作流构建器
    使用LangGraph构建完整的状态机工作流
    """
    
    def __init__(self, config: Config):
        self.config = config
        
        # 初始化日志
        self.logger = setup_logger(config.logging.dict())
        self.process_logger = ProcessLogger(self.logger)
        self.api_logger = APICallLogger(self.logger, config.logging.dict())
        
        # 初始化组件
        self.llm_client = LLMClient(config, self.api_logger)
        self.context_manager = ContextManager(config, self.process_logger)
        self.workflow_nodes = WorkflowNodes(
            config, 
            self.llm_client, 
            self.context_manager, 
            self.process_logger
        )
        self.word_generator = WordGenerator(config, self.process_logger)
        
        # 检查点保存器
        self.checkpointer = MemorySaver() if config.persistence.enabled else None
    
    def build_workflow(self) -> StateGraph:
        """
        构建LangGraph工作流
        """
        # 创建状态图
        workflow = StateGraph(ReportState)
        
        # 添加节点
        workflow.add_node("setup_sources", self._setup_sources_node)
        workflow.add_node("design_framework", self._design_framework_node)
        workflow.add_node("generate_content", self._generate_content_node)
        workflow.add_node("refine_content", self._refine_content_node)
        workflow.add_node("final_review", self._final_review_node)
        workflow.add_node("generate_document", self._generate_document_node)
        workflow.add_node("save_checkpoint", self._save_checkpoint_node)
        
        # 定义边（工作流程）
        workflow.set_entry_point("setup_sources")
        workflow.add_edge("setup_sources", "design_framework")
        workflow.add_edge("design_framework", "generate_content")
        workflow.add_edge("generate_content", "refine_content")
        
        # 条件边：判断是否继续迭代
        workflow.add_conditional_edges(
            "refine_content",
            self._should_continue_iteration,
            {
                "continue": "save_checkpoint",
                "complete": "final_review"
            }
        )
        
        workflow.add_edge("save_checkpoint", "generate_content")
        workflow.add_edge("final_review", "generate_document")
        workflow.add_edge("generate_document", END)
        
        return workflow.compile(checkpointer=self.checkpointer)
    
    def _setup_sources_node(self, state: ReportState) -> ReportState:
        """设置和准备数据源"""
        return self.workflow_nodes.setup_and_prepare_sources(state)
    
    def _design_framework_node(self, state: ReportState) -> ReportState:
        """设计报告框架"""
        return self.workflow_nodes.design_framework(state)
    
    def _generate_content_node(self, state: ReportState) -> ReportState:
        """生成内容（自上而下）"""
        return self.workflow_nodes.generate_content_top_down(state)
    
    def _refine_content_node(self, state: ReportState) -> ReportState:
        """优化内容（自下而上）"""
        return self.workflow_nodes.review_and_refine_bottom_up(state)
    
    def _final_review_node(self, state: ReportState) -> ReportState:
        """最终审核"""
        return self.workflow_nodes.final_review_and_structured_output(state)
    
    def _generate_document_node(self, state: ReportState) -> ReportState:
        """生成Word文档"""
        output_file = f"{state.topic}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.docx"
        output_path = Path(self.config.paths.output_dir) / output_file
        
        document_path = self.word_generator.generate_word_document(
            state, 
            str(output_path)
        )
        
        state.last_checkpoint = document_path
        state.update_phase("completed")
        
        return state
    
    def _save_checkpoint_node(self, state: ReportState) -> ReportState:
        """保存检查点"""
        if self.config.persistence.enabled:
            checkpoint_data = state.to_checkpoint_dict()
            checkpoint_file = Path(self.config.persistence.checkpoint_dir) / f"checkpoint_{state.current_iteration}.json"
            checkpoint_file.parent.mkdir(parents=True, exist_ok=True)
            
            with open(checkpoint_file, 'w', encoding='utf-8') as f:
                json.dump(checkpoint_data, f, ensure_ascii=False, indent=2)
            
            state.last_checkpoint = str(checkpoint_file)
            self.process_logger.log_state_checkpoint(str(checkpoint_file))
        
        return state
    
    def _should_continue_iteration(self, state: ReportState) -> str:
        """判断是否应该继续迭代"""
        if state.should_continue_iteration():
            return "continue"
        else:
            return "complete"
    
    def restore_from_checkpoint(self, checkpoint_path: str) -> ReportState:
        """从检查点恢复状态"""
        with open(checkpoint_path, 'r', encoding='utf-8') as f:
            checkpoint_data = json.load(f)
        
        return ReportState.from_checkpoint_dict(checkpoint_data)
