"""
配置管理模块
负责加载和管理系统配置
"""
import os
import yaml
from pathlib import Path
from typing import Dict, Any, List
from pydantic import BaseModel, Field, validator
from pydantic_settings import BaseSettings


class APIConfig(BaseModel):
    """API配置"""
    api_key: str
    orchestrator_model: str = "gemini-2.5-pro"
    executor_model: str = "gemini-2.5-flash"


class RetryConfig(BaseModel):
    """重试配置"""
    max_attempts: int = 3
    initial_wait_seconds: int = 2
    backoff_factor: int = 2


class RateLimitingConfig(BaseModel):
    """速率限制配置"""
    max_tokens_per_minute: int = 200000
    max_tokens_per_request: int = 50000
    batch_delay_seconds: int = 15


class GenerationConfig(BaseModel):
    """生成参数配置"""
    temperature: float = 0.7
    top_p: float = 0.95
    top_k: int = 40
    max_output_tokens: int = 1000000


class PDFConfig(BaseModel):
    """PDF处理配置"""
    dpi: int = 150
    extract_images: bool = True
    extract_tables: bool = True
    table_extraction_method: str = "lattice"


class DocumentProcessingConfig(BaseModel):
    """文档处理配置"""
    pdf: PDFConfig
    supported_formats: List[str] = [".pdf", ".docx", ".txt", ".md", ".xlsx", ".pptx"]


class ReportConfig(BaseModel):
    """报告结构配置"""
    num_top_level_sections: int = 8
    max_heading_depth: int = 6
    citation_pattern: str = r"\[来源:\s*([^\]]+)\]"


class PerformanceConfig(BaseModel):
    """性能配置"""
    max_memory_gb: int = 16
    chunk_size: int = 1000000
    concurrent_file_processing: int = 4


class LoggingConfig(BaseModel):
    """日志配置"""
    level: str = "INFO"
    format: str = "structured"
    log_api_costs: bool = True
    log_token_usage: bool = True


class PersistenceConfig(BaseModel):
    """持久化配置"""
    enabled: bool = True
    checkpoint_dir: str = "./checkpoints"
    save_interval_minutes: int = 10


class WordDocumentConfig(BaseModel):
    """Word文档输出配置"""
    font_family: str = "Arial"
    font_size_body: int = 11
    font_size_heading_1: int = 16
    font_size_heading_2: int = 14
    font_size_heading_3: int = 12
    font_size_heading_4: int = 11
    font_size_heading_5: int = 11
    font_size_heading_6: int = 11
    line_spacing: float = 1.5


class OutputConfig(BaseModel):
    """输出配置"""
    word_document: WordDocumentConfig


class PathsConfig(BaseModel):
    """路径配置"""
    templates_dir: str = "./templates"
    output_dir: str = "./output"
    temp_dir: str = "./temp"


class Config(BaseModel):
    """主配置类"""
    api: APIConfig
    retry: RetryConfig
    rate_limiting: RateLimitingConfig
    generation: GenerationConfig
    document_processing: DocumentProcessingConfig
    report: ReportConfig
    performance: PerformanceConfig
    logging: LoggingConfig
    persistence: PersistenceConfig
    output: OutputConfig
    paths: PathsConfig

    @classmethod
    def from_yaml(cls, config_path: str = "config.yaml") -> "Config":
        """从YAML文件加载配置"""
        # 加载环境变量
        from dotenv import load_dotenv
        load_dotenv()
        
        config_file = Path(config_path)
        if not config_file.exists():
            raise FileNotFoundError(f"配置文件不存在: {config_path}")
        
        with open(config_file, "r", encoding="utf-8") as f:
            config_data = yaml.safe_load(f)
        
        # 从环境变量覆盖API密钥（如果存在）
        api_config = config_data.get("api", {}).get("google", {})
        env_api_key = os.getenv("GEMINI_API_KEY")
        if env_api_key:
            api_config["api_key"] = env_api_key
        
        # 处理嵌套配置
        return cls(
            api=APIConfig(**api_config),
            retry=RetryConfig(**config_data.get("retry", {})),
            rate_limiting=RateLimitingConfig(**config_data.get("rate_limiting", {})),
            generation=GenerationConfig(**config_data.get("generation", {})),
            document_processing=DocumentProcessingConfig(**config_data.get("document_processing", {})),
            report=ReportConfig(**config_data.get("report", {})),
            performance=PerformanceConfig(**config_data.get("performance", {})),
            logging=LoggingConfig(**config_data.get("logging", {})),
            persistence=PersistenceConfig(**config_data.get("persistence", {})),
            output=OutputConfig(**config_data.get("output", {})),
            paths=PathsConfig(**config_data.get("paths", {}))
        )

    def ensure_directories(self):
        """确保所有必要的目录存在"""
        directories = [
            self.paths.templates_dir,
            self.paths.output_dir,
            self.paths.temp_dir,
            self.persistence.checkpoint_dir
        ]
        for directory in directories:
            Path(directory).mkdir(parents=True, exist_ok=True)
