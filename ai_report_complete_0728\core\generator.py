"""
完整报告生成器 - 修复版本
完全按照源代码逻辑重新实现，解决所有编码和逻辑问题
"""

import time
import asyncio
from datetime import datetime
from pathlib import Path
from typing import List, Dict, Any, Optional

# 导入所有必要的组件
from api.gemini_manager_new import GeminiAPIManager, AsyncGeminiAPIManager
from core.config import ReportConfig
from utils.token_manager import TokenManager
from content.content_cleaner import ContentCleaner
from search.search_trigger import SearchTrigger
from search.search_manager import SearchManager
from search.content_validator import ContentValidator
from search.content_integrator import ContentIntegrator
from core.optimization import ReportOptimizer
from utils.checkpoint_manager import CheckpointManager
from image.image_processor import ImageProcessor

# API配置
API_KEYS = [
    "AIzaSyBQsINJgGJJl7dJJJJJJJJJJJJJJJJJJJJ",  # 示例密钥
]

MODEL_NAMES = {
    "orchestrator": "gemini-2.0-flash-exp",
    "executor": "gemini-2.0-flash-exp"
}

# API管理器需要的模型列表
MODEL_LIST = ["gemini-2.0-flash-exp"]


class CompleteReportGenerator:
    """完整报告生成器 - 修复版本"""
    
    def __init__(self, use_async: bool = False, max_tokens: int = 8000000):
        """初始化生成器"""
        print(f"🚀 初始化报告生成器 (异步模式: {use_async})")
        
        # 基础配置
        self.use_async = use_async
        self.max_tokens = max_tokens
        self.current_checkpoint_id = None
        
        # 模型配置
        self.ORCHESTRATOR_MODEL = MODEL_NAMES["orchestrator"]
        self.EXECUTOR_MODEL = MODEL_NAMES["executor"]
        
        # 报告配置
        self.report_config = {
            "target_words": 50000,
            "enable_image_embedding": True,
            "enable_search_enhancement": True,
            "max_depth": 3
        }
        
        # 初始化配置
        self.config = ReportConfig(use_async=use_async, max_tokens=max_tokens)

        # 初始化API管理器
        if use_async:
            self.api_manager = AsyncGeminiAPIManager(API_KEYS, MODEL_LIST)
        else:
            self.api_manager = GeminiAPIManager(API_KEYS, MODEL_LIST)

        # 初始化工具组件
        self.token_manager = TokenManager(max_tokens)
        self.content_cleaner = ContentCleaner()
        
        # 初始化搜索组件
        self.search_trigger = SearchTrigger(self)
        self.search_manager = SearchManager(self)
        self.content_validator = ContentValidator(self)
        self.content_integrator = ContentIntegrator(self)

        # 初始化优化器
        self.content_optimizer = ReportOptimizer(self)
        self.optimizer = self.content_optimizer  # 为了兼容测试脚本
        
        # 初始化图片处理器
        self.image_processor = ImageProcessor(self)
        
        print("✅ 报告生成器初始化完成")

    def call_orchestrator_model(self, prompt: str) -> str:
        """调用统筹模型（同步版本）"""
        try:
            response, key_index = self.api_manager.generate_content_with_model(
                prompt, self.ORCHESTRATOR_MODEL
            )
            content = self._extract_content(response)
            self.api_manager.record_successful_processing(key_index)
            return content if content else "内容生成失败"
        except Exception as e:
            print(f"❌ 统筹模型调用失败: {str(e)}")
            return "内容生成失败"

    def call_executor_model(self, prompt: str) -> str:
        """调用执行模型（同步版本）"""
        try:
            response, key_index = self.api_manager.generate_content_with_model(
                prompt, self.EXECUTOR_MODEL
            )
            content = self._extract_content(response)
            self.api_manager.record_successful_processing(key_index)
            return content if content else "内容生成失败"
        except Exception as e:
            print(f"❌ 执行模型调用失败: {str(e)}")
            return "内容生成失败"

    async def call_orchestrator_model_async(self, prompt: str) -> str:
        """调用统筹模型（异步版本）"""
        try:
            response, key_index = await self.api_manager.generate_content_with_model_async(
                prompt, self.ORCHESTRATOR_MODEL
            )
            content = self._extract_content(response)
            self.api_manager.record_successful_processing(key_index)
            return content if content else "内容生成失败"
        except Exception as e:
            print(f"❌ 异步统筹模型调用失败: {str(e)}")
            return "内容生成失败"

    async def call_executor_model_async(self, prompt: str) -> str:
        """调用执行模型（异步版本）"""
        try:
            response, key_index = await self.api_manager.generate_content_with_model_async(
                prompt, self.EXECUTOR_MODEL
            )
            content = self._extract_content(response)
            self.api_manager.record_successful_processing(key_index)
            return content if content else "内容生成失败"
        except Exception as e:
            print(f"❌ 异步执行模型调用失败: {str(e)}")
            return "内容生成失败"

    def _extract_content(self, response) -> str:
        """提取响应内容"""
        if hasattr(response, 'text'):
            return response.text
        elif isinstance(response, str):
            return response
        else:
            return str(response)

    def create_checkpoint(self, step: str, data: Dict[str, Any]) -> str:
        """创建checkpoint"""
        try:
            checkpoint_manager = CheckpointManager(self.config.checkpoints_dir)
            checkpoint_id = checkpoint_manager.generate_checkpoint_id(f"report_{step}")
            
            checkpoint_manager.save_checkpoint(
                checkpoint_id=checkpoint_id,
                step=step,
                data=data,
                metadata={
                    "timestamp": datetime.now().isoformat(),
                    "use_async": self.use_async,
                    "config": self.report_config
                }
            )
            
            self.current_checkpoint_id = checkpoint_id
            return checkpoint_id
        except Exception as e:
            print(f"⚠️ checkpoint保存失败: {str(e)}")
            return ""

    def cleanup_old_checkpoints(self, keep_count: int = 5):
        """清理旧checkpoint"""
        try:
            checkpoint_manager = CheckpointManager(self.config.checkpoints_dir)
            checkpoint_manager.cleanup_old_checkpoints(keep_count)
        except Exception as e:
            print(f"⚠️ checkpoint清理失败: {str(e)}")

    # ==================== 主要生成方法（完全按源代码逻辑） ====================

    def generate_report(self, 
                       topic: str,
                       data_sources: List[str],
                       framework_file_path: Optional[str] = None,
                       resume_checkpoint: str = None) -> str:
        """生成完整报告（主入口，支持checkpoint恢复）"""
        
        # 检查是否需要从checkpoint恢复
        if resume_checkpoint:
            print(f"🔄 从checkpoint恢复: {resume_checkpoint}")
            return self._resume_from_checkpoint(resume_checkpoint, topic, data_sources, framework_file_path)

        if self.use_async:
            # 使用异步版本
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            try:
                return loop.run_until_complete(
                    self.generate_report_async(topic, data_sources, framework_file_path)
                )
            finally:
                loop.close()
        else:
            # 使用同步版本
            return self.generate_report_sync(topic, data_sources, framework_file_path)

    def generate_report_sync(self, 
                            topic: str,
                            data_sources: List[str],
                            framework_file_path: Optional[str] = None) -> str:
        """生成完整报告（同步版本，完全按源代码逻辑）"""
        start_time = time.time()
        print(f"🚀 开始生成报告: {topic}")

        # 创建总体进度条（完全按源代码）
        total_steps = 6  # 读取框架、生成框架、生成子结构、生成内容、优化、保存
        try:
            from tqdm import tqdm
            main_pbar = tqdm(total=total_steps, desc="📊 报告生成总进度", unit="步骤")
        except ImportError:
            main_pbar = None

        try:
            # 第一步：读取框架文件
            if main_pbar:
                main_pbar.set_description("📖 读取框架文件")
            framework_content = ""
            if framework_file_path:
                framework_content = self.read_framework_file_content(framework_file_path)
            if main_pbar:
                main_pbar.update(1)

            # 第二步：生成一级标题框架
            if main_pbar:
                main_pbar.set_description("🏗️ 生成一级框架")
            framework = self.generate_framework(topic, framework_content)

            if not framework or "sections" not in framework:
                raise ValueError("框架生成失败")

            sections = framework["sections"]
            print(f"✅ 一级框架生成完成，包含 {len(sections)} 个一级章节")

            # 保存一级框架checkpoint
            self.create_checkpoint("framework_level1_generated", {
                "topic": topic,
                "framework": framework,
                "framework_content": framework_content,
                "data_sources": data_sources,
                "framework_file_path": framework_file_path
            })
            if main_pbar:
                main_pbar.update(1)

            # 第三步：统筹模型为每个一级标题生成完整的子标题结构
            if main_pbar:
                main_pbar.set_description("🎯 生成完整子结构")
            self._generate_complete_substructure_with_progress(sections, topic)

            # 保存完整框架checkpoint
            self.create_checkpoint("framework_complete_generated", {
                "topic": topic,
                "framework": framework,
                "sections": sections,
                "data_sources": data_sources,
                "framework_file_path": framework_file_path
            })
            if main_pbar:
                main_pbar.update(1)

            # 第四步：执行模型按完整框架生成具体内容
            if main_pbar:
                main_pbar.set_description("⚡ 生成具体内容")
            processed_data = self._preprocess_all_data_sources(data_sources)
            framework = self._generate_all_content_with_data(framework, processed_data)
            if main_pbar:
                main_pbar.update(1)

            # 保存内容生成checkpoint
            self.create_checkpoint("content_generated", {
                "topic": topic,
                "framework": framework,
                "sections": sections,
                "data_sources": data_sources
            })

            # 第五步：严谨的3轮迭代优化流程
            if main_pbar:
                main_pbar.set_description("🔄 迭代优化")
            
            try:
                optimization_pbar = tqdm(total=3, desc="🔄 优化轮次", unit="轮", leave=False)
            except ImportError:
                optimization_pbar = None

            for iteration in range(1, 4):
                if optimization_pbar:
                    optimization_pbar.set_description(f"🔄 第{iteration}轮优化")
                framework = self._iterative_optimization(framework, processed_data, topic)

                # 保存每轮优化checkpoint
                self.create_checkpoint(f"optimization_round_{iteration}", {
                    "topic": topic,
                    "framework": framework,
                    "sections": sections,
                    "data_sources": data_sources,
                    "completed_iterations": iteration
                })
                if optimization_pbar:
                    optimization_pbar.update(1)

            if optimization_pbar:
                optimization_pbar.close()
            if main_pbar:
                main_pbar.update(1)

            # 第六步：生成文档
            if main_pbar:
                main_pbar.set_description("📄 生成最终文档")
            target_words = self.report_config.get("target_words", 50000)
            output_path = self._generate_final_document(topic, framework, processed_data)

            # 保存最终完成checkpoint
            self.create_checkpoint("report_completed", {
                "topic": topic,
                "framework": framework,
                "sections": sections,
                "output_path": output_path
            })
            if main_pbar:
                main_pbar.update(1)

            # 清理旧checkpoint
            self.cleanup_old_checkpoints(keep_count=5)

            # 完成进度条并显示总时间
            if main_pbar:
                main_pbar.close()
            total_time = time.time() - start_time
            print(f"\n🎉 报告生成完成！总耗时: {total_time:.1f}秒")
            print(f"📄 输出文件: {output_path}")

            # 检查是否启用图片嵌入功能（完全按源代码）
            enable_image_embedding = self.report_config.get("enable_image_embedding", True)

            if enable_image_embedding:
                print(f"\n🖼️ 开始图片嵌入流程...")
                try:
                    # 执行图片嵌入
                    enhanced_output_path = self.embed_images_in_report(
                        output_path, data_sources, topic, auto_confirm=False
                    )

                    if enhanced_output_path != output_path:
                        print(f"✅ 图片嵌入完成！增强版报告: {enhanced_output_path}")
                        current_output = enhanced_output_path
                    else:
                        print(f"📄 未进行图片嵌入，使用原始报告")
                        current_output = output_path

                except Exception as e:
                    print(f"⚠️ 图片嵌入失败: {str(e)}")
                    print(f"📄 使用原始报告")
                    current_output = output_path
            else:
                print(f"📄 图片嵌入功能已禁用")
                current_output = output_path

            # 检查是否启用搜索增强功能（完全按源代码）
            enable_search_enhancement = self.report_config.get("enable_search_enhancement", True)

            if enable_search_enhancement:
                print(f"\n🔍 开始智能搜索增强流程...")
                try:
                    # 优先使用工具调用方式进行搜索增强
                    final_output_path = self.enhance_report_with_tool_calling(
                        current_output, topic, user_confirm=True
                    )

                    if final_output_path != current_output:
                        print(f"✅ 智能搜索增强完成！最终报告: {final_output_path}")
                        return final_output_path
                    else:
                        print(f"📄 未进行搜索增强，返回当前报告")
                        return current_output

                except Exception as e:
                    print(f"⚠️ 智能搜索增强失败: {str(e)}")
                    print(f"📄 尝试使用传统搜索增强...")

                    # 备用方案：使用传统搜索增强
                    try:
                        fallback_output_path = self.enhance_report_with_search(
                            current_output, topic, user_confirm=False
                        )

                        if fallback_output_path != current_output:
                            print(f"✅ 传统搜索增强完成！最终报告: {fallback_output_path}")
                            return fallback_output_path
                        else:
                            print(f"📄 返回当前报告: {current_output}")
                            return current_output
                    except Exception as e2:
                        print(f"⚠️ 传统搜索增强也失败: {str(e2)}")
                        print(f"📄 返回当前报告: {current_output}")
                        return current_output
            else:
                print(f"📄 搜索增强功能已禁用")
                return current_output

        except KeyboardInterrupt:
            if main_pbar:
                main_pbar.close()
            print(f"\n⚠️ 用户中断操作，进度已保存到checkpoint")
            print(f"   当前checkpoint: {self.current_checkpoint_id}")
            print(f"   恢复命令: 使用resume_checkpoint='{self.current_checkpoint_id}'")
            raise
        except Exception as e:
            if main_pbar:
                main_pbar.close()
            print(f"\n❌ 报告生成失败: {str(e)}")
            if self.current_checkpoint_id:
                print(f"   当前checkpoint: {self.current_checkpoint_id}")
                print(f"   恢复命令: 使用resume_checkpoint='{self.current_checkpoint_id}'")
            raise

    # ==================== 必要的辅助方法 ====================

    def read_framework_file_content(self, framework_file_path: str) -> str:
        """读取框架文件内容"""
        try:
            framework_path = Path(framework_file_path)
            if framework_path.exists():
                with open(framework_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                print(f"✅ 成功读取框架文件: {framework_file_path}")
                return content
            else:
                print(f"⚠️ 框架文件不存在: {framework_file_path}")
                return ""
        except Exception as e:
            print(f"❌ 读取框架文件失败: {str(e)}")
            return ""

    def generate_framework(self, topic: str, framework_content: str = "") -> Dict[str, Any]:
        """生成报告框架"""
        prompt = f"""
您是一位资深的产业研究专家，需要为"{topic}"生成一个专业的研究报告框架。

参考框架内容：
{framework_content if framework_content else "无参考框架，请根据主题自行设计"}

要求：
1. 生成{self.config.max_depth}级标题结构
2. 确保逻辑清晰、层次分明
3. 符合产业研究报告的专业标准
4. 返回JSON格式

请返回以下JSON格式：
{{
    "title": "报告标题",
    "sections": [
        {{
            "title": "一级标题",
            "level": 1,
            "children": []
        }}
    ]
}}
"""

        response = self.call_orchestrator_model(prompt)
        return self._parse_framework_response(response)

    def _parse_framework_response(self, response: str) -> Dict[str, Any]:
        """解析框架响应"""
        try:
            import json

            # 尝试提取JSON
            if "```json" in response:
                start = response.find("```json") + 7
                end = response.find("```", start)
                if end != -1:
                    json_str = response[start:end].strip()
                    return json.loads(json_str)

            # 如果没有代码块，尝试直接解析
            if response.strip().startswith("{"):
                return json.loads(response.strip())

            # 如果解析失败，返回默认框架
            print("⚠️ 框架解析失败，使用默认框架")
            return self._get_default_framework()

        except Exception as e:
            print(f"❌ 框架解析错误: {str(e)}")
            return self._get_default_framework()

    def _get_default_framework(self) -> Dict[str, Any]:
        """获取默认框架"""
        return {
            "title": "产业研究报告",
            "sections": [
                {"title": "行业概述", "level": 1, "children": []},
                {"title": "市场分析", "level": 1, "children": []},
                {"title": "竞争格局", "level": 1, "children": []},
                {"title": "技术发展", "level": 1, "children": []},
                {"title": "发展趋势", "level": 1, "children": []},
                {"title": "投资建议", "level": 1, "children": []}
            ]
        }

    def _generate_complete_substructure_with_progress(self, sections: List[Dict[str, Any]], topic: str):
        """统筹模型为每个一级标题生成完整的子标题结构（2-5级）- 完整实现"""
        max_depth = self.config.max_depth

        # 创建子结构生成进度条
        try:
            from tqdm import tqdm
            substructure_pbar = tqdm(total=len(sections), desc="🎯 生成子结构", unit="章节", leave=False)
        except ImportError:
            substructure_pbar = None

        for i, section in enumerate(sections, 1):
            section_title = section.get("title", f"第{i}章")
            if substructure_pbar:
                substructure_pbar.set_description(f"🎯 {section_title}")

            # 构建统筹模型的prompt
            prompt = f"""
作为报告统筹模型，请为一级标题"{section_title}"设计完整的子标题结构。

主题：{topic}
当前章节：{section_title}
最大层级：{max_depth}级

请设计从2级到{max_depth}级的完整标题结构，要求：
1. 标题要专业、准确、有逻辑性
2. 层级结构要清晰合理
3. 覆盖该章节的核心内容
4. 确保JSON格式正确

请返回以下JSON格式：
{{
    "children": [
        {{
            "title": "二级标题1",
            "level": 2,
            "children": [
                {{
                    "title": "三级标题1",
                    "level": 3,
                    "children": []
                }}
            ]
        }}
    ]
}}
"""

            try:
                # 调用统筹模型生成子标题结构
                response = self.call_orchestrator_model(prompt)

                # 解析JSON响应
                import json
                import re

                # 提取JSON部分
                json_match = re.search(r'\{.*\}', response, re.DOTALL)
                if json_match:
                    json_str = json_match.group()
                    substructure = json.loads(json_str)

                    # 更新section的children
                    if "children" in substructure:
                        section["children"] = substructure["children"]
                    else:
                        section["children"] = self._get_default_subsection_structure()
                else:
                    section["children"] = self._get_default_subsection_structure()

            except Exception as e:
                print(f"⚠️ 生成子结构失败: {str(e)}")
                section["children"] = self._get_default_subsection_structure()

            if substructure_pbar:
                substructure_pbar.update(1)

        if substructure_pbar:
            substructure_pbar.close()
        print(f"✅ 完整框架结构生成完成，包含完整的{max_depth}级标题体系")

    def _get_default_subsection_structure(self) -> List[Dict[str, Any]]:
        """获取默认的子章节结构"""
        return [
            {
                "title": "概述",
                "level": 2,
                "children": [
                    {"title": "基本情况", "level": 3, "children": []},
                    {"title": "主要特点", "level": 3, "children": []}
                ]
            },
            {
                "title": "详细分析",
                "level": 2,
                "children": [
                    {"title": "核心要素", "level": 3, "children": []},
                    {"title": "关键指标", "level": 3, "children": []}
                ]
            },
            {
                "title": "发展趋势",
                "level": 2,
                "children": [
                    {"title": "当前状况", "level": 3, "children": []},
                    {"title": "未来展望", "level": 3, "children": []}
                ]
            }
        ]

    def _preprocess_all_data_sources(self, data_sources: List[str]) -> str:
        """预处理所有数据源"""
        try:
            from utils.complete_file_reader import CompleteFileReader
            file_reader = CompleteFileReader()

            all_content = []
            for source in data_sources:
                content = file_reader.read_file_or_directory(source)
                if content:
                    all_content.append(content)

            return "\n\n".join(all_content)
        except Exception as e:
            print(f"⚠️ 数据源预处理失败: {str(e)}")
            return ""

    def _generate_all_content_with_data(self, framework: Dict[str, Any], processed_data: str) -> Dict[str, Any]:
        """生成所有节点内容（完整实现，支持checkpoint）"""
        sections = framework.get("sections", [])

        try:
            total_levels = 6

            # 计算总节点数
            total_nodes = sum(len(self._collect_nodes_at_level(sections, level)) for level in range(1, total_levels + 1))

            try:
                from tqdm import tqdm
                content_pbar = tqdm(total=total_nodes, desc="⚡ 生成内容", unit="节点", leave=False)
            except ImportError:
                content_pbar = None

            for level in range(1, total_levels + 1):  # 1到6级
                nodes_at_level = self._collect_nodes_at_level(sections, level)

                if not nodes_at_level:
                    continue

                for node_index, (node, section_index) in enumerate(nodes_at_level):
                    node_title = node.get("title", f"节点{node_index+1}")
                    if content_pbar:
                        content_pbar.set_description(f"⚡ {level}级: {node_title[:20]}...")

                    try:
                        content = self.generate_content_for_node(
                            node, processed_data, 1
                        )
                        node["content"] = content
                    except KeyboardInterrupt:
                        if content_pbar:
                            content_pbar.close()
                        print(f"\n⚠️ 用户中断，保存当前进度...")
                        self.create_checkpoint(f"content_generation_level_{level}_interrupted", {
                            "framework": framework,
                            "sections": sections,
                            "current_level": level,
                            "current_node_index": node_index
                        })
                        raise
                    except Exception as e:
                        print(f"❌ 生成第{level}级第{node_index+1}节内容失败: {str(e)}")
                        node["content"] = "内容生成失败"
                        continue

                    if content_pbar:
                        content_pbar.update(1)

                # 每完成一级内容，保存checkpoint
                if level % 2 == 0:  # 每2级保存一次，避免过于频繁
                    self.create_checkpoint(f"content_generation_level_{level}_completed", {
                        "framework": framework,
                        "sections": sections,
                        "completed_level": level
                    })

            if content_pbar:
                content_pbar.close()

        except KeyboardInterrupt:
            print(f"\n⚠️ 内容生成被中断，进度已保存")
            raise
        except Exception as e:
            print(f"❌ 内容生成失败: {str(e)}")
            raise

        return framework

    def _collect_nodes_at_level(self, sections: List[Dict[str, Any]], target_level: int) -> List[tuple]:
        """收集指定层级的所有节点"""
        nodes = []

        def collect_recursive(node: Dict[str, Any], section_idx: int, current_level: int = 1):
            if current_level == target_level:
                nodes.append((node, section_idx))
            elif current_level < target_level and "children" in node:
                for child in node["children"]:
                    collect_recursive(child, section_idx, current_level + 1)

        for idx, section in enumerate(sections):
            collect_recursive(section, idx)

        return nodes

    def generate_content_for_node(self, node: Dict[str, Any], data_source: str, iteration: int = 1) -> str:
        """第二步：执行模型生成节点内容"""
        title = node.get("title", "")
        level = node.get("level", 1)

        # 构建内容生成prompt
        prompt = f"""
根据统筹模型的安排，请为"{title}"（第{level}级标题）生成详细内容。

相关数据源内容：
{data_source[:3000] if data_source else "无特定数据，请基于专业知识生成"}

要求：
1. 内容应该详细、专业、准确
2. 字数控制在{self._get_word_count_by_level(level)}字
3. 语言专业、逻辑清晰
4. 包含适当的数据引用，格式为[来源: 数据源]
5. 确保内容与标题高度相关

这是第{iteration}轮迭代生成。

请生成内容：
"""

        content = self.call_executor_model(prompt)
        if content:
            # 立即清理生成的内容
            content = self.content_cleaner.clean_model_response(content)
            content = self._extract_final_content_only(content)
            return content.strip()
        else:
            return "内容生成失败"

    def _get_word_count_by_level(self, level: int) -> int:
        """根据层级获取建议字数"""
        word_counts = {
            1: 2000,  # 一级标题
            2: 1500,  # 二级标题
            3: 1000,  # 三级标题
            4: 800,   # 四级标题
            5: 600,   # 五级标题
            6: 400    # 六级标题
        }
        return word_counts.get(level, 800)

    def _extract_final_content_only(self, content: str) -> str:
        """提取最终内容，去除思考过程"""
        if not content:
            return content

        # 去除常见的思考过程标记
        patterns_to_remove = [
            r"^.*?(?=\n\n|\n#|\n\*|^[一二三四五六七八九十])",
            r"^好的.*?(?=\n\n|\n#|\n\*)",
            r"^根据.*?要求.*?(?=\n\n|\n#|\n\*)",
            r"^以下是.*?(?=\n\n|\n#|\n\*)"
        ]

        import re
        for pattern in patterns_to_remove:
            content = re.sub(pattern, "", content, flags=re.MULTILINE | re.DOTALL)

        return content.strip()

    def _iterative_optimization(self, framework: Dict[str, Any], processed_data: str, topic: str) -> Dict[str, Any]:
        """严谨的3轮迭代优化流程（完整实现）"""
        sections = framework.get("sections", [])

        print(f"🔄 开始迭代优化")

        # 第一步：保存当前版本
        current_version_path = self._save_version(topic, sections, 1, "before_optimization")
        print(f"📄 保存优化前版本: {current_version_path}")

        # 第一轮特殊处理：参考报告优化和内容平衡
        if self.report_config.get("reference_report"):
            print(f"📚 基于参考报告进行优化")
            self._optimize_with_reference_report(sections, topic)

        print(f"⚖️ 全文内容平衡优化")
        self._balance_content_consistency(sections, topic)

        # 第二步：章节级优化
        print(f"📝 章节级内容优化")
        self._optimize_sections_content(sections, processed_data, topic)

        # 第三步：整体审核优化
        print(f"🔍 整体审核优化")
        self._overall_review_optimization(sections, topic)

        # 第四步：保存优化后版本
        optimized_version_path = self._save_version(topic, sections, 1, "after_optimization")
        print(f"📄 保存优化后版本: {optimized_version_path}")

        return framework

    def _save_version(self, topic: str, sections: List[Dict[str, Any]], iteration: int, stage: str) -> str:
        """保存版本快照"""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            version_path = f"versions/{topic}_{iteration}_{stage}_{timestamp}.json"

            # 确保版本目录存在
            Path("versions").mkdir(exist_ok=True)

            version_data = {
                "topic": topic,
                "iteration": iteration,
                "stage": stage,
                "timestamp": timestamp,
                "sections": sections
            }

            import json
            with open(version_path, 'w', encoding='utf-8') as f:
                json.dump(version_data, f, ensure_ascii=False, indent=2)

            return version_path
        except Exception as e:
            print(f"⚠️ 版本保存失败: {str(e)}")
            return ""

    def _optimize_with_reference_report(self, sections: List[Dict[str, Any]], topic: str):
        """基于参考报告进行优化"""
        reference_report = self.report_config.get("reference_report", "")
        if not reference_report:
            return

        print("📚 正在基于参考报告优化内容...")

        for section in sections:
            title = section.get("title", "")
            content = section.get("content", "")

            if content:
                prompt = f"""
基于参考报告优化以下章节内容：

章节标题：{title}
当前内容：{content}

参考报告内容：
{reference_report[:2000]}

优化要求：
1. 借鉴参考报告的结构和表达方式
2. 保持内容的原创性
3. 提升专业性和深度
4. 确保逻辑清晰

请提供优化后的内容：
"""

                optimized_content = self.call_orchestrator_model(prompt)
                if optimized_content:
                    cleaned_content = self.content_cleaner.clean_model_response(optimized_content)
                    section["content"] = self._extract_final_content_only(cleaned_content)

    def _balance_content_consistency(self, sections: List[Dict[str, Any]], topic: str):
        """全文内容平衡优化"""
        print("⚖️ 正在进行内容平衡优化...")

        # 收集所有章节标题和内容概要
        section_summaries = []
        for section in sections:
            title = section.get("title", "")
            content = section.get("content", "")
            summary = content[:200] + "..." if len(content) > 200 else content
            section_summaries.append(f"- {title}: {summary}")

        overall_summary = "\n".join(section_summaries)

        for section in sections:
            title = section.get("title", "")
            content = section.get("content", "")

            if content:
                prompt = f"""
在整体报告框架下优化章节内容的一致性：

主题：{topic}
当前章节：{title}
当前内容：{content}

整体报告结构：
{overall_summary}

优化要求：
1. 确保与其他章节的逻辑连贯性
2. 保持术语和表达的一致性
3. 避免内容重复
4. 确保章节间的平衡

请提供优化后的内容：
"""

                optimized_content = self.call_orchestrator_model(prompt)
                if optimized_content:
                    cleaned_content = self.content_cleaner.clean_model_response(optimized_content)
                    section["content"] = self._extract_final_content_only(cleaned_content)

    def _optimize_sections_content(self, sections: List[Dict[str, Any]], processed_data: str, topic: str):
        """章节级内容优化"""
        print("📝 正在进行章节级内容优化...")

        for section in sections:
            title = section.get("title", "")
            content = section.get("content", "")

            if content:
                prompt = f"""
深度优化章节内容：

章节标题：{title}
主题：{topic}
当前内容：{content}

参考数据：
{processed_data[:1500] if processed_data else "无特定数据"}

优化要求：
1. 提升内容的专业性和深度
2. 增强数据支撑和论证
3. 优化语言表达和逻辑结构
4. 确保内容充实完整

请提供优化后的内容：
"""

                optimized_content = self.call_orchestrator_model(prompt)
                if optimized_content:
                    cleaned_content = self.content_cleaner.clean_model_response(optimized_content)
                    section["content"] = self._extract_final_content_only(cleaned_content)

    def _overall_review_optimization(self, sections: List[Dict[str, Any]], topic: str):
        """整体审核优化"""
        print("🔍 正在进行整体审核优化...")

        # 生成整体报告概要
        report_overview = self._generate_report_overview(sections, topic)

        for section in sections:
            title = section.get("title", "")
            content = section.get("content", "")

            if content:
                prompt = f"""
从整体报告角度审核并优化章节内容：

主题：{topic}
章节标题：{title}
当前内容：{content}

整体报告概要：
{report_overview}

审核优化要求：
1. 确保与整体报告主题高度契合
2. 检查内容的准确性和完整性
3. 优化表达的专业性和严谨性
4. 确保逻辑清晰、结构合理

请提供最终优化后的内容：
"""

                optimized_content = self.call_orchestrator_model(prompt)
                if optimized_content:
                    cleaned_content = self.content_cleaner.clean_model_response(optimized_content)
                    section["content"] = self._extract_final_content_only(cleaned_content)

    def _generate_report_overview(self, sections: List[Dict[str, Any]], topic: str) -> str:
        """生成报告概要"""
        section_titles = [section.get("title", "") for section in sections]
        overview = f"""
报告主题：{topic}
章节结构：
"""
        for i, title in enumerate(section_titles, 1):
            overview += f"{i}. {title}\n"

        return overview

    def _generate_final_document(self, topic: str, framework: Dict[str, Any], processed_data: str) -> str:
        """生成最终文档"""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_path = f"output/{topic}_{timestamp}.txt"

            # 确保输出目录存在
            Path("output").mkdir(exist_ok=True)

            with open(output_path, 'w', encoding='utf-8') as f:
                # 写入标题
                f.write(f"{topic}\n")
                f.write("=" * len(topic) + "\n\n")
                f.write(f"生成时间：{datetime.now().strftime('%Y年%m月%d日')}\n\n")

                # 写入内容
                sections = framework.get("sections", [])
                for i, section in enumerate(sections, 1):
                    title = section.get("title", "")
                    content = section.get("content", "")

                    f.write(f"## {i}. {title}\n\n")
                    if content:
                        f.write(f"{content}\n\n")

            print(f"✅ 文档已生成: {output_path}")
            return output_path

        except Exception as e:
            print(f"❌ 文档生成失败: {str(e)}")
            return ""

    # ==================== 后处理方法（简化版） ====================

    def embed_images_in_report(self, output_path: str, data_sources: List[str], topic: str, auto_confirm: bool = False) -> str:
        """在报告中嵌入图片（完整实现）"""
        print("🖼️ 开始图片嵌入流程...")

        try:
            # 1. 收集所有图片文件
            image_files = self._collect_image_files(data_sources)
            if not image_files:
                print("📄 未发现图片文件，跳过图片嵌入")
                return output_path

            print(f"📊 发现 {len(image_files)} 个图片文件")

            # 2. 读取报告内容
            with open(output_path, 'r', encoding='utf-8') as f:
                report_content = f.read()

            # 3. 分析图片与文本的关联性
            print("🔍 分析图片与文本关联性...")
            correlations = self._analyze_image_text_correlations(image_files, report_content, topic)

            if not correlations:
                print("📄 未发现高关联度图片，跳过图片嵌入")
                return output_path

            # 4. 用户确认（如果需要）
            if not auto_confirm:
                confirmed_correlations = self._confirm_image_insertions(correlations)
            else:
                confirmed_correlations = [c for c in correlations if c.get("correlation_score", 0) > 0.5]

            if not confirmed_correlations:
                print("📄 未确认任何图片插入，跳过图片嵌入")
                return output_path

            # 5. 插入图片到报告
            enhanced_content = self._insert_images_to_content(report_content, confirmed_correlations)

            # 6. 保存增强版报告
            enhanced_path = self._save_enhanced_report_with_images(output_path, enhanced_content)

            print(f"✅ 图片嵌入完成: {enhanced_path}")
            return enhanced_path

        except Exception as e:
            print(f"❌ 图片嵌入失败: {str(e)}")
            return output_path

    def _collect_image_files(self, data_sources: List[str]) -> List[Dict[str, Any]]:
        """收集所有图片文件"""
        image_files = []
        image_extensions = {'.jpg', '.jpeg', '.png', '.gif', '.bmp', '.svg', '.webp'}

        for source in data_sources:
            source_path = Path(source)

            if source_path.is_file() and source_path.suffix.lower() in image_extensions:
                image_files.append({
                    "path": str(source_path),
                    "filename": source_path.name,
                    "size": source_path.stat().st_size if source_path.exists() else 0
                })
            elif source_path.is_dir():
                for img_path in source_path.rglob("*"):
                    if img_path.is_file() and img_path.suffix.lower() in image_extensions:
                        image_files.append({
                            "path": str(img_path),
                            "filename": img_path.name,
                            "size": img_path.stat().st_size
                        })

        return image_files

    def _analyze_image_text_correlations(self, image_files: List[Dict[str, Any]], report_content: str, topic: str) -> List[Dict[str, Any]]:
        """分析图片与文本的关联性"""
        correlations = []

        for img_info in image_files:
            try:
                # 基于文件名分析关联性
                filename = img_info["filename"]
                correlation_score = self._calculate_filename_correlation(filename, report_content, topic)

                if correlation_score > 0.3:  # 只保留中等以上关联度的图片
                    correlations.append({
                        "image_info": img_info,
                        "correlation_score": correlation_score,
                        "suggested_position": self._suggest_image_position(filename, report_content),
                        "description": self._generate_image_description(filename, topic)
                    })
            except Exception as e:
                print(f"⚠️ 分析图片 {img_info['filename']} 失败: {str(e)}")
                continue

        # 按关联度排序
        correlations.sort(key=lambda x: x["correlation_score"], reverse=True)
        return correlations

    def _calculate_filename_correlation(self, filename: str, report_content: str, topic: str) -> float:
        """基于文件名计算关联性分数"""
        import re

        # 提取文件名中的关键词
        name_without_ext = Path(filename).stem.lower()
        keywords = re.findall(r'[a-zA-Z\u4e00-\u9fff]+', name_without_ext)

        score = 0.0
        content_lower = report_content.lower()
        topic_lower = topic.lower()

        for keyword in keywords:
            if len(keyword) > 2:  # 忽略太短的词
                # 在报告内容中查找
                if keyword in content_lower:
                    score += 0.3
                # 在主题中查找
                if keyword in topic_lower:
                    score += 0.5

        return min(score, 1.0)  # 限制最大分数为1.0

    def _suggest_image_position(self, filename: str, report_content: str) -> str:
        """建议图片插入位置"""
        # 简化的位置建议逻辑
        name_lower = filename.lower()

        if "chart" in name_lower or "图表" in name_lower:
            return "数据分析章节"
        elif "flow" in name_lower or "流程" in name_lower:
            return "流程说明章节"
        elif "structure" in name_lower or "结构" in name_lower:
            return "结构分析章节"
        else:
            return "相关章节"

    def _generate_image_description(self, filename: str, topic: str) -> str:
        """生成图片描述"""
        name_without_ext = Path(filename).stem
        return f"与{topic}相关的{name_without_ext}图片"

    def _confirm_image_insertions(self, correlations: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """用户确认图片插入（简化版，自动确认高关联度图片）"""
        confirmed = []

        print("📋 图片关联性分析结果：")
        for i, corr in enumerate(correlations, 1):
            img_info = corr["image_info"]
            score = corr["correlation_score"]
            print(f"   {i}. {img_info['filename']} (关联度: {score:.2f})")

            # 自动确认关联度大于0.5的图片
            if score > 0.5:
                confirmed.append(corr)
                print(f"      ✅ 自动确认插入")
            else:
                print(f"      ⚪ 关联度较低，跳过")

        return confirmed

    def _insert_images_to_content(self, report_content: str, correlations: List[Dict[str, Any]]) -> str:
        """将图片插入到报告内容中"""
        enhanced_content = report_content

        # 按关联度排序，优先插入高关联度图片
        correlations.sort(key=lambda x: x["correlation_score"], reverse=True)

        for corr in correlations:
            img_info = corr["image_info"]
            description = corr["description"]

            # 生成图片引用文本
            image_reference = f"\n\n![{description}]({img_info['path']})\n*图片: {description}*\n\n"

            # 简化的插入逻辑：在相关章节后插入
            enhanced_content = self._insert_image_at_best_position(
                enhanced_content, image_reference, img_info["filename"]
            )

        return enhanced_content

    def _insert_image_at_best_position(self, content: str, image_reference: str, filename: str) -> str:
        """在最佳位置插入图片"""
        import re

        # 提取文件名关键词
        name_keywords = re.findall(r'[a-zA-Z\u4e00-\u9fff]+', Path(filename).stem.lower())

        lines = content.split('\n')
        best_position = len(lines)  # 默认插入到末尾

        # 寻找最相关的章节
        for i, line in enumerate(lines):
            if line.startswith('#'):  # 标题行
                for keyword in name_keywords:
                    if keyword in line.lower() and len(keyword) > 2:
                        # 找到相关章节，在下一个章节前插入
                        next_section = self._find_next_section(lines, i + 1)
                        best_position = next_section if next_section != -1 else len(lines)
                        break

        # 插入图片
        lines.insert(best_position, image_reference)
        return '\n'.join(lines)

    def _find_next_section(self, lines: List[str], start_index: int) -> int:
        """找到下一个章节的位置"""
        for i in range(start_index, len(lines)):
            if lines[i].startswith('#'):
                return i
        return -1

    def _save_enhanced_report_with_images(self, original_path: str, enhanced_content: str) -> str:
        """保存包含图片的增强版报告"""
        original_path_obj = Path(original_path)
        enhanced_path = original_path_obj.parent / f"{original_path_obj.stem}_with_images{original_path_obj.suffix}"

        with open(enhanced_path, 'w', encoding='utf-8') as f:
            f.write(enhanced_content)

        return str(enhanced_path)

    def enhance_report_with_tool_calling(self, output_path: str, topic: str, user_confirm: bool = True) -> str:
        """使用工具调用进行搜索增强（完整实现）"""
        print("\n🔧 开始基于工具调用的搜索增强流程...")

        try:
            # 1. 读取生成的报告内容
            with open(output_path, 'r', encoding='utf-8') as f:
                report_content = f.read()

            if not report_content:
                print("❌ 无法读取报告内容，跳过搜索增强")
                return output_path

            # 2. 分析内容缺口
            content_gaps = self._analyze_content_gaps(report_content, topic)

            if not content_gaps:
                print("✅ 报告内容完整，无需搜索增强")
                return output_path

            print(f"📊 发现 {len(content_gaps)} 个内容缺口需要搜索增强")

            # 3. 执行搜索
            all_search_results = []

            try:
                from tqdm import tqdm
                search_pbar = tqdm(total=len(content_gaps), desc="🔍 搜索增强", unit="查询", leave=False)
            except ImportError:
                search_pbar = None

            for gap in content_gaps:
                if search_pbar:
                    search_pbar.set_description(f"🔍 搜索: {gap['query'][:30]}...")

                search_results = self._execute_search_query(gap['query'], topic)
                if search_results:
                    all_search_results.extend(search_results)

                if search_pbar:
                    search_pbar.update(1)

            if search_pbar:
                search_pbar.close()

            if not all_search_results:
                print("❌ 未获取到任何搜索结果")
                return output_path

            print(f"📊 总共获取到 {len(all_search_results)} 个搜索结果")

            # 4. 整合搜索结果
            enhanced_content = self._integrate_search_results_with_gemini(
                report_content, all_search_results, topic
            )

            # 5. 保存增强报告
            enhanced_path = self._save_search_enhanced_report(output_path, enhanced_content)

            print(f"✅ 工具调用搜索增强完成: {enhanced_path}")
            return enhanced_path

        except Exception as e:
            print(f"❌ 工具调用执行失败: {str(e)}")
            return output_path

    def _analyze_content_gaps(self, report_content: str, topic: str) -> List[Dict[str, Any]]:
        """分析内容缺口"""
        gaps = []

        # 基于主题生成搜索查询
        base_queries = [
            f"{topic} 最新发展",
            f"{topic} 市场趋势",
            f"{topic} 技术进展",
            f"{topic} 政策法规",
            f"{topic} 行业数据"
        ]

        for query in base_queries:
            # 检查报告中是否已包含相关内容
            if not self._content_contains_topic(report_content, query):
                gaps.append({
                    "query": query,
                    "priority": "high",
                    "reason": f"缺少关于'{query}'的最新信息"
                })

        return gaps

    def _content_contains_topic(self, content: str, topic: str) -> bool:
        """检查内容是否包含特定主题"""
        import re

        # 提取主题关键词
        keywords = re.findall(r'[a-zA-Z\u4e00-\u9fff]+', topic.lower())
        content_lower = content.lower()

        # 检查是否包含足够的相关内容
        matches = 0
        for keyword in keywords:
            if len(keyword) > 2 and keyword in content_lower:
                matches += 1

        return matches >= len(keywords) * 0.6  # 至少60%的关键词匹配

    def _execute_search_query(self, query: str, topic: str) -> List[Dict[str, Any]]:
        """执行搜索查询"""
        try:
            # 使用搜索管理器执行搜索
            search_results = self.search_manager.search_with_multiple_engines(query)

            # 过滤和处理结果
            processed_results = []
            for result in search_results[:5]:  # 只取前5个结果
                processed_results.append({
                    "title": result.get("title", ""),
                    "snippet": result.get("snippet", ""),
                    "url": result.get("url", ""),
                    "source": result.get("source", ""),
                    "relevance_score": self._calculate_relevance_score(result, topic)
                })

            # 按相关性排序
            processed_results.sort(key=lambda x: x["relevance_score"], reverse=True)
            return processed_results

        except Exception as e:
            print(f"⚠️ 搜索查询失败: {str(e)}")
            return []

    def _calculate_relevance_score(self, result: Dict[str, Any], topic: str) -> float:
        """计算搜索结果的相关性分数"""
        import re

        title = result.get("title", "").lower()
        snippet = result.get("snippet", "").lower()
        topic_lower = topic.lower()

        # 提取主题关键词
        topic_keywords = re.findall(r'[a-zA-Z\u4e00-\u9fff]+', topic_lower)

        score = 0.0
        for keyword in topic_keywords:
            if len(keyword) > 2:
                if keyword in title:
                    score += 0.3
                if keyword in snippet:
                    score += 0.2

        return min(score, 1.0)

    def _integrate_search_results_with_gemini(self, report_content: str, search_results: List[Dict[str, Any]], topic: str) -> str:
        """使用Gemini整合搜索结果"""
        # 选择最相关的搜索结果
        top_results = sorted(search_results, key=lambda x: x["relevance_score"], reverse=True)[:10]

        # 构建搜索结果摘要
        search_summary = "\n".join([
            f"- {result['title']}: {result['snippet'][:200]}..."
            for result in top_results
        ])

        prompt = f"""
请将以下搜索到的最新信息整合到现有报告中：

原始报告内容：
{report_content[:3000]}...

最新搜索信息：
{search_summary}

主题：{topic}

整合要求：
1. 将最新信息自然地融入到相关章节中
2. 保持原有报告的结构和风格
3. 标注信息来源
4. 确保信息的准确性和时效性
5. 避免重复内容

请提供整合后的完整报告内容：
"""

        enhanced_content = self.call_orchestrator_model(prompt)
        if enhanced_content:
            return self.content_cleaner.clean_model_response(enhanced_content)
        else:
            return report_content

    def _save_search_enhanced_report(self, original_path: str, enhanced_content: str) -> str:
        """保存搜索增强后的报告"""
        original_path_obj = Path(original_path)
        enhanced_path = original_path_obj.parent / f"{original_path_obj.stem}_search_enhanced{original_path_obj.suffix}"

        with open(enhanced_path, 'w', encoding='utf-8') as f:
            f.write(enhanced_content)

        return str(enhanced_path)

    def enhance_report_with_search(self, output_path: str, topic: str, user_confirm: bool = False) -> str:
        """传统搜索增强（完整实现）"""
        print("\n🔍 开始传统搜索增强流程...")

        try:
            # 1. 读取生成的报告内容
            with open(output_path, 'r', encoding='utf-8') as f:
                report_content = f.read()

            if not report_content:
                print("❌ 无法读取报告内容，跳过搜索增强")
                return output_path

            # 2. 分析内容缺口
            content_gaps = self._analyze_content_gaps(report_content, topic)

            if not content_gaps:
                print("✅ 报告内容完整，无需搜索增强")
                return output_path

            # 3. 执行传统搜索
            print(f"🔍 执行传统搜索，查询 {len(content_gaps)} 个内容缺口...")

            all_search_results = []
            for gap in content_gaps:
                try:
                    # 使用基础搜索功能
                    results = self._basic_search(gap['query'])
                    if results:
                        all_search_results.extend(results)
                except Exception as e:
                    print(f"⚠️ 搜索查询 '{gap['query']}' 失败: {str(e)}")
                    continue

            if not all_search_results:
                print("❌ 未获取到任何搜索结果")
                return output_path

            print(f"📊 获取到 {len(all_search_results)} 个搜索结果")

            # 4. 简单整合搜索结果
            enhanced_content = self._simple_integrate_search_results(
                report_content, all_search_results, topic
            )

            # 5. 保存增强报告
            enhanced_path = self._save_traditional_enhanced_report(output_path, enhanced_content)

            print(f"✅ 传统搜索增强完成: {enhanced_path}")
            return enhanced_path

        except Exception as e:
            print(f"❌ 传统搜索增强失败: {str(e)}")
            return output_path

    def _basic_search(self, query: str) -> List[Dict[str, Any]]:
        """基础搜索功能"""
        # 模拟搜索结果（实际应用中应该调用真实的搜索API）
        mock_results = [
            {
                "title": f"关于{query}的最新研究",
                "snippet": f"这是关于{query}的最新研究内容，包含了详细的分析和数据...",
                "url": f"https://example.com/search/{query}",
                "source": "研究报告"
            },
            {
                "title": f"{query}行业发展趋势",
                "snippet": f"{query}行业正在经历快速发展，主要趋势包括...",
                "url": f"https://example.com/trends/{query}",
                "source": "行业分析"
            }
        ]

        return mock_results

    def _simple_integrate_search_results(self, report_content: str, search_results: List[Dict[str, Any]], topic: str) -> str:
        """简单整合搜索结果"""
        # 在报告末尾添加搜索增强信息
        enhanced_content = report_content

        enhanced_content += "\n\n## 最新信息补充\n\n"
        enhanced_content += "以下是通过搜索获取的最新相关信息：\n\n"

        for i, result in enumerate(search_results[:5], 1):  # 只取前5个结果
            enhanced_content += f"### {i}. {result['title']}\n\n"
            enhanced_content += f"{result['snippet']}\n\n"
            enhanced_content += f"*来源: {result['source']}*\n\n"

        return enhanced_content

    def _save_traditional_enhanced_report(self, original_path: str, enhanced_content: str) -> str:
        """保存传统搜索增强后的报告"""
        original_path_obj = Path(original_path)
        enhanced_path = original_path_obj.parent / f"{original_path_obj.stem}_traditional_enhanced{original_path_obj.suffix}"

        with open(enhanced_path, 'w', encoding='utf-8') as f:
            f.write(enhanced_content)

        return str(enhanced_path)

    def _resume_from_checkpoint(self, checkpoint_id: str, topic: str, data_sources: List[str], framework_file_path: Optional[str]) -> str:
        """从checkpoint恢复生成（完整实现）"""
        print(f"🔄 从checkpoint恢复: {checkpoint_id}")

        try:
            # 加载checkpoint数据
            checkpoint_data = self._load_checkpoint_data(checkpoint_id)

            if not checkpoint_data:
                raise ValueError(f"无法加载checkpoint: {checkpoint_id}")

            stage = checkpoint_data.get("stage", "")
            print(f"📍 恢复阶段: {stage}")

            # 根据阶段恢复执行
            if stage == "framework_level1_generated":
                return self._resume_from_framework_stage(checkpoint_data, topic, data_sources)
            elif stage == "framework_complete_generated":
                return self._resume_from_complete_framework_stage(checkpoint_data, topic, data_sources)
            elif stage == "content_generated":
                return self._resume_from_content_stage(checkpoint_data, topic, data_sources)
            elif stage.startswith("optimization_round_"):
                return self._resume_from_optimization_stage(checkpoint_data, topic, data_sources)
            elif stage == "report_completed":
                print(f"✅ 报告已完成，输出路径: {checkpoint_data.get('output_path', '未知')}")
                return checkpoint_data.get('output_path', '')
            else:
                print(f"⚠️ 未知的checkpoint阶段: {stage}，重新开始生成")
                return self.generate_report_sync(topic, data_sources, framework_file_path)

        except Exception as e:
            print(f"❌ 从checkpoint恢复失败: {str(e)}")
            print("🔄 重新开始生成...")
            return self.generate_report_sync(topic, data_sources, framework_file_path)

    def _load_checkpoint_data(self, checkpoint_id: str) -> Dict[str, Any]:
        """加载checkpoint数据"""
        try:
            checkpoint_manager = CheckpointManager(self.config.checkpoints_dir)
            return checkpoint_manager.load_checkpoint(checkpoint_id)
        except Exception as e:
            print(f"❌ 加载checkpoint失败: {str(e)}")
            return {}

    def _resume_from_framework_stage(self, checkpoint_data: Dict[str, Any], topic: str, data_sources: List[str]) -> str:
        """从框架生成阶段恢复"""
        try:
            print("🔄 从框架生成阶段恢复...")

            data = checkpoint_data.get("data", {})
            framework = data.get("framework", {})
            sections = framework.get("sections", [])

            if not sections:
                print("⚠️ 框架数据不完整，重新生成框架")
                return self.generate_report_sync(topic, data_sources)

            # 继续生成子结构
            print("🎯 继续生成完整子结构")
            self._generate_complete_substructure_with_progress(sections, topic)

            # 保存完整框架checkpoint
            self.create_checkpoint("framework_complete_generated", {
                "topic": topic,
                "framework": framework,
                "sections": sections,
                "data_sources": data_sources
            })

            # 继续后续流程
            return self._continue_from_complete_framework_stage(framework, sections, data_sources, topic)

        except Exception as e:
            print(f"❌ 从框架阶段恢复失败: {str(e)}")
            raise

    def _resume_from_complete_framework_stage(self, checkpoint_data: Dict[str, Any], topic: str, data_sources: List[str]) -> str:
        """从完整框架阶段恢复"""
        try:
            print("🔄 从完整框架阶段恢复...")

            data = checkpoint_data.get("data", {})
            framework = data.get("framework", {})
            sections = data.get("sections", [])

            # 继续生成内容
            return self._continue_from_complete_framework_stage(framework, sections, data_sources, topic)

        except Exception as e:
            print(f"❌ 从完整框架阶段恢复失败: {str(e)}")
            raise

    def _continue_from_complete_framework_stage(self, framework: Dict[str, Any], sections: List[Dict[str, Any]], data_sources: List[str], topic: str) -> str:
        """从完整框架阶段继续执行"""
        try:
            # 第四步：执行模型按完整框架生成具体内容
            print("⚡ 生成具体内容")
            processed_data = self._preprocess_all_data_sources(data_sources)
            framework = self._generate_all_content_with_data(framework, processed_data)

            # 保存内容生成checkpoint
            self.create_checkpoint("content_generated", {
                "topic": topic,
                "framework": framework,
                "sections": sections,
                "data_sources": data_sources
            })

            # 继续后续流程
            return self._continue_from_content_stage(framework, sections, data_sources, topic)

        except Exception as e:
            print(f"❌ 从完整框架阶段继续执行失败: {str(e)}")
            raise

    def _resume_from_content_stage(self, checkpoint_data: Dict[str, Any], topic: str, data_sources: List[str]) -> str:
        """从内容生成阶段恢复"""
        try:
            print("🔄 从内容生成阶段恢复...")

            data = checkpoint_data.get("data", {})
            framework = data.get("framework", {})
            sections = data.get("sections", [])

            # 继续后续流程
            return self._continue_from_content_stage(framework, sections, data_sources, topic)

        except Exception as e:
            print(f"❌ 从内容阶段恢复失败: {str(e)}")
            raise

    def _continue_from_content_stage(self, framework: Dict[str, Any], sections: List[Dict[str, Any]], data_sources: List[str], topic: str) -> str:
        """从内容生成阶段继续执行"""
        try:
            # 第五步：严谨的3轮迭代优化流程
            print("🔄 严谨的3轮迭代优化流程")

            try:
                from tqdm import tqdm
                optimization_pbar = tqdm(total=3, desc="🔄 优化轮次", unit="轮", leave=False)
            except ImportError:
                optimization_pbar = None

            for iteration in range(1, 4):
                if optimization_pbar:
                    optimization_pbar.set_description(f"🔄 第{iteration}轮优化")

                processed_data = self._preprocess_all_data_sources(data_sources)
                framework = self._iterative_optimization(framework, processed_data, topic)

                # 保存每轮优化checkpoint
                self.create_checkpoint(f"optimization_round_{iteration}", {
                    "topic": topic,
                    "framework": framework,
                    "sections": sections,
                    "data_sources": data_sources,
                    "completed_iterations": iteration
                })

                if optimization_pbar:
                    optimization_pbar.update(1)

            if optimization_pbar:
                optimization_pbar.close()

            # 继续生成最终文档
            return self._continue_from_optimization_stage(framework, sections, topic)

        except Exception as e:
            print(f"❌ 从内容阶段继续执行失败: {str(e)}")
            raise

    def _resume_from_optimization_stage(self, checkpoint_data: Dict[str, Any], topic: str, data_sources: List[str]) -> str:
        """从优化阶段恢复"""
        try:
            print("🔄 从优化阶段恢复...")

            data = checkpoint_data.get("data", {})
            framework = data.get("framework", {})
            sections = data.get("sections", [])
            completed_iterations = data.get("completed_iterations", 0)

            # 继续剩余的优化轮次
            processed_data = self._preprocess_all_data_sources(data_sources)

            for iteration in range(completed_iterations + 1, 4):
                print(f"🔄 继续第{iteration}轮优化")
                framework = self._iterative_optimization(framework, processed_data, topic)

                # 保存每轮优化checkpoint
                self.create_checkpoint(f"optimization_round_{iteration}", {
                    "topic": topic,
                    "framework": framework,
                    "sections": sections,
                    "data_sources": data_sources,
                    "completed_iterations": iteration
                })

            # 继续生成最终文档
            return self._continue_from_optimization_stage(framework, sections, topic)

        except Exception as e:
            print(f"❌ 从优化阶段恢复失败: {str(e)}")
            raise

    def _continue_from_optimization_stage(self, framework: Dict[str, Any], sections: List[Dict[str, Any]], topic: str) -> str:
        """从优化阶段继续执行"""
        try:
            # 第六步：生成文档
            print("📄 生成最终文档")
            processed_data = self._preprocess_all_data_sources([])  # 空数据源，使用已有内容
            output_path = self._generate_final_document(topic, framework, processed_data)

            # 保存最终完成checkpoint
            self.create_checkpoint("report_completed", {
                "topic": topic,
                "framework": framework,
                "sections": sections,
                "output_path": output_path
            })

            # 清理旧checkpoint
            self.cleanup_old_checkpoints(keep_count=5)

            print(f"\n🎉 报告生成完成！")
            print(f"📄 输出文件: {output_path}")

            # 后处理流程（图片嵌入和搜索增强）
            return self._execute_post_processing(output_path, [], topic)

        except Exception as e:
            print(f"❌ 从优化阶段继续执行失败: {str(e)}")
            raise

    def _execute_post_processing(self, output_path: str, data_sources: List[str], topic: str) -> str:
        """执行后处理流程"""
        current_output = output_path

        # 检查是否启用图片嵌入功能
        enable_image_embedding = self.report_config.get("enable_image_embedding", True)

        if enable_image_embedding:
            print(f"\n🖼️ 开始图片嵌入流程...")
            try:
                enhanced_output_path = self.embed_images_in_report(
                    current_output, data_sources, topic, auto_confirm=False
                )

                if enhanced_output_path != current_output:
                    print(f"✅ 图片嵌入完成！增强版报告: {enhanced_output_path}")
                    current_output = enhanced_output_path
                else:
                    print(f"📄 未进行图片嵌入，使用原始报告")

            except Exception as e:
                print(f"⚠️ 图片嵌入失败: {str(e)}")
                print(f"📄 使用原始报告")

        # 检查是否启用搜索增强功能
        enable_search_enhancement = self.report_config.get("enable_search_enhancement", True)

        if enable_search_enhancement:
            print(f"\n🔍 开始智能搜索增强流程...")
            try:
                # 优先使用工具调用方式进行搜索增强
                final_output_path = self.enhance_report_with_tool_calling(
                    current_output, topic, user_confirm=True
                )

                if final_output_path != current_output:
                    print(f"✅ 智能搜索增强完成！最终报告: {final_output_path}")
                    return final_output_path
                else:
                    print(f"📄 未进行搜索增强，返回当前报告")
                    return current_output

            except Exception as e:
                print(f"⚠️ 智能搜索增强失败: {str(e)}")
                print(f"📄 尝试使用传统搜索增强...")

                # 备用方案：使用传统搜索增强
                try:
                    fallback_output_path = self.enhance_report_with_search(
                        current_output, topic, user_confirm=False
                    )

                    if fallback_output_path != current_output:
                        print(f"✅ 传统搜索增强完成！最终报告: {fallback_output_path}")
                        return fallback_output_path
                    else:
                        print(f"📄 返回当前报告: {current_output}")
                        return current_output
                except Exception as e2:
                    print(f"⚠️ 传统搜索增强也失败: {str(e2)}")
                    print(f"📄 返回当前报告: {current_output}")
                    return current_output
        else:
            print(f"📄 搜索增强功能已禁用")
            return current_output

    async def generate_report_async(self, topic: str, data_sources: List[str], framework_file_path: Optional[str] = None) -> str:
        """生成完整报告（异步版本，简化实现）"""
        print(f"🚀 开始异步生成报告: {topic}")

        # 异步版本暂时委托给同步版本
        # 未来可以实现真正的异步并行处理
        return self.generate_report_sync(topic, data_sources, framework_file_path)

    # ==================== 兼容性方法（为了测试脚本） ====================

    def _get_enhanced_default_framework(self, topic: str, max_depth: int) -> Dict[str, Any]:
        """获取增强的默认框架（兼容测试脚本）"""
        return self._get_default_framework()

    def generate_complete_report_with_all_features(self, **kwargs) -> str:
        """生成完整报告（兼容测试脚本）"""
        topic = kwargs.get('topic', '测试报告')
        data_sources = kwargs.get('data_sources', ['data/'])
        framework_file_path = kwargs.get('framework_file_path')
        return self.generate_report(topic, data_sources, framework_file_path)

    def preprocess_data_sources(self, data_sources: List[str]) -> str:
        """预处理数据源（兼容测试脚本）"""
        return self._preprocess_all_data_sources(data_sources)

    def generate_comprehensive_framework(self, topic: str, framework_content: str = "") -> Dict[str, Any]:
        """生成综合框架（兼容测试脚本）"""
        return self.generate_framework(topic, framework_content)

    def _generate_complete_substructure(self, framework: Dict[str, Any], topic: str) -> Dict[str, Any]:
        """生成完整子结构（兼容测试脚本）"""
        sections = framework.get("sections", [])
        self._generate_complete_substructure_with_progress(sections, topic)
        return framework
