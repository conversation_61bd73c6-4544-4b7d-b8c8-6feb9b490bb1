#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
全面测试脚本
测试重构后的所有核心功能
"""

import sys
from pathlib import Path

# 添加当前目录到Python路径
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

def test_core_functionality():
    """测试核心功能"""
    print("🧪 测试核心功能...")
    
    try:
        # 1. 测试配置系统
        print("1. 测试配置系统...")
        from core.config import ReportConfig, API_KEYS, MODEL_NAMES
        config = ReportConfig(target_words=30000, max_depth=5, use_async=True)
        print(f"   ✅ 配置创建成功: {config.target_words} 字, 异步={config.use_async}")
        
        # 2. 测试API管理器
        print("2. 测试API管理器...")
        from api.gemini_manager_new import GeminiAPIManager, AsyncGeminiAPIManager
        
        # 同步API管理器
        sync_manager = GeminiAPIManager(API_KEYS, MODEL_NAMES)
        print(f"   ✅ 同步API管理器创建成功: {sync_manager.total_keys} 个密钥")
        
        # 异步API管理器
        async_manager = AsyncGeminiAPIManager(API_KEYS, MODEL_NAMES)
        print(f"   ✅ 异步API管理器创建成功: {async_manager.total_keys} 个密钥")
        
        # 3. 测试生成器创建
        print("3. 测试生成器创建...")
        from core.generator import CompleteReportGenerator
        
        # 同步生成器
        sync_generator = CompleteReportGenerator(use_async=False)
        print(f"   ✅ 同步生成器创建成功")
        
        # 异步生成器
        async_generator = CompleteReportGenerator(use_async=True)
        print(f"   ✅ 异步生成器创建成功")
        
        # 4. 测试Checkpoint系统
        print("4. 测试Checkpoint系统...")
        test_data = {"test": "checkpoint_data", "stage": "test"}
        checkpoint_id = sync_generator.create_checkpoint("test_stage", test_data)
        print(f"   ✅ Checkpoint创建成功: {checkpoint_id}")
        
        # 加载checkpoint
        loaded_data = sync_generator.load_checkpoint(checkpoint_id)
        print(f"   ✅ Checkpoint加载成功: {len(loaded_data)} 个数据项")
        
        # 列出checkpoints
        checkpoints = sync_generator.list_checkpoints()
        print(f"   ✅ 找到 {len(checkpoints)} 个checkpoint")
        
        # 5. 测试Token管理器
        print("5. 测试Token管理器...")
        from utils.token_manager import TokenManager, AsyncConfig
        token_manager = TokenManager()
        test_text = "这是一个测试文本。" * 100
        token_info = token_manager.get_token_info(test_text)
        print(f"   ✅ Token估算: {token_info['estimated_tokens']} tokens")
        
        # 异步配置
        perf_info = AsyncConfig.get_performance_info()
        print(f"   ✅ 性能配置: {perf_info['estimated_speedup']} 加速")
        
        # 6. 测试内容清理器
        print("6. 测试内容清理器...")
        from content.content_cleaner import ContentCleaner
        cleaner = ContentCleaner()
        dirty_text = """
        好的，遵照您的要求，我来生成内容。
        
        这是实际的有用内容，包含重要信息。
        
        优化前版本：旧的内容
        优化后版本：新的内容
        
        这是另一段有用的内容。
        
        思考过程：我在思考如何写这段内容...
        """
        clean_text = cleaner.clean_content_thoroughly(dirty_text)
        reduction = (1 - len(clean_text) / len(dirty_text)) * 100
        print(f"   ✅ 内容清理成功，清理率: {reduction:.1f}%")
        
        # 7. 测试搜索模块
        print("7. 测试搜索模块...")
        from search.search_trigger import SearchTrigger
        from search.search_manager import SearchManager
        
        search_trigger = SearchTrigger(sync_generator)
        search_manager = SearchManager(sync_generator)
        
        test_content = "这是一个关于人工智能的报告。技术发展迅速。"
        gaps = search_trigger.analyze_content_gaps(test_content, "人工智能")
        print(f"   ✅ 搜索触发器: 发现 {len(gaps)} 个内容缺口")
        
        # 8. 测试文件读取器
        print("8. 测试文件读取器...")
        from utils.complete_file_reader import CompleteFileReader as FileReader
        file_reader = FileReader()
        print(f"   ✅ 文件读取器支持格式: {len(file_reader.supported_extensions)} 种")
        
        print("\n🎉 所有核心功能测试通过！")
        return True
        
    except Exception as e:
        print(f"\n❌ 核心功能测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_integration():
    """测试集成功能"""
    print("\n🔧 测试集成功能...")
    
    try:
        from core.generator import CompleteReportGenerator
        
        # 创建生成器
        generator = CompleteReportGenerator(use_async=False)
        
        # 测试框架生成
        print("1. 测试框架生成...")
        framework = generator._get_default_framework()
        print(f"   ✅ 默认框架: {len(framework.get('sections', []))} 个章节")
        
        # 测试内容处理
        print("2. 测试内容处理...")
        test_prompt = "生成一个关于人工智能的简短介绍"
        # 注意：这里不实际调用API，只测试方法存在
        print(f"   ✅ 模型调用方法已准备就绪")
        
        # 测试配置更新
        print("3. 测试配置更新...")
        generator.report_config.update({
            "title": "测试报告",
            "target_words": 25000
        })
        print(f"   ✅ 配置更新成功: {generator.report_config['title']}")
        
        print("\n🎉 集成功能测试通过！")
        return True
        
    except Exception as e:
        print(f"\n❌ 集成功能测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🚀 开始全面功能测试")
    print("=" * 60)
    
    # 测试核心功能
    core_success = test_core_functionality()
    
    # 测试集成功能
    integration_success = test_integration()
    
    # 总结
    print("\n" + "=" * 60)
    if core_success and integration_success:
        print("🎉 所有测试通过！重构版本功能完整！")
        print("\n📊 功能对比:")
        print("   ✅ API管理器: 完整实现（同步+异步）")
        print("   ✅ Checkpoint系统: 完整实现")
        print("   ✅ Token管理: 完整实现")
        print("   ✅ 内容清理: 完整实现")
        print("   ✅ 搜索模块: 基础实现")
        print("   ✅ 文件处理: 完整实现")
        print("   ✅ 配置管理: 完整实现")
        print("   ⚠️ 报告生成流程: 需要进一步测试")
        return True
    else:
        print("❌ 部分测试失败，需要修复")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
