"""
测试搜索增强功能
验证搜索API集成和内容增强功能
"""
import os
import sys
import time
import shutil
from pathlib import Path

def create_test_report():
    """创建测试报告"""
    print("📄 创建测试报告...")
    
    test_dir = Path("test_search_enhancement_data")
    if test_dir.exists():
        shutil.rmtree(test_dir)
    test_dir.mkdir()
    
    # 创建一个缺少最新信息的测试报告
    test_report_content = """# 人工智能技术发展报告

## 1. 技术概述

人工智能（AI）是计算机科学的一个分支，致力于创建能够执行通常需要人类智能的任务的系统。

### 1.1 基本概念

人工智能包括机器学习、深度学习、自然语言处理等多个子领域。

### 1.2 发展历程

人工智能的发展可以追溯到20世纪50年代，经历了多次起伏。

## 2. 技术分类

### 2.1 机器学习

机器学习是AI的核心技术之一，包括监督学习、无监督学习和强化学习。

### 2.2 深度学习

深度学习基于神经网络，在图像识别、语音识别等领域取得了突破性进展。

## 3. 应用领域

### 3.1 医疗健康

AI在医疗诊断、药物发现等方面显示出巨大潜力。

### 3.2 自动驾驶

自动驾驶技术是AI应用的重要方向之一。

## 4. 发展前景

人工智能技术将继续快速发展，对社会产生深远影响。

---

*注：本报告基于公开资料整理，部分数据可能不是最新的。*
"""
    
    test_report_path = test_dir / "ai_report.md"
    with open(test_report_path, 'w', encoding='utf-8') as f:
        f.write(test_report_content)
    
    print(f"✅ 测试报告已创建: {test_report_path}")
    return test_report_path

def test_search_trigger():
    """测试搜索需求识别"""
    print("\n🧪 测试搜索需求识别")
    print("=" * 50)
    
    try:
        from complete_report_generator import CompleteReportGenerator
        
        generator = CompleteReportGenerator(use_async=False)
        search_trigger = generator.SearchTrigger(generator)
        
        # 创建测试内容
        test_content = """
        人工智能技术发展报告
        
        人工智能是一个快速发展的领域。机器学习和深度学习是其核心技术。
        在医疗、自动驾驶等领域有广泛应用。
        
        技术发展迅速，创新不断涌现。
        """
        
        topic = "人工智能技术发展"
        
        # 分析内容缺口
        gaps = search_trigger.analyze_content_gaps(test_content, topic)
        
        print(f"📊 发现 {len(gaps)} 个内容缺口:")
        for i, gap in enumerate(gaps, 1):
            print(f"   {i}. 类型: {gap['type']}")
            print(f"      查询: {gap['query']}")
            print(f"      优先级: {gap['priority']}")
            print(f"      原因: {gap['reason']}")
            print()
        
        if gaps:
            print("✅ 搜索需求识别测试通过")
            return True
        else:
            print("⚠️ 未识别到内容缺口")
            return False
            
    except Exception as e:
        print(f"❌ 搜索需求识别测试失败: {str(e)}")
        return False

def test_search_manager():
    """测试搜索管理器"""
    print("\n🧪 测试搜索管理器")
    print("=" * 50)
    
    try:
        from complete_report_generator import CompleteReportGenerator
        
        generator = CompleteReportGenerator(use_async=False)
        search_manager = generator.SearchManager(generator)
        
        print(f"📊 搜索API配置状态:")
        for api_name, config in search_manager.search_apis.items():
            status = "✅ 已配置" if config.get('enabled', False) else "❌ 未配置"
            print(f"   {api_name}: {status}")
        
        # 测试查询生成
        test_gap = {
            'type': 'latest_data',
            'query': '人工智能 最新数据 2024',
            'priority': 'high'
        }
        
        queries = search_manager.generate_search_queries("人工智能", test_gap)
        
        print(f"\n📝 生成的搜索查询:")
        for i, query in enumerate(queries, 1):
            print(f"   {i}. {query}")
        
        # 如果有配置的API，尝试搜索
        if search_manager.search_apis:
            print(f"\n🔍 尝试搜索测试...")
            
            try:
                # 选择第一个可用的搜索源
                available_sources = [name for name, config in search_manager.search_apis.items() 
                                   if config.get('enabled', False)]
                
                if available_sources:
                    test_query = "人工智能 2024"
                    results = search_manager.multi_source_search(
                        test_query, available_sources[:1], num_results=2
                    )
                    
                    print(f"   查询: {test_query}")
                    print(f"   结果数量: {len(results)}")
                    
                    for i, result in enumerate(results, 1):
                        print(f"   {i}. {result.get('title', '')[:50]}...")
                        print(f"      来源: {result.get('source', '')}")
                    
                    if results:
                        print("✅ 搜索管理器测试通过")
                        return True
                    else:
                        print("⚠️ 搜索未返回结果")
                        return False
                else:
                    print("⚠️ 没有可用的搜索API")
                    return False
                    
            except Exception as e:
                print(f"⚠️ 搜索测试失败: {str(e)}")
                return False
        else:
            print("⚠️ 未配置搜索API，跳过搜索测试")
            print("✅ 搜索管理器基础功能测试通过")
            return True
            
    except Exception as e:
        print(f"❌ 搜索管理器测试失败: {str(e)}")
        return False

def test_content_validator():
    """测试内容验证器"""
    print("\n🧪 测试内容验证器")
    print("=" * 50)
    
    try:
        from complete_report_generator import CompleteReportGenerator
        
        generator = CompleteReportGenerator(use_async=False)
        validator = generator.ContentValidator(generator)
        
        # 测试搜索结果
        test_results = [
            {
                'title': '2024年人工智能发展报告',
                'url': 'https://example.edu/ai-report-2024',
                'snippet': '2024年人工智能技术取得重大突破，机器学习算法效率提升30%，深度学习在医疗诊断领域准确率达到95%。',
                'date': '2024-01-15'
            },
            {
                'title': '人工智能简介',
                'url': 'https://blog.example.com/ai-intro',
                'snippet': 'AI是很有趣的技术。',
                'date': '2020-01-01'
            }
        ]
        
        topic = "人工智能技术发展"
        
        print("📊 验证搜索结果质量:")
        for i, result in enumerate(test_results, 1):
            is_valid = validator.validate_search_result(result, topic)
            status = "✅ 通过" if is_valid else "❌ 不通过"
            
            print(f"   结果 {i}: {status}")
            print(f"      标题: {result['title']}")
            print(f"      相关性: {validator.calculate_relevance(result['snippet'], topic):.2f}")
            print(f"      权威性: {validator.evaluate_source_authority(result['url']):.2f}")
            print(f"      时效性: {validator.evaluate_freshness(result['date']):.2f}")
            print()
        
        print("✅ 内容验证器测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 内容验证器测试失败: {str(e)}")
        return False

def test_search_enhancement_flow():
    """测试完整的搜索增强流程"""
    print("\n🧪 测试搜索增强流程")
    print("=" * 50)
    
    try:
        from complete_report_generator import CompleteReportGenerator
        
        # 创建测试报告
        test_report_path = create_test_report()
        
        # 初始化生成器
        generator = CompleteReportGenerator(use_async=False)
        
        # 禁用用户确认以便自动测试
        original_method = generator.enhance_report_with_search
        
        def mock_enhance_report_with_search(output_path, topic, user_confirm=True):
            # 强制设置为不需要用户确认进行测试
            return original_method(output_path, topic, user_confirm=False)
        
        generator.enhance_report_with_search = mock_enhance_report_with_search
        
        # 执行搜索增强
        print("🔍 执行搜索增强...")
        
        enhanced_path = generator.enhance_report_with_search(
            str(test_report_path),
            "人工智能技术发展",
            user_confirm=False
        )
        
        # 检查结果
        if enhanced_path != str(test_report_path):
            print(f"✅ 搜索增强成功: {enhanced_path}")
            
            # 检查增强内容
            if Path(enhanced_path).exists():
                with open(enhanced_path, 'r', encoding='utf-8') as f:
                    enhanced_content = f.read()
                
                if "最新信息补充" in enhanced_content:
                    print("✅ 增强内容包含补充信息")
                    return True
                else:
                    print("⚠️ 增强内容未包含预期的补充信息")
                    return False
            else:
                print("❌ 增强文件不存在")
                return False
        else:
            print("⚠️ 未生成增强报告（可能是API未配置）")
            return False
            
    except Exception as e:
        print(f"❌ 搜索增强流程测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        # 清理测试数据
        test_dir = Path("test_search_enhancement_data")
        if test_dir.exists():
            shutil.rmtree(test_dir)

def test_api_configuration():
    """测试API配置"""
    print("\n🧪 测试API配置")
    print("=" * 50)
    
    try:
        from search_api_config import check_search_api_status, load_env_file
        
        # 加载环境变量
        load_env_file()
        
        # 检查API状态
        check_search_api_status()
        
        # 检查环境变量
        google_api = os.getenv('GOOGLE_SEARCH_API_KEY')
        google_cx = os.getenv('GOOGLE_SEARCH_CX')
        bing_api = os.getenv('BING_SEARCH_API_KEY')
        
        configured_apis = 0
        if google_api and google_cx:
            configured_apis += 1
        if bing_api:
            configured_apis += 1
        
        print(f"\n📊 配置状态: {configured_apis}/2 个API已配置")
        
        if configured_apis > 0:
            print("✅ API配置测试通过")
            return True
        else:
            print("⚠️ 未配置任何搜索API")
            print("💡 请运行 python search_api_config.py 进行配置")
            return False
            
    except Exception as e:
        print(f"❌ API配置测试失败: {str(e)}")
        return False

def main():
    """主测试函数"""
    print("🚀 AI报告生成器 - 搜索增强功能测试")
    print("=" * 60)
    
    tests = [
        ("API配置检查", test_api_configuration),
        ("搜索需求识别", test_search_trigger),
        ("搜索管理器", test_search_manager),
        ("内容验证器", test_content_validator),
        ("搜索增强流程", test_search_enhancement_flow)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {str(e)}")
            results.append((test_name, False))
    
    # 总结
    print(f"\n📊 测试总结:")
    print("=" * 60)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {test_name}: {status}")
    
    print(f"\n🎯 总体结果: {passed}/{total} 个测试通过")
    
    if passed == total:
        print(f"\n🎉 搜索增强功能测试通过！")
        print(f"\n💡 功能特点:")
        print(f"   1. 🔍 智能识别内容缺口")
        print(f"   2. 🌐 多源搜索API集成")
        print(f"   3. 🔒 内容质量验证")
        print(f"   4. 🤖 智能内容整合")
        print(f"   5. 👤 用户确认机制")
    else:
        print(f"\n💡 如果测试失败，请检查:")
        print(f"   1. 配置搜索API: python search_api_config.py")
        print(f"   2. 安装requests库: pip install requests")
        print(f"   3. 检查网络连接")
        print(f"   4. 验证API密钥有效性")
    
    return passed == total

if __name__ == "__main__":
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\n❌ 用户中断测试")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 测试程序错误: {str(e)}")
        sys.exit(1)
