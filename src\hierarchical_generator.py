"""
分层内容生成器模块
实现从一级到六级的分层生成逻辑和上下文继承算法
"""
import time
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime

from .state import ReportState, GenerationPhaseResult, IterationPhase
from .config import Config
from .logger import ProcessLogger
from .llm_client import LLMClient
from .context_manager import ContextManager


class IHierarchicalContentGenerator:
    """分层内容生成器接口"""
    
    def generate_hierarchical_content(self, state: ReportState) -> ReportState:
        """分层生成内容"""
        raise NotImplementedError
    
    def build_hierarchical_context(self, node: Dict[str, Any], state: ReportState) -> Dict[str, Any]:
        """构建层级上下文"""
        raise NotImplementedError


class HierarchicalContentGenerator(IHierarchicalContentGenerator):
    """
    分层内容生成器实现
    按照一级→二级→三级→四级→五级→六级的顺序生成内容
    """
    
    def __init__(
        self, 
        config: Config, 
        llm_client: LLMClient, 
        context_manager: ContextManager,
        logger: ProcessLogger
    ):
        self.config = config
        self.llm_client = llm_client
        self.context_manager = context_manager
        self.logger = logger
        self.max_depth = config.report.max_heading_depth
    
    def generate_hierarchical_content(self, state: ReportState) -> ReportState:
        """
        分层生成内容的主入口
        
        Args:
            state: 报告状态
            
        Returns:
            更新后的报告状态
        """
        self.logger.logger.info("开始分层内容生成")
        start_time = time.time()
        
        try:
            if not state.report_structure or "sections" not in state.report_structure:
                raise ValueError("报告结构未初始化")
            
            sections = state.report_structure["sections"]
            
            # 按层级顺序生成内容
            for level in range(1, self.max_depth + 1):
                self.logger.logger.info(f"生成第 {level} 级内容")
                self._generate_level_content(sections, level, state)
            
            duration = time.time() - start_time
            self.logger.logger.info(f"分层内容生成完成，耗时: {duration:.2f}秒")
            
        except Exception as e:
            self.logger.logger.error(f"分层内容生成失败: {str(e)}")
            state.add_error(f"分层生成器错误: {str(e)}", "hierarchical_generator")
            raise
        
        return state
    
    def _generate_level_content(
        self, 
        sections: List[Dict[str, Any]], 
        target_level: int, 
        state: ReportState
    ):
        """
        生成指定层级的内容
        
        Args:
            sections: 章节列表
            target_level: 目标层级
            state: 报告状态
        """
        nodes_at_level = self._collect_nodes_at_level(sections, target_level)
        
        self.logger.logger.info(f"第 {target_level} 级共有 {len(nodes_at_level)} 个节点")
        
        for node_info in nodes_at_level:
            node, section_index = node_info
            self._generate_single_node_content(node, section_index, state)
    
    def _collect_nodes_at_level(
        self, 
        sections: List[Dict[str, Any]], 
        target_level: int
    ) -> List[Tuple[Dict[str, Any], int]]:
        """
        收集指定层级的所有节点
        
        Args:
            sections: 章节列表
            target_level: 目标层级
            
        Returns:
            节点和章节索引的元组列表
        """
        nodes = []
        
        def collect_recursive(node: Dict[str, Any], section_idx: int, current_level: int = 1):
            if current_level == target_level:
                nodes.append((node, section_idx))
            elif current_level < target_level and "children" in node:
                for child in node["children"]:
                    collect_recursive(child, section_idx, current_level + 1)
        
        for idx, section in enumerate(sections):
            collect_recursive(section, idx)
        
        return nodes
    
    def _generate_single_node_content(
        self, 
        node: Dict[str, Any], 
        section_index: int, 
        state: ReportState
    ):
        """
        生成单个节点的内容
        
        Args:
            node: 节点信息
            section_index: 章节索引
            state: 报告状态
        """
        try:
            # 构建层级上下文
            context = self.build_hierarchical_context(node, state, section_index)
            
            # 生成内容
            content = self._call_llm_for_content(node, context, state)
            
            # 更新节点内容
            node["content"] = content
            
            # 记录到状态中
            node_key = self._get_node_key(node)
            state.generated_content[node_key] = content
            
            self.logger.logger.debug(f"生成节点内容: {node.get('title', 'Unknown')}")
            
        except Exception as e:
            error_msg = f"生成节点内容失败: {node.get('title', 'Unknown')} - {str(e)}"
            self.logger.logger.error(error_msg)
            state.add_error(error_msg, "single_node_generation")
            
            # 设置默认内容
            node["content"] = "内容生成失败，请检查数据源和配置。"
    
    def build_hierarchical_context(
        self, 
        node: Dict[str, Any], 
        state: ReportState, 
        section_index: int
    ) -> Dict[str, Any]:
        """
        构建层级上下文
        实现父级节点内容作为子级上下文的逻辑
        
        Args:
            node: 当前节点
            state: 报告状态
            section_index: 章节索引
            
        Returns:
            构建的上下文
        """
        context = {"parts": []}
        
        try:
            # 1. 获取章节特定的数据源
            if section_index < len(state.data_sources):
                source_path = state.data_sources[section_index]
                chapter_context = self.context_manager.prepare_chapter_context(
                    chapter_title=node.get("title", ""),
                    source_path=source_path
                )
                if "parts" in chapter_context:
                    context["parts"].extend(chapter_context["parts"])
            
            # 2. 添加父级节点内容作为上下文
            parent_content = self._get_parent_content(node, state)
            if parent_content:
                context["parts"].append({
                    "text": f"父级章节内容参考：\n{parent_content}"
                })
            
            # 3. 添加同级节点参考
            sibling_content = self._get_sibling_content(node, state)
            if sibling_content:
                context["parts"].append({
                    "text": f"同级章节参考：\n{sibling_content}"
                })
            
            # 4. 添加前轮迭代内容
            previous_iteration_content = self._get_previous_iteration_content(node, state)
            if previous_iteration_content:
                context["parts"].append({
                    "text": f"前轮迭代内容：\n{previous_iteration_content}"
                })
            
        except Exception as e:
            self.logger.logger.error(f"构建层级上下文失败: {str(e)}")
            # 返回基本上下文
            if section_index < len(state.data_sources):
                source_path = state.data_sources[section_index]
                context = self.context_manager.prepare_chapter_context(
                    chapter_title=node.get("title", ""),
                    source_path=source_path
                )
        
        return context
    
    def _get_parent_content(self, node: Dict[str, Any], state: ReportState) -> str:
        """获取父级节点内容"""
        # 这里需要实现查找父级节点的逻辑
        # 暂时返回空字符串，具体实现需要遍历报告结构
        return ""
    
    def _get_sibling_content(self, node: Dict[str, Any], state: ReportState) -> str:
        """获取同级节点内容"""
        # 这里需要实现查找同级节点的逻辑
        # 暂时返回空字符串
        return ""
    
    def _get_previous_iteration_content(self, node: Dict[str, Any], state: ReportState) -> str:
        """获取前轮迭代内容"""
        node_key = self._get_node_key(node)
        
        # 从迭代历史中获取前一轮的内容
        if "iteration_history" in node and node["iteration_history"]:
            return node["iteration_history"][-1]
        
        # 从状态中获取之前生成的内容
        if node_key in state.generated_content:
            return state.generated_content[node_key]
        
        return ""
    
    def _call_llm_for_content(
        self, 
        node: Dict[str, Any], 
        context: Dict[str, Any], 
        state: ReportState
    ) -> str:
        """
        调用LLM生成内容
        
        Args:
            node: 节点信息
            context: 上下文
            state: 报告状态
            
        Returns:
            生成的内容
        """
        level = node.get("level", 1)
        title = node.get("title", "")
        
        # 根据层级调整提示词
        if level == 1:
            prompt = f"""
基于提供的所有资料，为一级章节'{title}'生成综合性的概述内容。

要求：
1. 内容应该涵盖该章节的核心要点
2. 字数控制在800-1200字
3. 语言专业、逻辑清晰
4. 包含适当的数据引用，格式为[来源: 文件名]
5. 为下级章节提供良好的引导

请生成内容：
"""
        elif level <= 3:
            prompt = f"""
基于提供的资料和上级章节内容，为'{title}'章节生成详细的分析内容。

要求：
1. 内容应该详细分析该章节的具体方面
2. 字数控制在600-800字
3. 与上级章节保持逻辑一致性
4. 包含具体的数据和案例
5. 引用格式：[来源: 文件名]

请生成内容：
"""
        else:
            prompt = f"""
基于提供的资料和上级章节内容，为'{title}'子章节生成具体的说明内容。

要求：
1. 内容应该具体说明该子章节的要点
2. 字数控制在400-600字
3. 与上级章节紧密关联
4. 提供具体的细节和数据
5. 引用格式：[来源: 文件名]

请生成内容：
"""
        
        try:
            content = self.llm_client.call_llm(
                prompt=prompt,
                context=context,
                model_type="executor",
                response_format="text"
            )
            return content.strip()
            
        except Exception as e:
            self.logger.logger.error(f"LLM调用失败: {str(e)}")
            return f"内容生成失败：{str(e)}"
    
    def _get_node_key(self, node: Dict[str, Any]) -> str:
        """生成节点的唯一键"""
        level = node.get("level", 0)
        title = node.get("title", "")
        return f"level_{level}_{title}"


class ChapterDataSourceIntegrator:
    """
    章节数据源集成器
    负责将章节特定数据源集成到分层生成过程中
    """
    
    def __init__(self, config: Config, logger: ProcessLogger):
        self.config = config
        self.logger = logger
    
    def integrate_chapter_data_sources(
        self, 
        generator: HierarchicalContentGenerator,
        state: ReportState
    ) -> HierarchicalContentGenerator:
        """
        集成章节数据源到分层生成器
        
        Args:
            generator: 分层生成器实例
            state: 报告状态
            
        Returns:
            增强后的分层生成器
        """
        self.logger.logger.info("集成章节数据源到分层生成器")
        
        # 验证数据源配置
        self._validate_data_source_configuration(state)
        
        # 创建章节ID到数据源的映射
        chapter_mapping = self._create_chapter_data_mapping(state)
        
        # 增强生成器的上下文构建方法
        original_build_context = generator.build_hierarchical_context
        
        def enhanced_build_context(node, state, section_index):
            # 调用原始方法
            context = original_build_context(node, state, section_index)
            
            # 添加章节特定的数据源增强
            enhanced_context = self._enhance_context_with_chapter_data(
                context, node, section_index, chapter_mapping
            )
            
            return enhanced_context
        
        # 替换方法
        generator.build_hierarchical_context = enhanced_build_context
        
        return generator
    
    def _validate_data_source_configuration(self, state: ReportState):
        """验证数据源配置"""
        if len(state.data_sources) != self.config.report.num_top_level_sections:
            raise ValueError(
                f"数据源数量({len(state.data_sources)}) "
                f"与顶级章节数量({self.config.report.num_top_level_sections})不匹配"
            )
    
    def _create_chapter_data_mapping(self, state: ReportState) -> Dict[int, str]:
        """创建章节到数据源的映射"""
        mapping = {}
        for idx, data_source in enumerate(state.data_sources):
            mapping[idx] = data_source
        
        self.logger.logger.info(f"创建章节数据映射: {len(mapping)} 个映射")
        return mapping
    
    def _enhance_context_with_chapter_data(
        self, 
        context: Dict[str, Any], 
        node: Dict[str, Any], 
        section_index: int,
        chapter_mapping: Dict[int, str]
    ) -> Dict[str, Any]:
        """使用章节特定数据增强上下文"""
        if section_index in chapter_mapping:
            data_source_path = chapter_mapping[section_index]
            
            # 添加数据源路径信息到上下文
            if "metadata" not in context:
                context["metadata"] = {}
            
            context["metadata"]["chapter_data_source"] = data_source_path
            context["metadata"]["section_index"] = section_index
            
            self.logger.logger.debug(
                f"为节点 {node.get('title', 'Unknown')} "
                f"添加章节数据源: {data_source_path}"
            )
        
        return context
