#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
重构后的报告生成器测试脚本
测试核心功能是否正常工作
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from core.generator import CompleteReportGenerator

def test_basic_functionality():
    """测试基本功能"""
    print("🧪 开始测试重构后的报告生成器...")
    
    try:
        # 1. 初始化生成器
        print("\n1️⃣ 初始化生成器...")
        generator = CompleteReportGenerator()
        print("✅ 生成器初始化成功")
        
        # 2. 测试配置加载
        print("\n2️⃣ 测试配置...")
        config = generator.report_config
        print(f"✅ 配置加载成功，包含 {len(config)} 个配置项")
        
        # 3. 测试框架生成
        print("\n3️⃣ 测试框架生成...")
        topic = "人工智能技术发展报告"
        framework = generator.generate_framework(topic, "")
        
        if framework and "sections" in framework:
            sections_count = len(framework["sections"])
            print(f"✅ 框架生成成功，包含 {sections_count} 个一级章节")
            
            # 显示框架结构
            for i, section in enumerate(framework["sections"][:3], 1):
                title = section.get("title", f"章节{i}")
                print(f"   {i}. {title}")
        else:
            print("❌ 框架生成失败")
            return False
        
        # 4. 测试子结构生成
        print("\n4️⃣ 测试子结构生成...")
        sections = framework["sections"][:2]  # 只测试前2个章节
        generator._generate_complete_substructure(sections, topic)
        
        # 检查子结构
        has_children = any(section.get("children") for section in sections)
        if has_children:
            print("✅ 子结构生成成功")
        else:
            print("⚠️ 子结构生成可能有问题")
        
        # 5. 测试文件读取功能
        print("\n5️⃣ 测试文件读取功能...")
        
        # 创建测试文件
        test_dir = Path("test_data")
        test_dir.mkdir(exist_ok=True)
        
        test_file = test_dir / "test.txt"
        with open(test_file, 'w', encoding='utf-8') as f:
            f.write("这是一个测试文件内容。")
        
        content = generator.read_data_source(str(test_dir))
        if content and "测试文件内容" in content:
            print("✅ 文件读取功能正常")
        else:
            print("⚠️ 文件读取功能可能有问题")
        
        # 清理测试文件
        test_file.unlink()
        test_dir.rmdir()
        
        # 6. 测试内容清理功能
        print("\n6️⃣ 测试内容清理功能...")
        test_content = """
        好的，遵照您的要求，我来生成内容。
        
        这是正常的内容部分。
        
        优化前版本：旧内容
        优化后版本：新内容
        
        这是另一个正常的内容部分。
        """
        
        cleaned_content = generator._clean_model_response(test_content)
        if "好的，遵照您的要求" not in cleaned_content and "优化前版本" not in cleaned_content:
            print("✅ 内容清理功能正常")
        else:
            print("⚠️ 内容清理功能可能有问题")
        
        # 7. 测试搜索管理器初始化
        print("\n7️⃣ 测试搜索管理器...")
        try:
            search_manager = generator.SearchManager(generator)
            print(f"✅ 搜索管理器初始化成功，配置了 {len(search_manager.search_apis)} 个搜索API")
        except Exception as e:
            print(f"⚠️ 搜索管理器初始化失败: {str(e)}")
        
        print("\n🎉 基本功能测试完成！")
        return True
        
    except Exception as e:
        print(f"\n❌ 测试过程中发生错误: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_report_generation():
    """测试完整报告生成（简化版）"""
    print("\n🧪 测试简化版报告生成...")
    
    try:
        generator = CompleteReportGenerator()
        
        # 创建测试数据源
        test_data_dir = Path("test_data_sources")
        test_data_dir.mkdir(exist_ok=True)
        
        # 创建测试数据文件
        for i in range(2):
            data_dir = test_data_dir / f"data_{i+1}"
            data_dir.mkdir(exist_ok=True)
            
            test_file = data_dir / "test.txt"
            with open(test_file, 'w', encoding='utf-8') as f:
                f.write(f"这是第{i+1}个数据源的测试内容。包含一些关于人工智能的信息。")
        
        # 设置简化配置
        generator.report_config.update({
            "primary_sections": 2,
            "max_depth": 3,
            "enable_image_embedding": False,
            "enable_search_enhancement": False
        })
        
        # 生成报告
        topic = "人工智能技术测试报告"
        data_sources = [str(test_data_dir / f"data_{i+1}") for i in range(2)]
        
        print(f"📝 开始生成报告: {topic}")
        output_path = generator.generate_report(topic, data_sources)
        
        if output_path and Path(output_path).exists():
            print(f"✅ 报告生成成功: {output_path}")
            
            # 检查文件大小
            file_size = Path(output_path).stat().st_size
            print(f"📊 报告文件大小: {file_size} 字节")
            
            return True
        else:
            print("❌ 报告生成失败")
            return False
            
    except Exception as e:
        print(f"❌ 报告生成测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False
    finally:
        # 清理测试数据
        import shutil
        if test_data_dir.exists():
            shutil.rmtree(test_data_dir)

def main():
    """主测试函数"""
    print("🚀 重构后的报告生成器测试")
    print("=" * 50)
    
    # 测试基本功能
    basic_test_passed = test_basic_functionality()
    
    if basic_test_passed:
        print("\n" + "=" * 50)
        # 测试报告生成
        generation_test_passed = test_report_generation()
        
        if generation_test_passed:
            print("\n🎉 所有测试通过！重构成功！")
        else:
            print("\n⚠️ 报告生成测试失败，但基本功能正常")
    else:
        print("\n❌ 基本功能测试失败，需要检查代码")

if __name__ == "__main__":
    main()
