#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
模块化系统测试文件
验证各个模块是否能正常导入和工作
"""

import sys
from pathlib import Path

# 添加当前目录到Python路径
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))


def test_imports():
    """测试模块导入"""
    print("🧪 测试模块导入...")
    
    try:
        # 测试核心模块
        from core.config import ReportConfig, API_KEYS, MODEL_NAMES
        from core.generator import CompleteReportGenerator
        print("✅ 核心模块导入成功")
        
        # 测试API模块
        from api.gemini_manager import GeminiAPIManager, AsyncGeminiAPIManager
        print("✅ API模块导入成功")
        
        # 测试工具模块
        from utils.token_manager import TokenManager, AsyncConfig
        print("✅ 工具模块导入成功")
        
        # 测试搜索模块
        from search.search_trigger import SearchTrigger
        from search.search_manager import SearchManager
        print("✅ 搜索模块导入成功")
        
        # 测试内容模块
        from content.content_cleaner import ContentCleaner
        print("✅ 内容模块导入成功")
        
        return True
        
    except ImportError as e:
        print(f"❌ 模块导入失败: {str(e)}")
        return False


def test_config():
    """测试配置模块"""
    print("\n🧪 测试配置模块...")
    
    try:
        from core.config import ReportConfig, get_environment_config
        
        # 测试配置创建
        config = ReportConfig(
            target_words=30000,
            max_depth=5,
            use_async=True
        )
        
        print(f"✅ 配置创建成功: 目标字数={config.target_words}")
        
        # 测试配置转换
        config_dict = config.to_dict()
        config_restored = ReportConfig.from_dict(config_dict)
        
        print(f"✅ 配置序列化成功: 异步模式={config_restored.use_async}")
        
        # 测试环境配置
        env_config = get_environment_config()
        print(f"✅ 环境配置获取成功: {len(env_config)} 个配置项")
        
        return True
        
    except Exception as e:
        print(f"❌ 配置模块测试失败: {str(e)}")
        return False


def test_token_manager():
    """测试Token管理器"""
    print("\n🧪 测试Token管理器...")
    
    try:
        from utils.token_manager import TokenManager, AsyncConfig
        
        # 测试Token管理器
        token_manager = TokenManager(max_tokens=1000)
        
        test_text = "这是一个测试文本。" * 100
        token_info = token_manager.get_token_info(test_text)
        
        print(f"✅ Token估算成功: {token_info['estimated_tokens']} tokens")
        
        # 测试异步配置
        api_count = AsyncConfig.get_available_api_count()
        performance_info = AsyncConfig.get_performance_info()
        
        print(f"✅ 异步配置成功: {api_count} 个API密钥")
        print(f"✅ 性能信息: {performance_info['estimated_speedup']} 加速")
        
        return True
        
    except Exception as e:
        print(f"❌ Token管理器测试失败: {str(e)}")
        return False


def test_content_cleaner():
    """测试内容清理器"""
    print("\n🧪 测试内容清理器...")
    
    try:
        from content.content_cleaner import ContentCleaner
        
        cleaner = ContentCleaner()
        
        # 测试内容清理
        dirty_content = """
        好的，遵照您的要求，我来生成内容。
        
        这是实际的有用内容。
        
        优化前版本：旧内容
        优化后版本：新内容
        
        这是另一段有用的内容。
        
        思考过程：我在思考如何写这段内容...
        """
        
        cleaned_content = cleaner.clean_content_thoroughly(dirty_content)
        
        print(f"✅ 内容清理成功")
        print(f"   原始长度: {len(dirty_content)} 字符")
        print(f"   清理后长度: {len(cleaned_content)} 字符")
        print(f"   清理比例: {(1 - len(cleaned_content)/len(dirty_content))*100:.1f}%")
        
        return True
        
    except Exception as e:
        print(f"❌ 内容清理器测试失败: {str(e)}")
        return False


def test_search_trigger():
    """测试搜索触发器"""
    print("\n🧪 测试搜索触发器...")
    
    try:
        from search.search_trigger import SearchTrigger
        from core.generator import CompleteReportGenerator
        
        # 创建一个简单的生成器实例用于测试
        generator = CompleteReportGenerator(use_async=False)
        search_trigger = SearchTrigger(generator)
        
        # 测试内容缺口分析
        test_content = """
        这是一个关于人工智能的报告。
        人工智能技术正在快速发展。
        市场规模不断扩大。
        """
        
        gaps = search_trigger.analyze_content_gaps(test_content, "人工智能")
        
        print(f"✅ 搜索触发器测试成功")
        print(f"   发现 {len(gaps)} 个内容缺口")
        
        for gap in gaps:
            print(f"   - {gap['type']}: {gap['reason']}")
        
        return True
        
    except Exception as e:
        print(f"❌ 搜索触发器测试失败: {str(e)}")
        return False


def test_generator_creation():
    """测试生成器创建"""
    print("\n🧪 测试生成器创建...")
    
    try:
        from core.generator import CompleteReportGenerator
        
        # 测试同步生成器
        sync_generator = CompleteReportGenerator(use_async=False)
        print("✅ 同步生成器创建成功")
        
        # 测试异步生成器
        async_generator = CompleteReportGenerator(use_async=True)
        print("✅ 异步生成器创建成功")
        
        # 测试配置
        print(f"   同步模式API管理器: {type(sync_generator.api_manager).__name__}")
        print(f"   异步模式API管理器: {type(async_generator.api_manager).__name__}")
        
        return True
        
    except Exception as e:
        print(f"❌ 生成器创建测试失败: {str(e)}")
        return False


def main():
    """主测试函数"""
    print("🚀 开始模块化系统测试")
    print("=" * 50)
    
    tests = [
        test_imports,
        test_config,
        test_token_manager,
        test_content_cleaner,
        test_search_trigger,
        test_generator_creation
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"❌ 测试异常: {str(e)}")
    
    print("\n" + "=" * 50)
    print(f"🎯 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！模块化系统工作正常！")
        return True
    else:
        print("⚠️ 部分测试失败，请检查相关模块")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
