"""
质量检查器模块
实现从六级到一级的反向质量检查算法
"""
import time
import re
from typing import Dict, Any, List, Optional, Tuple
from datetime import datetime

from .state import (
    ReportState, QualityCheckPhaseResult, QualityIssue, QualityMetrics,
    QualityDimension, SeverityLevel
)
from .config import Config
from .logger import ProcessLogger
from .llm_client import LLMClient


class IQualityChecker:
    """质量检查器接口"""
    
    def check_report_quality(self, state: ReportState) -> ReportState:
        """检查报告质量"""
        raise NotImplementedError
    
    def check_node_quality(self, node: Dict[str, Any], state: ReportState) -> QualityMetrics:
        """检查节点质量"""
        raise NotImplementedError


class QualityChecker(IQualityChecker):
    """
    质量检查器实现
    从六级→五级→四级→三级→二级→一级的反向检查逻辑
    """
    
    def __init__(self, config: Config, llm_client: LLMClient, logger: ProcessLogger):
        self.config = config
        self.llm_client = llm_client
        self.logger = logger
        self.max_depth = config.report.max_heading_depth
    
    def check_report_quality(self, state: ReportState) -> ReportState:
        """
        检查整个报告的质量
        
        Args:
            state: 报告状态
            
        Returns:
            更新后的报告状态
        """
        self.logger.logger.info("开始反向质量检查")
        start_time = time.time()
        
        try:
            if not state.report_structure or "sections" not in state.report_structure:
                raise ValueError("报告结构未初始化")
            
            sections = state.report_structure["sections"]
            total_issues = 0
            total_nodes = 0
            total_quality_score = 0.0
            
            # 从最深层级开始，逐层向上检查
            for level in range(self.max_depth, 0, -1):
                self.logger.logger.info(f"检查第 {level} 级质量")
                level_issues, level_nodes, level_score = self._check_level_quality(
                    sections, level, state
                )
                total_issues += level_issues
                total_nodes += level_nodes
                total_quality_score += level_score
            
            # 计算平均质量分数
            average_quality = total_quality_score / total_nodes if total_nodes > 0 else 0.0
            
            duration = time.time() - start_time
            self.logger.logger.info(
                f"质量检查完成，耗时: {duration:.2f}秒，"
                f"总问题数: {total_issues}，平均质量分数: {average_quality:.3f}"
            )
            
        except Exception as e:
            self.logger.logger.error(f"质量检查失败: {str(e)}")
            state.add_error(f"质量检查器错误: {str(e)}", "quality_checker")
            raise
        
        return state
    
    def _check_level_quality(
        self, 
        sections: List[Dict[str, Any]], 
        target_level: int, 
        state: ReportState
    ) -> Tuple[int, int, float]:
        """
        检查指定层级的质量
        
        Args:
            sections: 章节列表
            target_level: 目标层级
            state: 报告状态
            
        Returns:
            (问题数量, 节点数量, 总质量分数)
        """
        nodes_at_level = self._collect_nodes_at_level(sections, target_level)
        
        total_issues = 0
        total_score = 0.0
        
        for node, section_index in nodes_at_level:
            quality_metrics = self.check_node_quality(node, state)
            
            # 更新节点的质量指标
            node["quality_metrics"] = quality_metrics.dict()
            
            # 统计问题和分数
            if "quality_issues" in node:
                total_issues += len(node["quality_issues"])
            total_score += quality_metrics.overall_score
        
        return total_issues, len(nodes_at_level), total_score
    
    def check_node_quality(self, node: Dict[str, Any], state: ReportState) -> QualityMetrics:
        """
        检查单个节点的质量
        
        Args:
            node: 节点信息
            state: 报告状态
            
        Returns:
            质量指标
        """
        metrics = QualityMetrics()
        
        try:
            content = node.get("content", "")
            if not content.strip():
                # 空内容直接返回零分
                return metrics
            
            # 检查六个维度的质量
            metrics.coherence_score = self._check_coherence(node, content, state)
            metrics.consistency_score = self._check_consistency(node, content, state)
            metrics.accuracy_score = self._check_accuracy(node, content, state)
            metrics.completeness_score = self._check_completeness(node, content, state)
            metrics.comprehensiveness_score = self._check_comprehensiveness(node, content, state)
            metrics.rigor_score = self._check_rigor(node, content, state)
            
            # 计算总体分数
            metrics.calculate_overall_score()
            
            self.logger.logger.debug(
                f"节点质量检查完成: {node.get('title', 'Unknown')} "
                f"- 总分: {metrics.overall_score:.3f}"
            )
            
        except Exception as e:
            self.logger.logger.error(f"节点质量检查失败: {str(e)}")
            # 发生错误时返回零分
            metrics = QualityMetrics()
        
        return metrics
    
    def _check_coherence(self, node: Dict[str, Any], content: str, state: ReportState) -> float:
        """检查连贯性"""
        try:
            # 基本连贯性检查
            score = 0.5  # 基础分数
            
            # 检查段落结构
            paragraphs = content.split('\n\n')
            if len(paragraphs) >= 2:
                score += 0.2
            
            # 检查逻辑连接词
            connectors = ['因此', '然而', '此外', '同时', '另外', '综上', '总之']
            connector_count = sum(1 for connector in connectors if connector in content)
            score += min(0.3, connector_count * 0.1)
            
            return min(1.0, score)
            
        except Exception as e:
            self.logger.logger.error(f"连贯性检查失败: {str(e)}")
            return 0.0
    
    def _check_consistency(self, node: Dict[str, Any], content: str, state: ReportState) -> float:
        """检查一致性"""
        try:
            score = 0.5  # 基础分数
            
            # 检查术语一致性
            # 这里可以实现更复杂的术语一致性检查
            
            # 检查数据一致性
            numbers = re.findall(r'\d+(?:\.\d+)?', content)
            if numbers:
                score += 0.2
            
            # 检查引用格式一致性
            citations = re.findall(self.config.report.citation_pattern, content)
            if citations:
                score += 0.3
            
            return min(1.0, score)
            
        except Exception as e:
            self.logger.logger.error(f"一致性检查失败: {str(e)}")
            return 0.0
    
    def _check_accuracy(self, node: Dict[str, Any], content: str, state: ReportState) -> float:
        """检查准确性"""
        try:
            score = 0.6  # 基础分数
            
            # 检查是否有引用来源
            citations = re.findall(self.config.report.citation_pattern, content)
            if citations:
                score += 0.4
            
            return min(1.0, score)
            
        except Exception as e:
            self.logger.logger.error(f"准确性检查失败: {str(e)}")
            return 0.0
    
    def _check_completeness(self, node: Dict[str, Any], content: str, state: ReportState) -> float:
        """检查完整性"""
        try:
            score = 0.0
            
            # 检查内容长度
            word_count = len(content)
            if word_count >= 200:
                score += 0.3
            if word_count >= 500:
                score += 0.3
            if word_count >= 800:
                score += 0.4
            
            return min(1.0, score)
            
        except Exception as e:
            self.logger.logger.error(f"完整性检查失败: {str(e)}")
            return 0.0
    
    def _check_comprehensiveness(self, node: Dict[str, Any], content: str, state: ReportState) -> float:
        """检查全面性"""
        try:
            score = 0.5  # 基础分数
            
            # 检查是否涵盖多个方面
            aspects = ['市场', '技术', '政策', '竞争', '风险', '机遇', '趋势', '分析']
            covered_aspects = sum(1 for aspect in aspects if aspect in content)
            score += min(0.5, covered_aspects * 0.1)
            
            return min(1.0, score)
            
        except Exception as e:
            self.logger.logger.error(f"全面性检查失败: {str(e)}")
            return 0.0
    
    def _check_rigor(self, node: Dict[str, Any], content: str, state: ReportState) -> float:
        """检查严谨性"""
        try:
            score = 0.5  # 基础分数
            
            # 检查专业术语使用
            professional_terms = ['分析', '研究', '数据', '报告', '市场', '技术', '发展']
            term_count = sum(1 for term in professional_terms if term in content)
            score += min(0.3, term_count * 0.05)
            
            # 检查数据支撑
            if re.search(r'\d+%|\d+亿|\d+万', content):
                score += 0.2
            
            return min(1.0, score)
            
        except Exception as e:
            self.logger.logger.error(f"严谨性检查失败: {str(e)}")
            return 0.0
    
    def _collect_nodes_at_level(
        self, 
        sections: List[Dict[str, Any]], 
        target_level: int
    ) -> List[Tuple[Dict[str, Any], int]]:
        """收集指定层级的所有节点"""
        nodes = []
        
        def collect_recursive(node: Dict[str, Any], section_idx: int, current_level: int = 1):
            if current_level == target_level:
                nodes.append((node, section_idx))
            elif current_level < target_level and "children" in node:
                for child in node["children"]:
                    collect_recursive(child, section_idx, current_level + 1)
        
        for idx, section in enumerate(sections):
            collect_recursive(section, idx)
        
        return nodes


class CoherenceChecker:
    """连贯性检查器"""
    
    def __init__(self, config: Config, llm_client: LLMClient, logger: ProcessLogger):
        self.config = config
        self.llm_client = llm_client
        self.logger = logger
    
    def check_node_coherence(self, node: Dict[str, Any], state: ReportState) -> float:
        """
        检查节点内容的连贯性
        
        Args:
            node: 节点信息
            state: 报告状态
            
        Returns:
            连贯性分数 (0.0-1.0)
        """
        content = node.get("content", "")
        if not content.strip():
            return 0.0
        
        try:
            # 使用LLM进行深度连贯性分析
            prompt = f"""
请分析以下内容的连贯性，从以下几个方面评估：
1. 逻辑流畅性：内容是否有清晰的逻辑顺序
2. 语言连贯性：句子之间是否自然衔接
3. 主题一致性：是否围绕主题展开

内容：
{content}

请给出0-10分的评分，并简要说明理由。
只返回数字分数。
"""
            
            response = self.llm_client.call_llm(
                prompt=prompt,
                model_type="orchestrator",
                response_format="text"
            )
            
            # 提取分数
            score_match = re.search(r'(\d+(?:\.\d+)?)', response)
            if score_match:
                score = float(score_match.group(1)) / 10.0
                return min(1.0, max(0.0, score))
            
        except Exception as e:
            self.logger.logger.error(f"LLM连贯性检查失败: {str(e)}")
        
        # 回退到基础检查
        return self._basic_coherence_check(content)
    
    def _basic_coherence_check(self, content: str) -> float:
        """基础连贯性检查"""
        score = 0.0
        
        # 检查段落结构
        paragraphs = content.split('\n\n')
        if len(paragraphs) >= 2:
            score += 0.3
        
        # 检查连接词
        connectors = ['因此', '然而', '此外', '同时', '另外', '综上', '总之', '首先', '其次', '最后']
        connector_count = sum(1 for connector in connectors if connector in content)
        score += min(0.4, connector_count * 0.1)
        
        # 检查句子长度分布
        sentences = re.split(r'[。！？]', content)
        if len(sentences) >= 3:
            avg_length = sum(len(s) for s in sentences) / len(sentences)
            if 10 <= avg_length <= 50:  # 合理的句子长度
                score += 0.3
        
        return min(1.0, score)


class ConsistencyChecker:
    """一致性检查器"""
    
    def __init__(self, config: Config, llm_client: LLMClient, logger: ProcessLogger):
        self.config = config
        self.llm_client = llm_client
        self.logger = logger
    
    def check_parent_child_consistency(
        self, 
        parent_node: Dict[str, Any], 
        child_node: Dict[str, Any], 
        state: ReportState
    ) -> float:
        """
        检查父子节点的一致性
        
        Args:
            parent_node: 父节点
            child_node: 子节点
            state: 报告状态
            
        Returns:
            一致性分数 (0.0-1.0)
        """
        parent_content = parent_node.get("content", "")
        child_content = child_node.get("content", "")
        
        if not parent_content.strip() or not child_content.strip():
            return 0.0
        
        try:
            # 使用LLM进行深度一致性分析
            prompt = f"""
请分析父子章节内容的一致性：

父章节标题：{parent_node.get('title', '')}
父章节内容：
{parent_content}

子章节标题：{child_node.get('title', '')}
子章节内容：
{child_content}

请从以下方面评估一致性：
1. 逻辑一致性：子章节是否符合父章节的逻辑框架
2. 事实一致性：数据和观点是否一致
3. 风格一致性：表述风格是否统一

请给出0-10分的评分。
只返回数字分数。
"""
            
            response = self.llm_client.call_llm(
                prompt=prompt,
                model_type="orchestrator",
                response_format="text"
            )
            
            # 提取分数
            score_match = re.search(r'(\d+(?:\.\d+)?)', response)
            if score_match:
                score = float(score_match.group(1)) / 10.0
                return min(1.0, max(0.0, score))
            
        except Exception as e:
            self.logger.logger.error(f"LLM一致性检查失败: {str(e)}")
        
        # 回退到基础检查
        return self._basic_consistency_check(parent_content, child_content)
    
    def _basic_consistency_check(self, parent_content: str, child_content: str) -> float:
        """基础一致性检查"""
        score = 0.5  # 基础分数
        
        # 检查关键词重叠
        parent_words = set(re.findall(r'\w+', parent_content))
        child_words = set(re.findall(r'\w+', child_content))
        
        if parent_words and child_words:
            overlap = len(parent_words & child_words) / len(parent_words | child_words)
            score += overlap * 0.5
        
        return min(1.0, score)
