#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
AI报告生成器主程序
模块化版本的入口文件
"""

import os
import sys
from pathlib import Path
from typing import List, Optional

# 添加当前目录到Python路径
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

from core.generator import CompleteReportGenerator
from core.config import ReportConfig


def get_user_inputs() -> dict:
    """获取用户输入配置"""
    print("🎯 AI报告生成器 - 模块化版本")
    print("=" * 50)
    
    # 获取报告主题
    topic = input("📝 请输入报告主题: ").strip()
    if not topic:
        topic = "人工智能产业发展研究"
        print(f"   使用默认主题: {topic}")
    
    # 获取框架文件路径
    framework_file = input("📋 请输入框架文件路径 (可选，直接回车跳过): ").strip()
    if framework_file and not Path(framework_file).exists():
        print(f"⚠️ 框架文件不存在: {framework_file}")
        framework_file = ""
    
    # 获取数据源路径
    data_source = input("📁 请输入数据源路径 (默认: data): ").strip()
    if not data_source:
        data_source = "data"
    
    # 确保数据源目录存在
    data_path = Path(data_source)
    if not data_path.exists():
        data_path.mkdir(parents=True, exist_ok=True)
        print(f"✅ 已创建数据源目录: {data_source}")
        
        # 创建示例数据文件
        create_sample_data(data_path)
    
    # 获取目标字数
    try:
        target_words = int(input("📊 请输入目标字数 (默认: 50000): ").strip() or "50000")
    except ValueError:
        target_words = 50000
        print(f"   使用默认字数: {target_words}")
    
    # 获取最大层级
    try:
        max_depth = int(input("📚 请输入最大层级深度 (默认: 6): ").strip() or "6")
    except ValueError:
        max_depth = 6
        print(f"   使用默认层级: {max_depth}")
    
    # 选择运行模式
    print("\n🚀 选择运行模式:")
    print("   1. 异步并行模式 (推荐，速度快)")
    print("   2. 同步顺序模式 (稳定，速度慢)")
    
    mode_choice = input("请选择模式 (1/2，默认: 1): ").strip()
    use_async = mode_choice != "2"
    
    # 是否启用搜索增强
    search_choice = input("🔍 是否启用联网搜索增强? (y/n，默认: y): ").strip().lower()
    enable_search = search_choice != "n"
    
    # 是否启用图片嵌入
    image_choice = input("🖼️ 是否启用图片嵌入? (y/n，默认: y): ").strip().lower()
    enable_images = image_choice != "n"
    
    return {
        "topic": topic,
        "framework_file": framework_file if framework_file else None,
        "data_sources": [data_source],
        "target_words": target_words,
        "max_depth": max_depth,
        "use_async": use_async,
        "enable_search": enable_search,
        "enable_images": enable_images
    }


def create_sample_data(data_path: Path):
    """创建示例数据文件"""
    sample_content = """
# 示例数据文件

这是一个示例数据文件，用于演示AI报告生成器的功能。

## 市场数据
- 全球市场规模：1000亿美元
- 年增长率：15%
- 主要参与者：公司A、公司B、公司C

## 技术趋势
- 人工智能技术快速发展
- 云计算普及率提升
- 数据安全重要性增加

## 政策环境
- 政府支持数字化转型
- 相关法规逐步完善
- 标准化工作推进

请将您的实际数据文件放在此目录中，支持的格式包括：
.txt, .md, .docx, .pdf, .xlsx, .json, .xml 等
"""
    
    sample_file = data_path / "sample_data.md"
    with open(sample_file, 'w', encoding='utf-8') as f:
        f.write(sample_content)
    
    print(f"✅ 已创建示例数据文件: {sample_file}")


def validate_data_sources(data_sources: List[str]) -> bool:
    """验证数据源"""
    for data_source in data_sources:
        data_path = Path(data_source)
        if not data_path.exists():
            print(f"❌ 数据源不存在: {data_source}")
            return False
        
        # 检查是否有可读取的文件
        supported_extensions = {'.txt', '.md', '.docx', '.pdf', '.xlsx', '.json', '.xml'}
        has_files = False
        
        if data_path.is_file():
            if data_path.suffix.lower() in supported_extensions:
                has_files = True
        else:
            for file_path in data_path.rglob('*'):
                if file_path.is_file() and file_path.suffix.lower() in supported_extensions:
                    has_files = True
                    break
        
        if not has_files:
            print(f"⚠️ 数据源中没有找到支持的文件格式: {data_source}")
            print(f"   支持的格式: {', '.join(supported_extensions)}")
    
    return True


def main():
    """主程序入口"""
    try:
        # 获取用户配置
        config = get_user_inputs()

        # 验证数据源
        if not validate_data_sources(config["data_sources"]):
            print("❌ 数据源验证失败")
            return

        # 显示配置摘要
        print("\n📋 配置摘要:")
        print(f"   报告主题: {config['topic']}")
        print(f"   数据源: {', '.join(config['data_sources'])}")
        print(f"   目标字数: {config['target_words']:,}")
        print(f"   最大层级: {config['max_depth']}")
        print(f"   运行模式: {'异步并行' if config['use_async'] else '同步顺序'}")
        print(f"   搜索增强: {'启用' if config['enable_search'] else '禁用'}")
        print(f"   图片嵌入: {'启用' if config['enable_images'] else '禁用'}")

        # 确认开始生成
        confirm = input("\n🚀 确认开始生成报告? (y/n): ").strip().lower()
        if confirm != 'y':
            print("❌ 用户取消生成")
            return

        # 创建报告生成器
        generator = CompleteReportGenerator(
            use_async=config["use_async"],
            max_tokens=250000
        )

        # 更新生成器配置
        generator.report_config.update({
            "target_words": config["target_words"],
            "max_depth": config["max_depth"],
            "enable_search": config["enable_search"],
            "enable_images": config["enable_images"]
        })

        # 开始生成报告
        print("\n" + "="*50)
        print("🚀 开始生成报告...")
        print("="*50)

        output_path = generator.generate_report(
            topic=config["topic"],
            data_sources=config["data_sources"],
            framework_file_path=config["framework_file"]
        )

        # 搜索增强
        if config["enable_search"]:
            print("\n🔍 开始搜索增强...")
            output_path = generator.enhance_report_with_search(
                output_path, config["topic"], user_confirm=False
            )

        # 图片嵌入
        if config["enable_images"]:
            print("\n🖼️ 开始图片嵌入...")
            output_path = generator.embed_images_in_report(
                output_path, config["data_sources"], config["topic"], auto_confirm=True
            )

        print("\n" + "="*50)
        print("🎉 报告生成完成!")
        print(f"📄 输出文件: {output_path}")
        print("="*50)

    except KeyboardInterrupt:
        print("\n❌ 用户中断程序")
    except Exception as e:
        print(f"\n❌ 程序执行失败: {str(e)}")
        import traceback
        traceback.print_exc()


def create_directories():
    """创建必要的目录"""
    directories = ["data", "templates", "output", "logs", "checkpoints"]
    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)


def create_framework_file(primary_sections=8):
    """创建框架文件"""
    framework_dir = Path("templates")
    framework_dir.mkdir(exist_ok=True)

    framework_path = framework_dir / f"framework_{primary_sections}sections.md"

    # 定义标题模板
    section_templates = [
        ("市场概览与现状分析", "市场规模与增长趋势", "全球市场规模", "市场总量分析", "历史数据回顾"),
        ("技术发展与创新趋势", "核心技术分析", "技术创新动态", "研发投入情况", "技术路线图"),
        ("产业链分析", "上游供应链", "中游制造环节", "下游应用市场", "价值链分布"),
        ("竞争格局与主要企业", "市场竞争态势", "主要企业分析", "竞争优势对比", "市场份额分析"),
        ("政策环境与法规标准", "政策支持体系", "法规标准现状", "监管政策影响", "政策趋势预测"),
        ("投资与融资分析", "投资规模统计", "融资事件分析", "投资热点领域", "资本市场表现"),
        ("发展趋势与前景预测", "短期发展趋势", "中长期前景", "市场机遇分析", "风险挑战评估"),
        ("结论与建议", "主要结论总结", "发展建议", "投资建议", "政策建议")
    ]

    # 生成框架内容
    framework_content = f"# 产业研究报告框架 ({primary_sections}个一级标题)\n\n"

    for i in range(primary_sections):
        if i < len(section_templates):
            main_title, sub1, sub2, sub3, sub4 = section_templates[i]
        else:
            main_title = f"专题分析{i-len(section_templates)+1}"
            sub1, sub2, sub3, sub4 = "分析要点1", "分析要点2", "分析要点3", "分析要点4"

        framework_content += f"## {i+1}. {main_title}\n\n"
        framework_content += f"### {i+1}.1 {sub1}\n"
        framework_content += f"### {i+1}.2 {sub2}\n"
        framework_content += f"### {i+1}.3 {sub3}\n"
        framework_content += f"### {i+1}.4 {sub4}\n\n"

    with open(framework_path, 'w', encoding='utf-8') as f:
        f.write(framework_content)

    print(f"✅ 创建框架文件: {framework_path} ({primary_sections}个一级标题)")
    return str(framework_path)


def manage_checkpoints():
    """Checkpoint管理工具"""
    print("📂 Checkpoint管理工具")
    print("=" * 50)

    try:
        from utils.checkpoint_manager import CheckpointManager
        checkpoint_manager = CheckpointManager()

        while True:
            print("\n选择操作:")
            print("1. 列出所有checkpoint")
            print("2. 删除checkpoint")
            print("3. 清理旧checkpoint")
            print("4. 退出")

            choice = input("\n请选择 (1-4): ").strip()

            if choice == '1':
                checkpoints = checkpoint_manager.list_checkpoints()
                if checkpoints:
                    print(f"\n📋 找到 {len(checkpoints)} 个checkpoint:")
                    for i, cp in enumerate(checkpoints, 1):
                        print(f"   {i}. {cp['checkpoint_id']} - {cp['step']} ({cp['timestamp']})")
                else:
                    print("\n📋 没有找到checkpoint")

            elif choice == '2':
                checkpoint_id = input("请输入要删除的checkpoint ID: ").strip()
                if checkpoint_id:
                    if checkpoint_manager.delete_checkpoint(checkpoint_id):
                        print("✅ Checkpoint删除成功")
                    else:
                        print("❌ Checkpoint删除失败")

            elif choice == '3':
                keep_count = input("保留最新的几个checkpoint (默认10): ").strip()
                keep_count = int(keep_count) if keep_count.isdigit() else 10
                deleted = checkpoint_manager.cleanup_old_checkpoints(keep_count)
                print(f"✅ 清理完成，删除了 {deleted} 个旧checkpoint")

            elif choice == '4':
                break

            else:
                print("❌ 无效选择")

    except Exception as e:
        print(f"❌ Checkpoint管理失败: {str(e)}")


def interactive_mode():
    """交互式模式"""
    print("🤖 AI报告生成器 - 交互式模式")
    print("=" * 60)

    while True:
        print("\n选择功能:")
        print("1. 生成报告")
        print("2. 管理Checkpoint")
        print("3. 创建框架文件")
        print("4. 创建示例数据")
        print("5. 退出")

        choice = input("\n请选择 (1-5): ").strip()

        if choice == '1':
            main()
        elif choice == '2':
            manage_checkpoints()
        elif choice == '3':
            sections = input("请输入一级标题数量 (默认8): ").strip()
            sections = int(sections) if sections.isdigit() else 8
            create_framework_file(sections)
        elif choice == '4':
            create_sample_data()
        elif choice == '5':
            print("👋 再见!")
            break
        else:
            print("❌ 无效选择")


if __name__ == "__main__":
    # 创建必要的目录
    create_directories()

    # 检查命令行参数
    if len(sys.argv) > 1:
        if sys.argv[1] == "--interactive":
            interactive_mode()
        elif sys.argv[1] == "--checkpoint":
            manage_checkpoints()
        elif sys.argv[1] == "--create-framework":
            sections = int(sys.argv[2]) if len(sys.argv) > 2 and sys.argv[2].isdigit() else 8
            create_framework_file(sections)
        elif sys.argv[1] == "--create-data":
            create_sample_data()
        else:
            print("❌ 未知参数")
            print("可用参数:")
            print("  --interactive    交互式模式")
            print("  --checkpoint     管理checkpoint")
            print("  --create-framework [数量]  创建框架文件")
            print("  --create-data    创建示例数据")
    else:
        # 默认运行主程序
        main()
