"""
测试异步API优化效果
验证异步模式的稳定性和性能
"""
import asyncio
import time
import sys
from pathlib import Path

def test_async_api_manager():
    """测试异步API管理器"""
    print("🧪 测试异步API管理器")
    print("=" * 60)
    
    try:
        from complete_report_generator import CompleteReportGenerator
        
        # 创建生成器
        generator = CompleteReportGenerator(use_async=True)
        
        # 检查API管理器初始化
        if hasattr(generator, 'api_manager') and generator.use_async:
            manager = generator.api_manager
            print(f"✅ 异步API管理器初始化成功")
            print(f"   可用API密钥: {manager.total_keys} 个")
            print(f"   最大并发数: {manager.total_keys}")

            # 测试API配置获取
            config = manager._get_available_api_config()
            if config:
                print(f"✅ API配置获取成功: {config['api_name']}")
            else:
                print("⚠️ 暂时无可用API配置")

            return True
        else:
            print("❌ 异步API管理器未初始化")
            return False
            
    except Exception as e:
        print(f"❌ 异步API管理器测试失败: {str(e)}")
        return False

async def test_async_content_generation():
    """测试异步内容生成"""
    print("\n🧪 测试异步内容生成")
    print("=" * 60)
    
    try:
        from complete_report_generator import CompleteReportGenerator
        
        generator = CompleteReportGenerator(use_async=True)
        
        if not (hasattr(generator, 'api_manager') and generator.use_async):
            print("❌ 异步API管理器未初始化")
            return False
        
        # 测试简单的内容生成
        test_prompt = "请简要介绍人工智能的发展历程。"
        
        print(f"📝 测试prompt: {test_prompt}")
        print(f"🚀 开始异步内容生成...")
        
        start_time = time.time()
        
        try:
            response, api_index = await generator.api_manager.generate_content_with_model_async(
                test_prompt, "gemini-2.5-flash"
            )
            
            end_time = time.time()
            duration = end_time - start_time
            
            if response and hasattr(response, 'text'):
                content = response.text
                print(f"✅ 异步内容生成成功")
                print(f"   使用API: {api_index + 1}")
                print(f"   生成时间: {duration:.2f} 秒")
                print(f"   内容长度: {len(content)} 字符")
                print(f"   内容预览: {content[:100]}...")
                return True
            else:
                print("❌ 响应格式异常")
                return False
                
        except Exception as e:
            print(f"❌ 异步内容生成失败: {str(e)}")
            return False
            
    except Exception as e:
        print(f"❌ 异步内容生成测试失败: {str(e)}")
        return False

async def test_batch_processing():
    """测试批处理功能"""
    print("\n🧪 测试批处理功能")
    print("=" * 60)
    
    try:
        from complete_report_generator import CompleteReportGenerator
        
        generator = CompleteReportGenerator(use_async=True)
        
        if not (hasattr(generator, 'api_manager') and generator.use_async):
            print("❌ 异步API管理器未初始化")
            return False
        
        # 创建测试任务
        test_prompts = [
            "介绍人工智能的基本概念",
            "解释机器学习的原理",
            "描述深度学习的应用",
            "分析自然语言处理的发展",
            "讨论计算机视觉的技术"
        ]
        
        print(f"📋 创建 {len(test_prompts)} 个测试任务")
        
        # 创建异步任务
        async def generate_content(prompt):
            return await generator.api_manager.generate_content_with_model_async(
                prompt, "gemini-2.5-flash"
            )
        
        tasks = [generate_content(prompt) for prompt in test_prompts]
        
        print(f"🚀 开始批处理测试...")
        start_time = time.time()
        
        # 使用优化的批处理方法
        await generator._execute_tasks_in_batches(tasks, 3, "内容生成测试")
        
        end_time = time.time()
        duration = end_time - start_time
        
        print(f"✅ 批处理测试完成")
        print(f"   总时间: {duration:.2f} 秒")
        print(f"   平均每任务: {duration/len(test_prompts):.2f} 秒")
        
        return True
        
    except Exception as e:
        print(f"❌ 批处理测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_config_optimization():
    """测试配置优化"""
    print("\n🧪 测试配置优化")
    print("=" * 60)
    
    try:
        from complete_report_generator import AsyncConfig
        
        # 测试API数量获取
        api_count = AsyncConfig.get_available_api_count()
        print(f"📊 可用API数量: {api_count}")
        
        # 测试并发数计算
        max_concurrent = AsyncConfig.get_max_concurrent_requests()
        print(f"📊 最大并发数: {max_concurrent}")
        
        # 测试批次大小计算
        test_task_counts = [5, 15, 25, 50]
        for task_count in test_task_counts:
            batch_size = AsyncConfig.calculate_optimal_batch_size(task_count)
            print(f"📊 {task_count} 个任务 → 批次大小: {batch_size}")
        
        print(f"✅ 配置优化测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 配置优化测试失败: {str(e)}")
        return False

async def main():
    """主测试函数"""
    print("🚀 异步API优化测试")
    print("=" * 60)
    
    tests = [
        ("异步API管理器", test_async_api_manager, False),  # 同步测试
        ("异步内容生成", test_async_content_generation, True),  # 异步测试
        ("批处理功能", test_batch_processing, True),
        ("配置优化", test_config_optimization, False)
    ]
    
    results = []
    
    for test_name, test_func, is_async in tests:
        try:
            print(f"\n{'='*60}")
            
            if is_async:
                result = await test_func()
            else:
                result = test_func()
            
            results.append((test_name, result))
            
            if result:
                print(f"✅ {test_name}: 测试通过")
            else:
                print(f"❌ {test_name}: 测试失败")
                
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {str(e)}")
            results.append((test_name, False))
    
    # 总结
    print(f"\n📊 异步优化测试总结:")
    print("=" * 60)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {test_name}: {status}")
    
    print(f"\n🎯 总体结果: {passed}/{total} 个测试通过")
    
    if passed == total:
        print(f"\n🎉 异步API优化测试全部通过！")
        print(f"\n💡 优化效果:")
        print(f"   1. ✅ 使用线程锁避免异步锁死锁")
        print(f"   2. ✅ 改进的API配置获取逻辑")
        print(f"   3. ✅ 增强的错误处理和重试机制")
        print(f"   4. ✅ 优化的批处理执行逻辑")
        print(f"   5. ✅ 智能的错误状态管理")
        print(f"   6. ✅ 更好的并发控制和稳定性")
    else:
        print(f"\n💡 如果测试失败，请检查:")
        print(f"   1. API密钥配置是否正确")
        print(f"   2. 网络连接是否稳定")
        print(f"   3. 是否有API配额限制")
    
    return passed == total

if __name__ == "__main__":
    try:
        success = asyncio.run(main())
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\n❌ 用户中断测试")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 测试程序错误: {str(e)}")
        sys.exit(1)
